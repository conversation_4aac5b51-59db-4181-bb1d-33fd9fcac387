# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependency directories
node_modules/
.jazzy/
vendor/

# React Native cache
.cache/
# Metro cache
metro-cache/
# Jest cache
jest/
# Build directories
build/

# Expo files
.expo/
.expo-shared/

# iOS
ios/Pods/
ios/build/
ios/DerivedData/
ios/*.xcodeproj/project.xcworkspace/
ios/*.xcodeproj/xcuserdata/
ios/*.xcodeproj/xcshareddata/
ios/*/build/
ios/*/DerivedData/
ios/*.xcworkspace/
ios/.last_build_state/
ios/*/xcuserdata/
ios/.hmap

# Android
android/.gradle/
android/app/build/
android/local.properties
android/.idea/
android/*.iml
android/*.keystore

# fastlane
*/fastlane/report.xml
*/fastlane/Preview.html
*/fastlane/screenshots/**/*.png
*/fastlane/test_output

# macOS
*.DS_Store

# Package files
*.tgz

# Env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/
.idea/
*.sublime-workspace

## Xcode
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local

## Android/IntelliJ
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore

# Others
*.swp
*~
