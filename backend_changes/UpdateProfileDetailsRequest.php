<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProfileDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $userId = $this->user()->id;

        return [
            'first_name' => ['sometimes', 'required', 'string', 'max:255'],
            'last_name' => ['sometimes', 'required', 'string', 'max:255'],
            'birthday' => ['sometimes', 'date'],
            'phone' => [
                'sometimes',
                'string',
                'regex:/^(0|\+84)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])[0-9]{7}$/',
                Rule::unique('users', 'phone')->ignore($userId),
            ],
            'address' => ['sometimes', 'string', 'max:500'],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'first_name.required' => '<PERSON><PERSON> là bắt buộc.',
            'first_name.max' => 'Họ không được vượt quá 255 ký tự.',
            'last_name.required' => 'Tên là bắt buộc.',
            'last_name.max' => 'Tên không được vượt quá 255 ký tự.',
            'phone.unique' => 'Số điện thoại đã được sử dụng.',
            'phone.regex' => 'Số điện thoại không đúng định dạng.',
            'birthday.date' => 'Ngày sinh không đúng định dạng.',
            'address.max' => 'Địa chỉ không được vượt quá 500 ký tự.',
        ];
    }
}
