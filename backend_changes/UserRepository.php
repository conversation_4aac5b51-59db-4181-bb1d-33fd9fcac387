<?php

namespace App\Repositories;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserRepository
{
    /**
     * Update user by ID
     */
    public function update(int $userId, array $data): User
    {
        return DB::transaction(function () use ($userId, $data) {
            $user = User::findOrFail($userId);
            
            // Log name changes for audit purposes
            if (isset($data['first_name']) || isset($data['last_name'])) {
                Log::info('User name updated', [
                    'user_id' => $userId,
                    'old_first_name' => $user->first_name,
                    'new_first_name' => $data['first_name'] ?? $user->first_name,
                    'old_last_name' => $user->last_name,
                    'new_last_name' => $data['last_name'] ?? $user->last_name,
                    'updated_by' => auth()->id() ?? 'system'
                ]);
            }

            // Update the user
            $user->update($data);
            
            // Refresh the model to get updated data
            $user->refresh();
            
            return $user;
        });
    }

    /**
     * Check if phone exists (excluding current user)
     */
    public function phoneExists(string $phone, ?int $excludeUserId = null): bool
    {
        $query = User::where('phone', $phone);

        if ($excludeUserId) {
            $query->where('id', '!=', $excludeUserId);
        }

        return $query->exists();
    }

    /**
     * Get user profile with related data
     */
    public function getProfile(int $userId): User
    {
        return User::with(['branch', 'roles', 'permissions'])
                   ->findOrFail($userId);
    }
}
