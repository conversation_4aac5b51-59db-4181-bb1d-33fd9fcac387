<?php

namespace App\Http\Controllers;

use App\Http\Requests\UpdateProfileDetailsRequest;
use App\Http\Resources\UserResource;
use App\Repositories\UserRepository;
use App\Events\UpdatedProfileDetails;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class ProfileController extends Controller
{
    /**
     * Update user profile details
     */
    public function update(UpdateProfileDetailsRequest $request, UserRepository $users): JsonResponse
    {
        try {
            $user = $request->user();

            $data = collect($request->validated());

            // Only include fields that are present in the request
            $allowedFields = [
                'first_name', 'last_name', 'birthday',
                'phone', 'address'
            ];

            $data = $data->only($allowedFields)->toArray();

            // Log the update attempt for security purposes
            Log::info('User profile update attempt', [
                'user_id' => $user->id,
                'fields' => array_keys($data),
                'name_changed' => isset($data['first_name']) || isset($data['last_name'])
            ]);

            // Check if name is being changed and log it
            if (isset($data['first_name']) || isset($data['last_name'])) {
                Log::info('User name change', [
                    'user_id' => $user->id,
                    'old_first_name' => $user->first_name,
                    'new_first_name' => $data['first_name'] ?? $user->first_name,
                    'old_last_name' => $user->last_name,
                    'new_last_name' => $data['last_name'] ?? $user->last_name
                ]);
            }

            $user = $users->update($user->id, $data);

            event(new UpdatedProfileDetails($user));

            return response()->json([
                'success' => true,
                'message' => 'Cập nhật thông tin thành công',
                'data' => new UserResource($user)
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Profile update failed', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật thông tin'
            ], 500);
        }
    }
}
