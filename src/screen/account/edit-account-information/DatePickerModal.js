import React, {useEffect, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import ReactNativeModal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useDispatch} from 'react-redux';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import DatePicker from 'react-native-date-picker';
import moment from 'moment';
import Color from '../../../components/theme/Color';

const DatePickerModal = ({isVisible, setIsVisible, value, setValue}) => {
  const insets = useSafeAreaInsets();
  const dispatch = useDispatch();
  const [tempValue, setTempValue] = useState(new Date());

  useEffect(() => {
    if (isVisible) {
      setTempValue(value);
    }
  }, [isVisible]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      zIndex: 90,
      alignItems: 'center',
      justifyContent: 'center',
    },
    content: {
      backgroundColor: 'white',
      padding: 20,
      borderTopLeftRadius: 12,
      borderTopRightRadius: 12,
      width: '100%',
      paddingBottom: insets.bottom + 20,
    },
    title: {
      fontSize: 20,
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
      lineHeight: 30,
      marginBottom: 20,
    },
    space: {
      flex: 1,
      width: '100%',
    },
    modal: {
      width: '100%',
      padding: 0,
      margin: 0,
    },
    dpContainer: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    btnArea: {
      marginVertical: 10,
    },
  });

  const onAccept = () => {
    setValue(moment(tempValue).format('DD/MM/YYYY'));
    setIsVisible(false);
  };

  return (
    <ReactNativeModal
      animationIn={'fadeInUp'}
      animationOut={'fadeOutDown'}
      style={styles.modal}
      isVisible={isVisible}
      onSwipeComplete={() => {
        setIsVisible(false);
      }}
      swipeDirection="down">
      <View style={styles.container}>
        <TouchableOpacity
          style={styles.space}
          onPress={() => setIsVisible(false)}
        />
        <View style={styles.content}>
          <Sys.Text style={styles.title}>Chọn ngày sinh</Sys.Text>
          <View style={styles.dpContainer}>
            <DatePicker
              mode="date"
              onDateChange={date => {
                setTempValue(date);
              }}
              textColor={Color.text}
              date={tempValue}
            />
          </View>
          <View style={styles.btnArea}>
            <Sys.Button onPress={onAccept}>Xác nhận</Sys.Button>
          </View>
        </View>
      </View>
    </ReactNativeModal>
  );
};

export default DatePickerModal;
