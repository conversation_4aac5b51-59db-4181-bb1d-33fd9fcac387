import moment from 'moment';
import React, {useCallback, useEffect, useState} from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import {AccountActions, AccountSelectors} from '../services/account.slice';
import DatePickerModal from './DatePickerModal';

const EditAccountInformationScreen = () => {
  const dispatch = useDispatch();
  const profile = useSelector(AccountSelectors.profile);
  const [phone, setPhone] = useState('');
  const [birthday, setBirthday] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [isShowDatePicker, setIsShowDatePicker] = useState(false);
  const {isStaff, isGuest, isPos, isBranchOwner, isBranchManager} = useSelector(
    state => state.account,
  );
  useEffect(() => {
    setBirthday(
      profile?.birthday
        ? moment(profile?.birthday, 'YYYY-MM-DD').format('DD/MM/YYYY')
        : '',
    );
    setPhone(profile?.phone);
    setFirstName(profile?.first_name || '');
    setLastName(profile?.last_name || '');
  }, [profile]);

  const styles = StyleSheet.create({
    container: {
      margin: 20,
    },
    avatar: {
      width: 120,
      height: 120,
      borderRadius: 60,
    },
    avatarArea: {
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    nameArea: {
      marginTop: 28,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
    },
    name: {
      fontSize: 18,
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
      color: '#001451',
      lineHeight: 28,
    },
    code: {
      lineHeight: 24,
    },
    btnArea: {
      marginTop: 30,
    },
    camera: {
      position: 'absolute',
      bottom: 0,
      width: 35,
      height: 35,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#F1F1F1',
      borderRadius: 50,
      right: 0,
    },
  });

  const onSave = useCallback(() => {
    dispatch(
      AccountActions.saveProfile({
        birthday,
        phone,
        first_name: firstName,
        last_name: lastName,
      }),
    );
  }, [phone, birthday, firstName, lastName]);

  return (
    <Sys.Container hasHeader>
      <DatePickerModal
        value={birthday ? moment(birthday, 'DD/MM/YYYY').toDate() : new Date()}
        setValue={setBirthday}
        isVisible={isShowDatePicker}
        setIsVisible={setIsShowDatePicker}
      />
      <Sys.Header title={'Sửa thông tin cá nhân'} />
      <View style={styles.container}>
        <View style={styles.avatarArea}>
          <View>
            <Image
              style={styles.avatar}
              source={{
                uri: profile?.avatar,
              }}
            />
          </View>
        </View>
        <View style={styles.nameArea}>
          <Sys.Text style={styles.name}>{profile?.name}</Sys.Text>
          {!isGuest ? (
            <Sys.Text style={styles.code}>
              Mã nhân viên: {`${profile?.code || ''}`}
            </Sys.Text>
          ) : (
            <View />
          )}
        </View>
        <View>
          <OneItem
            item={{
              label: 'Họ:',
              value: firstName,
              onChangeText: e => {
                setFirstName(e);
              },
            }}
          />
          <OneItem
            item={{
              label: 'Tên:',
              value: lastName,
              onChangeText: e => {
                setLastName(e);
              },
            }}
          />
          <OneItem
            item={{
              label: 'Số điện thoại:',
              value: phone,
              onChangeText: e => {
                setPhone(e);
              },
            }}
          />
          <OneItem
            isSelect
            onPress={() => {
              setIsShowDatePicker(true);
            }}
            item={{
              label: 'Ngày sinh:',
              value: birthday ? birthday : undefined,
              onChangeText: e => {
                setBirthday(e);
              },
            }}
          />
          {!isGuest ? (
            <OneItem
              disabled
              item={{label: 'Phòng ban:', value: profile?.branch?.code}}
            />
          ) : (
            <View />
          )}
        </View>
        <View style={styles.btnArea}>
          <Sys.Button onPress={onSave}>Cập nhật thông tin</Sys.Button>
        </View>
      </View>
    </Sys.Container>
  );
};

export default EditAccountInformationScreen;

const OneItem = ({item, isSelect = false, onPress, disabled = false}) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 20,
      alignItems: 'center',
    },
    label: {
      color: '#848484',
    },
    selectContain: {
      borderWidth: 1,
      backgroundColor: 'white',
      flex: 2,
      borderRadius: 8,
      borderColor: '#D1D1D1',
      paddingVertical: 10,
    },
    selectText: {
      paddingHorizontal: 20,
    },
  });

  return (
    <View style={styles.container}>
      <View
        style={{
          flex: 1,
        }}>
        <Sys.Text style={styles.label}>{item.label}</Sys.Text>
      </View>
      {isSelect ? (
        <TouchableOpacity
          activeOpacity={0.9}
          onPress={onPress}
          style={styles.selectContain}>
          <Sys.Text style={styles.selectText}>{item.value}</Sys.Text>
        </TouchableOpacity>
      ) : (
        <View
          style={{
            flex: 2,
          }}>
          <Sys.Input
            editable={!disabled}
            value={item.value}
            onChangeText={item.onChangeText}
          />
        </View>
      )}
    </View>
  );
};
