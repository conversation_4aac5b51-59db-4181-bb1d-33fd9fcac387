import moment from 'moment';
import Toast from 'react-native-toast-message';
import {delay, put, takeEvery} from 'redux-saga/effects';
import SysFetch from '../../../services/fetch';
import {AccountActions} from './account.slice';
import StorageService from '../../../services/storageService';

function* AccountSaga() {
  yield takeEvery(AccountActions.getProfile, getProfile);
  yield takeEvery(AccountActions.saveProfile, saveProfile);
  yield takeEvery(AccountActions.deleteAccount, deleteAccount);
  yield takeEvery(AccountActions.changePassword, changePassword);
}

export default AccountSaga;

function* deleteAccount({payload}) {
  try {
    const rs = yield SysFetch.post('me/delete-account', payload);

    payload.onSuccess && payload.onSuccess();
  } catch (error) {
    console.log({error});
  }
}

function* saveProfile({payload}) {
  try {
    // handle account by password
    const requestData = {
      ...payload,
      birthday: payload?.birthday ? moment.utc(payload?.birthday, 'DD/MM/YYYY') : undefined,
    };

    // Remove undefined values
    Object.keys(requestData).forEach(key => {
      if (requestData[key] === undefined) {
        delete requestData[key];
      }
    });

    const rs = yield SysFetch.patch('me/details', requestData);
    if (rs?.data) {
      yield getProfile();
      Toast.show({
        type: 'success',
        text1: 'Thông báo',
        text2: 'Cập nhật thông tin thành công',
      });
    }
  } catch (error) {
    console.log({error});
    Toast.show({
      type: 'error',
      text1: 'Lỗi',
      text2: error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật thông tin',
    });
  }
}

function* changePassword({payload}) {
  try {
    const {onSuccess, ...requestData} = payload;
    const rs = yield SysFetch.post('change-password', requestData);

    // If we get here without error, the request was successful
    Toast.show({
      type: 'success',
      text1: 'Thông báo',
      text2: rs?.message || 'Đổi mật khẩu thành công',
    });

    // Call success callback if provided
    if (onSuccess) {
      onSuccess();
    }
  } catch (error) {
    console.log({error});
    Toast.show({
      type: 'error',
      text1: 'Lỗi',
      text2: error?.response?.data?.message || 'Có lỗi xảy ra khi đổi mật khẩu',
    });
  }
}

function* getProfile() {
  try {
    yield delay(100);
    const rs = yield SysFetch.get('me');
    if (rs?.data) {
      yield put(AccountActions.setProfile(rs.data));
      yield StorageService.setUserDName(rs?.data?.name);
      yield StorageService.setUserAvatar(rs?.data?.avatar);
      yield put(AccountActions.setAvatar(rs?.data?.avatar));
    }
  } catch (error) {
    console.log({error});
  }
}
