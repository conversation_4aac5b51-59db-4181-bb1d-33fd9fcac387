import {createSlice} from '@reduxjs/toolkit';
import moment from 'moment';
import StorageService from '../../../services/storageService';

const initialState = {
  profile: undefined,
  avatar: '',
  username: '',
  view: '',
  isGuest: false,
  isPos: false,
  isAgent: false,
  isStaff: false,
  isBranchOwner: false,
  isBranchManager: false,
  globalFilter: {
    date: moment().format('YYYY-MM-DD'),
    branch: [],
  },
};

const reducers = {
  setGlobalFilter: (state, {payload}) => {
    state.globalFilter = {...state.globalFilter, ...payload};
    StorageService.setFilter({
      branch: state.globalFilter?.branch,
    });
  },
  resetGlobalFilter: (state, {payload}) => {
    state.globalFilter = initialState.globalFilter;
    StorageService.setFilter({
      branch: [],
    });
  },
  getProfile: () => {},
  deleteAccount: (state, {payload}) => {},
  saveProfile: () => {},
  changePassword: () => {},
  setProfile: (state, {payload}) => {
    state.profile = payload;
    state.avatar = payload?.avatar;
    StorageService.setUserDName(payload?.name);
    StorageService.setUserAvatar(payload?.avatar);
  },
  updateProfile: (state, action) => {
    state.profile = {
      ...state.profile,
      ...action.payload,
    };
  },
  setView: (state, {payload}) => {
    state.view = payload;
    state.isGuest = false;
    state.isPos = false;
    state.isAgent = false;
    state.isStaff = false;
    state.isBranchManager = false;
    state.isBranchOwner = false;

    switch (payload) {
      case 'ROLE_GUEST':
      case 'Guest':
        state.view = 'Guest';
        state.isGuest = true;
        break;
      case 'pos.owner':
        state.isPos = true;
        break;
      case 'pos.agent':
        state.isAgent = true;
        break;
      case 'pos.manager':
        state.isStaff = true;
        break;
      case 'branch.owner':
        state.isBranchOwner = true;
        break;
      case 'branch.manager':
        state.isBranchManager = true;
        break;
      default:
        break;
    }
  },

  setAvatar: (state, {payload}) => {
    state.avatar = payload;
  },
  setUsername: (state, {payload}) => {
    state.username = payload;
  },
};

const AccountSlice = createSlice({
  name: 'account',
  initialState,
  reducers,
});

const AccountReducers = AccountSlice.reducer;

export default AccountReducers;

export const AccountActions = AccountSlice.actions;

export const AccountSelectors = {
  profile: state => state.account.profile,
  avatar: state => state.account.avatar,
  username: state => state.account.username,
  view: state => state.account.view,
  isGuest: state => state.account.isGuest,
  isPos: state => state.account.isPos,
  isAgent: state => state.account.isAgent,
  isStaff: state => state.account.isStaff,
  isBranchOwner: state => state.account.isBranchOwner,
  isBranchManager: state => state.account.isBranchManager,
  globalFilter: state => state.account.globalFilter,
};
