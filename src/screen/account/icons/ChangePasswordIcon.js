import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

function ChangePasswordIcon(props) {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={28}
      height={22}
      viewBox="0 0 28 22"
      fill="none"
      {...props}>
      <Path
        d="M0 0v11h28V0H0zm1.867 1.833h24.266v7.334H1.867V1.833zM5.6 3.667c-1.032 0-1.867.82-1.867 1.833S4.568 7.333 5.6 7.333s1.867-.82 1.867-1.833S6.632 3.667 5.6 3.667zm5.6 0c-1.032 0-1.867.82-1.867 1.833s.835 1.833 1.867 1.833 1.867-.82 1.867-1.833-.835-1.833-1.867-1.833zm5.6 0c-1.032 0-1.867.82-1.867 1.833s.835 1.833 1.867 1.833 1.867-.82 1.867-1.833-.835-1.833-1.867-1.833zm5.6 0c-1.032 0-1.867.82-1.867 1.833s.835 1.833 1.867 1.833 1.867-.82 1.867-1.833-.835-1.833-1.867-1.833zM14 12.833c-2.574 0-4.667 2.056-4.667 4.584C9.333 19.945 11.426 22 14 22a4.654 4.654 0 004.258-2.75h-2.187a2.805 2.805 0 01-2.071.917c-1.542 0-2.8-1.236-2.8-2.75 0-1.515 1.258-2.75 2.8-2.75.824 0 1.557.358 2.07.916H14v1.834h4.667v-4.584H16.8v.946a4.633 4.633 0 00-2.8-.946z"
        fill="#F5000D"
      />
    </Svg>
  );
}

export default ChangePasswordIcon;
