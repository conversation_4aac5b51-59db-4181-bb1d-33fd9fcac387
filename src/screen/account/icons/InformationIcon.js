import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

function InformationIcon(props) {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={21}
      height={21}
      viewBox="0 0 21 21"
      fill="none"
      {...props}>
      <Path
        d="M6.125 0v6.125c.608 0 1.196.079 1.75.246V1.75h7v4.375h4.375V17.5h-6.098c.277.55.517 1.135.657 1.75H21V5.332L15.668 0H6.125zm0 7.875A4.388 4.388 0 001.75 12.25c0 1.326.619 2.505 1.559 3.309A6.136 6.136 0 000 21h1.75a4.362 4.362 0 014.375-4.375A4.362 4.362 0 0110.5 21h1.75c0-2.362-1.34-4.42-3.309-5.441.94-.804 1.559-1.983 1.559-3.309a4.388 4.388 0 00-4.375-4.375zm0 1.75A2.613 2.613 0 018.75 12.25a2.613 2.613 0 01-2.625 2.625A2.613 2.613 0 013.5 12.25a2.613 2.613 0 012.625-2.625z"
        fill="#F5000D"
      />
    </Svg>
  );
}

export default InformationIcon;
