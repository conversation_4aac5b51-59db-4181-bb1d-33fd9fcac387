import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import ReactNativeModal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import {useDispatch} from 'react-redux';
import {AppActions} from '../../../app.slice';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import SysFetch from '../../../services/fetch';
import {AccountActions} from '../services/account.slice';

const UploadAvatarModal = ({isVisible = false, setIsVisible}) => {
  const insets = useSafeAreaInsets();
  const dispatch = useDispatch();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      zIndex: 90,
      alignItems: 'center',
      justifyContent: 'center',
    },
    content: {
      backgroundColor: 'white',
      padding: 20,
      borderTopLeftRadius: 12,
      borderTopRightRadius: 12,
      width: '100%',
      paddingBottom: insets.bottom + 20,
    },
    title: {
      fontSize: 20,
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
      lineHeight: 30,
      marginBottom: 20,
    },
    space: {
      flex: 1,
      width: '100%',
    },
    modal: {
      width: '100%',
      padding: 0,
      margin: 0,
    },
    btnArea: {
      marginBottom: 10,
    },
  });

  const imageOptions = {
    width: 1200,
    height: 1200,
    cropping: true,
  };

  const onTakePhoto = async () => {
    try {
      const image = await ImagePicker.openCamera(imageOptions);
      setIsVisible(false);
      onUpload(image);
    } catch (error) {
      console.log({error});
    }
  };

  const onPickPhoto = async () => {
    try {
      const image = await ImagePicker.openPicker(imageOptions);
      setIsVisible(false);
      onUpload(image);
    } catch (error) {
      console.log({error});
    }
  };

  const onUpload = async currentImage => {
    try {
      const formData = new FormData();
      const fileName = currentImage?.path.split('/').pop();

      formData.append('file', {
        uri: currentImage.path,
        name: fileName,
        type: currentImage?.mime,
      });

      const response = await SysFetch.post('me/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      dispatch(AppActions.setLoading(false));
      dispatch(AccountActions.setProfile(response?.data));

      Toast.show({
        type: 'success',
        text1: 'Cập nhật avatar',
        text2: 'Cập nhật thành công',
      });
    } catch (error) {
      // console.log({error});
      // Toast.show({
      //   type: 'error',
      //   text1: 'Cập nhật avatar',
      //   text2: error,
      // });
      setIsVisible(true);
    }
  };

  return (
    <ReactNativeModal
      animationIn={'fadeInUp'}
      animationOut={'fadeOutDown'}
      style={styles.modal}
      isVisible={isVisible}
      onSwipeComplete={() => {
        setIsVisible(false);
      }}
      swipeDirection="down">
      <View style={styles.container}>
        <TouchableOpacity
          style={styles.space}
          onPress={() => setIsVisible(false)}
        />
        <View style={styles.content}>
          <Sys.Text style={styles.title}>Cập nhật avatar</Sys.Text>
          <View style={styles.btnArea}>
            <Sys.Button onPress={onTakePhoto}>Chụp ảnh</Sys.Button>
          </View>
          <View style={styles.btnArea}>
            <Sys.Button onPress={onPickPhoto}>Upload</Sys.Button>
          </View>
        </View>
      </View>
    </ReactNativeModal>
  );
};

export default UploadAvatarModal;
