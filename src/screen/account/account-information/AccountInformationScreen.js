import {useNavigation} from '@react-navigation/native';
import React, {useState} from 'react';
import {
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {useSelector} from 'react-redux';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import {AccountSelectors} from '../services/account.slice';
import UploadAvatarModal from './UploadAvatarModal';
import CameraIcon from './icons/CameraIcon';
import moment from 'moment';

const AccountInformationScreen = () => {
  const {navigate} = useNavigation();
  const profile = useSelector(AccountSelectors.profile);
  const [modalUpload, setModalUpload] = useState(false);
  const {isGuest} = useSelector(state => state.account);
  const styles = StyleSheet.create({
    container: {
      margin: 20,
    },
    avatar: {
      width: 120,
      height: 120,
      borderRadius: 60,
    },
    avatarArea: {
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    nameArea: {
      marginTop: 28,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
    },
    name: {
      fontSize: 18,
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
      color: '#001451',
      lineHeight: 28,
    },
    code: {
      lineHeight: 24,
    },
    btnArea: {
      marginTop: 30,
    },
    camera: {
      position: 'absolute',
      bottom: 0,
      width: 35,
      height: 35,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#F1F1F1',
      borderRadius: 50,
      right: 0,
    },
  });

  const renderItem = ({item, index}) => {
    if (isGuest && item.label !== 'Phòng ban:') {
      return <></>;
    } else {
      return <OneItem item={item} />;
    }
  };

  const data = [
    {label: 'Họ:', value: `${profile?.first_name || ''}`},
    {label: 'Tên:', value: `${profile?.last_name || ''}`},
    {label: 'Số điện thoại:', value: `${profile?.phone || ''}`},
    {
      label: 'Ngày sinh:',
      value: profile?.birthday
        ? `${moment(profile?.birthday, 'YYYY-MM-DD').format('DD/MM/YYYY')}`
        : '',
    },
    {label: 'Phòng ban:', value: profile?.branch?.code},
  ];
  const goToEdit = () => {
    navigate('EditAccountInformationScreen');
  };

  return (
    <Sys.Container hasHeader>
      <Sys.Header title={'Thông tin cá nhân'} />
      <UploadAvatarModal
        isVisible={modalUpload}
        setIsVisible={setModalUpload}
      />
      <View style={styles.container}>
        <View style={styles.avatarArea}>
          <View>
            <Image
              style={styles.avatar}
              source={{
                uri: profile?.avatar,
              }}
            />
            <TouchableOpacity
              activeOpacity={0.9}
              onPress={() => setModalUpload(true)}
              style={styles.camera}>
              <CameraIcon />
            </TouchableOpacity>
          </View>
        </View>
        {!isGuest ? (
          <View style={styles.nameArea}>
            <Sys.Text style={styles.name}>{`${profile?.name}`}</Sys.Text>
            {profile?.code && (
              <Sys.Text style={styles.code}>
                Mã nhân viên: {`${profile?.code || ''}`}
              </Sys.Text>
            )}
          </View>
        ) : (
          <View />
        )}
        <View>
          <FlatList
            scrollEnabled={false}
            data={data}
            keyExtractor={(item, index) => index.toString()}
            renderItem={renderItem}
          />
        </View>
        <View style={styles.btnArea}>
          <Sys.Button onPress={goToEdit}>Sửa thông tin</Sys.Button>
        </View>
      </View>
    </Sys.Container>
  );
};

export default AccountInformationScreen;

const OneItem = ({item}) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 20,
    },
    label: {
      color: '#848484',
    },
  });

  return (
    <View style={styles.container}>
      <View>
        <Sys.Text style={styles.label}>{item.label}</Sys.Text>
      </View>
      <View>
        <Sys.Text>{item.value}</Sys.Text>
      </View>
    </View>
  );
};
