import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

function CameraIcon(props) {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={19}
      height={19}
      viewBox="0 0 19 19"
      fill="none"
      {...props}>
      <Path
        d="M18.208 15.042a1.584 1.584 0 01-1.583 1.583H2.375a1.583 1.583 0 01-1.583-1.583V6.333A1.583 1.583 0 012.375 4.75h3.167l1.583-2.375h4.75l1.583 2.375h3.167a1.583 1.583 0 011.583 1.583v8.709z"
        stroke="#000"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M9.5 13.458a3.167 3.167 0 100-6.333 3.167 3.167 0 000 6.333z"
        stroke="#000"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}

export default CameraIcon;
