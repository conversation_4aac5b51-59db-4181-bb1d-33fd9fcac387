import React, {useMemo} from 'react';
import {Image, StyleSheet, useWindowDimensions, View} from 'react-native';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import {useSelector} from 'react-redux';
import {AccountSelectors} from '../services/account.slice';

const BaseInfoAccount = () => {
  const {width} = useWindowDimensions();
  const profile = useSelector(AccountSelectors.profile);
  const {view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);
  const dynamicStyles = useMemo(
    () => ({
      inforArea: {
        borderRadius: 8,
        backgroundColor: '#ff000015',
        height: isGuest ? 80 : 120,
        width: width - 40,
        marginTop: isGuest ? 0 : 40,
        justifyContent: 'flex-end',
        paddingTop: isGuest ? 50 : 0,
      },
    }),
    [width, view],
  );

  return (
    <View style={styles.container}>
      <View style={styles.avatarArea}>
        <Image
          style={styles.avatar}
          source={{
            uri: profile?.avatar,
          }}
        />
      </View>
      <View style={dynamicStyles.inforArea}>
        {isGuest ? (
          <Sys.Text style={styles.guestName}>{profile?.username}</Sys.Text>
        ) : (
          <View>
            <Sys.Text style={styles.name}>{`${profile?.name}`}</Sys.Text>
            <Sys.Text style={styles.address}>
              {`${profile?.branch?.code || ''}${
                profile?.branch?.name ? ` | ${profile?.branch?.name}` : ''
              }`}
            </Sys.Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 160,
    margin: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 50,
    backgroundColor: '#********',
  },
  avatarArea: {
    position: 'absolute',
    top: 0,
    zIndex: 8,
  },
  name: {
    color: '#001451',
    fontSize: 18,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    lineHeight: 28,
    textAlign: 'center',
    marginBottom: 3,
  },
  guestName: {
    color: '#001451',
    fontSize: 18,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    textAlign: 'center',
  },
  address: {
    marginBottom: 15,
    textAlign: 'center',
  },
});

export default BaseInfoAccount;
