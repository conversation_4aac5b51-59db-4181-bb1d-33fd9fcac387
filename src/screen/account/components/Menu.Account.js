import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>View,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Sys from '../../../components/Sys';

import {CommonActions, useNavigation} from '@react-navigation/native';
import {Switch} from 'react-native-switch';
import TouchID from 'react-native-touch-id';
import {useDispatch, useSelector} from 'react-redux';
import {AppActions} from '../../../app.slice';
import THEME from '../../../components/theme/theme';
import CONST from '../../../services/const';
import SysStorage from '../../../services/storage';
import FaceIdIcon from '../../login/icons/FaceIdIcon';
import {LoginActions} from '../../login/services/login.slice';
import ArrowIcon from '../icons/ArrowIcon';
import CallIcon from '../icons/CallIcon';
import InformationIcon from '../icons/InformationIcon';
import MenuIcon from '../icons/MenuIcon';
import PasswordIcon from '../icons/PasswordIcon';
import {AccountActions, AccountSelectors} from '../services/account.slice';
import {useEffect, useState} from 'react';
import StorageService from '../../../services/storageService';
import SysFetch from '../../../services/fetch';
import BellIcon from './BellIcon';

const MenuAccount = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const profile = useSelector(AccountSelectors.profile);
  const notifications_enabled = Boolean(profile?.notifications_enabled);
  const [isBioAuthEnabled, setIsBioAuthEnabled] = useState(false);

  useEffect(() => {
    initCheckBio();
  }, []);

  const initCheckBio = async () => {
    const currentBioTCheck = await SysStorage(CONST.STORAGE.BIO_ENABLE).get();
    setIsBioAuthEnabled(currentBioTCheck);
  };

  const onSwitchBio = async () => {
    const checkBio = await TouchID.authenticate();
    if (checkBio) {
      const currentBioTCheck = await SysStorage(CONST.STORAGE.BIO_ENABLE).get();
      const check = !currentBioTCheck;
      await StorageService.setBioEnable(check);
      setIsBioAuthEnabled(check);
      dispatch(
        LoginActions.getRefreshToken({
          onSuccess: rs => {
            dispatch(AppActions.setBioToken(rs));
          },
        }),
      );
    }
  };

  const onLogout = async () => {
    console.log('onLogout click');
    navigation.dispatch(
      CommonActions.reset({
        index: 1,
        routes: [{name: 'LoginScreen'}],
      }),
    );
  };
  const handleNotificationToggle = async val => {
    try {
      const response = await SysFetch.post('notification-settings', {
        notifications_enabled: val ? 1 : 0,
      });
      if (response.success) {
        dispatch(AccountActions.updateProfile({notifications_enabled: val}));
        console.log('Notification settings updated:', val);
      } else {
        console.error('Failed to update notification settings:', response);
      }
    } catch (error) {
      console.error('Error updating notification settings:', error);
    }
  };
  const menuList = [
    {
      name: 'Thông tin cá nhân',
      onPress: () => {
        navigation.navigate('AccountInformationScreen');
      },
      icon: <InformationIcon />,
    },
    {
      name: 'Đổi mật khẩu',
      onPress: () => {
        navigation.navigate('ChangePasswordScreen');
      },
      icon: <PasswordIcon />,
    },
    {
      name: 'Sinh trắc học',
      onPress: () => {
        // navigation.navigate('ChangePasswordScreen');
      },
      icon: <FaceIdIcon />,
      right: (
        <Switch
          value={isBioAuthEnabled}
          onValueChange={onSwitchBio}
          renderActiveText={false}
          renderInActiveText={false}
          backgroundActive={THEME.Color.primary}
          backgroundInactive={'gray'}
        />
      ),
    },
    {
      name: 'Thông báo',
      onPress: () => {
        console.log(1);
        // navigation.navigate('SettingScreen');
      },
      icon: <BellIcon />,
      right: (
        <Switch
          value={notifications_enabled}
          onValueChange={handleNotificationToggle}
          renderActiveText={false}
          renderInActiveText={false}
          backgroundActive={THEME.Color.primary}
          backgroundInactive={'gray'}
        />
      ),
    },
  ];
  const menuList2 = [
    {
      name: 'Liên hệ',
      onPress: () => {
        navigation.navigate('AppContactScreen');
      },
      icon: <CallIcon />,
    },
    {
      name: 'Thông tin ứng dụng',
      onPress: () => {
        navigation.navigate('AppInfoScreen');
      },
      icon: <MenuIcon />,
    },
  ];
  const renderItem = ({item, index}) => {
    return <OneItem item={item} />;
  };

  const styles = StyleSheet.create({
    btnArea: {
      marginHorizontal: 20,
    },
  });

  const onDelete = () => {
    navigation.navigate('DeleteAccountScreen');
  };

  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      <View>
        <View
          style={{
            marginHorizontal: 20,
            marginBottom: 10,
          }}>
          <Sys.Text
            style={{
              color: '#001451',
              fontSize: 18,
              fontWeight: 700,
            }}>
            Cài đặt
          </Sys.Text>
        </View>
        <FlatList
          scrollEnabled={false}
          data={menuList}
          renderItem={renderItem}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
      <View>
        <View
          style={{
            marginHorizontal: 20,
            marginBottom: 10,
          }}>
          <Sys.Text
            style={{
              color: '#001451',
              fontSize: 18,
              fontWeight: 700,
            }}>
            Hỗ trợ
          </Sys.Text>
        </View>
        <FlatList
          scrollEnabled={false}
          data={menuList2}
          renderItem={renderItem}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
      {/*{isGuest && (*/}
      <View
        style={[
          styles.btnArea,
          {
            marginBottom: 10,
          },
        ]}>
        <TouchableOpacity
          style={{
            padding: 10,
            alignItems: 'center',
          }}
          onPress={onDelete}>
          <Sys.Text
            style={{
              color: 'red',
            }}>
            Xóa tài khoản
          </Sys.Text>
        </TouchableOpacity>
      </View>
      {/*)*/}

      <View style={styles.btnArea}>
        <Sys.Button onPress={onLogout}>Đăng xuất</Sys.Button>
      </View>
      <View style={{height: 400}} />
    </ScrollView>
  );
};

export default MenuAccount;
const OneItem = ({item}) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      borderRadius: 8,
      borderWidth: 1,
      marginBottom: 20,
      marginHorizontal: 20,
      height: 60,
      alignItems: 'center',
      borderColor: '#D1D1D1',
      backgroundColor: 'white',
    },
    iconContainer: {
      padding: 20,
    },
    content: {
      flex: 1,
    },
    iconArrow: {
      padding: 20,
    },
    name: {
      lineHeight: 24,
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
    },
  });

  return (
    <TouchableOpacity
      style={styles.container}
      activeOpacity={0.8}
      onPress={item?.onPress}>
      <View style={styles.iconContainer}>{item?.icon}</View>
      <View style={styles.content}>
        <Sys.Text style={styles.name}>{item?.name}</Sys.Text>
      </View>
      <View style={styles.iconArrow}>{item.right || <ArrowIcon />}</View>
    </TouchableOpacity>
  );
};
