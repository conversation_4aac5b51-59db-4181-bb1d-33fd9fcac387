import React, {useEffect} from 'react';
import {View} from 'react-native';

import {useDispatch} from 'react-redux';
import MenuAccount from './Menu.Account';
import BaseInfoAccount from './BaseInfo.Account';
import {useIsFocused} from '@react-navigation/native';
import {AccountActions} from '../services/account.slice';

const InformationAccount = () => {
  const dispatch = useDispatch();

  const isFocused = useIsFocused();
  useEffect(() => {
    if (isFocused) {
      dispatch(AccountActions.getProfile());
    }
  }, [isFocused]);

  return (
    <View>
      <BaseInfoAccount />
      <MenuAccount />
    </View>
  );
};

export default InformationAccount;
