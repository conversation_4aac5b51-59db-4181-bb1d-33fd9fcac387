import {View, Text, StyleSheet} from 'react-native';
import React, {useState, useCallback} from 'react';
import {useDispatch} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import {AccountActions} from '../services/account.slice';

const ChangePasswordScreen = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const validatePassword = password => {
    return (
      password.length >= 8 &&
      password.length <= 20 &&
      /[A-Z]/.test(password) &&
      /[0-9]/.test(password) &&
      /[!@#$%^&*(),.?":{}|<>]/.test(password)
    );
  };

  const onChangePassword = useCallback(() => {
    if (!currentPassword) {
      Toast.show({
        type: 'error',
        text1: 'Lỗi',
        text2: 'Vui lòng nhập mật khẩu hiện tại',
      });
      return;
    }

    if (!validatePassword(newPassword)) {
      Toast.show({
        type: 'error',
        text1: 'Lỗi',
        text2: 'Mật khẩu mới không đáp ứng các điều kiện yêu cầu',
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      Toast.show({
        type: 'error',
        text1: 'Lỗi',
        text2: 'Mật khẩu xác nhận không trùng khớp',
      });
      return;
    }

    dispatch(
      AccountActions.changePassword({
        current_password: currentPassword,
        password: newPassword,
        password_confirmation: confirmPassword,
        onSuccess: () => {
          // Reset form
          setCurrentPassword('');
          setNewPassword('');
          setConfirmPassword('');
          // Navigate back after success
          setTimeout(() => {
            navigation.goBack();
          }, 1500);
        },
      }),
    );
  }, [currentPassword, newPassword, confirmPassword, dispatch]);

  const styles = StyleSheet.create({
    container: {
      margin: 20,
    },
    oneRow: {
      marginBottom: 20,
    },
    btnArea: {
      marginTop: 20,
      marginBottom: 50,
    },
    title: {
      color: '#848484',
      lineHeight: 24,
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
    },
    text: {
      color: '#848484',
      lineHeight: 24,
      marginLeft: 12,
    },
  });
  return (
    <Sys.Container hasHeader>
      <Sys.Header title={'Thay đổi mật khẩu'} />
      <View style={styles.container}>
        <View style={styles.oneRow}>
          <Sys.Input
            label={'Mật khẩu cũ'}
            placeholder={'Nhập mật khẩu cũ'}
            value={currentPassword}
            onChangeText={setCurrentPassword}
            secureTextEntry
          />
        </View>
        <View style={styles.oneRow}>
          <Sys.Input
            label={'Mật khẩu mới'}
            placeholder={'Nhập mật khẩu mới'}
            value={newPassword}
            onChangeText={setNewPassword}
            secureTextEntry
          />
        </View>
        <View style={styles.oneRow}>
          <Sys.Input
            label={'Nhập lại mật khẩu mới'}
            placeholder={'Nhập lại mật khẩu mới'}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
          />
        </View>
        <View style={styles.btnArea}>
          <Sys.Button onPress={onChangePassword}>Xác nhận</Sys.Button>
        </View>
        <View>
          <Sys.Text style={styles.title}>
            Mật khẩu phải thỏa mãn các điều kiện sau:
          </Sys.Text>
          <Sys.Text style={styles.text}>Có độ dài từ 8 đến 20 kí tự.</Sys.Text>
          <Sys.Text style={styles.text}>
            Chứa ít nhất 01 ký tự số, 01 ký tự chữ Hoa, 01 ký tự đặc biệt
          </Sys.Text>
          <Sys.Text style={styles.text}>
            Ví dụ: V@123456; 123456@V; 123456V@
          </Sys.Text>
        </View>
      </View>
    </Sys.Container>
  );
};

export default ChangePasswordScreen;
