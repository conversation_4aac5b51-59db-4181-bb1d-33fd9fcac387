import {StyleSheet, View} from 'react-native';
import React from 'react';
import Sys from '../../../components/Sys';
import {useDispatch, useSelector} from 'react-redux';
import {AppActions, AppSelectors} from '../../../app.slice';
import {Switch} from 'react-native-switch';
import TouchID from 'react-native-touch-id';
import THEME from '../../../components/theme/theme';
import {LoginActions} from '../../login/services/login.slice';
import SysStorage from '../../../services/storage';
import CONST from '../../../services/const';
import StorageService from '../../../services/storageService';
import {AccountActions, AccountSelectors} from '../services/account.slice';
import SysFetch from '../../../services/fetch';

const SettingScreen = () => {
  const dispatch = useDispatch();
  const bioType = useSelector(AppSelectors.bioType);
  const bioToken = useSelector(AppSelectors.bioToken);
  const profile = useSelector(AccountSelectors.profile);
  const notifications_enabled = Boolean(profile?.notifications_enabled);

  const onSwitchBio = async () => {
    const checkbio = await TouchID.authenticate();

    if (checkbio) {
      if (bioToken) {
        //off bio
        dispatch(AppActions.setBioToken(''));
        const bioStorage = SysStorage(CONST.STORAGE.BIO_TOKEN);
        bioStorage.remove();
      } else {
        await StorageService.setBioEnable(true);
        dispatch(
          LoginActions.getRefreshToken({
            onSuccess: rs => {
              dispatch(AppActions.setBioToken(rs));
            },
          }),
        );
      }
    }
  };
  const handleNotificationToggle = async val => {
    try {
      const response = await SysFetch.post('notification-settings', {
        notifications_enabled: val,
      });

      if (response.success) {
        dispatch(AccountActions.updateProfile({notifications_enabled: val}));
        console.log('Notification settings updated:', val);
      } else {
        console.error('Failed to update notification settings:', response);
      }
    } catch (error) {
      console.error('Error updating notification settings:', error);
    }
  };
  const styles = StyleSheet.create({
    container: {
      margin: 20,
    },
    oneItem: {
      flexDirection: 'row',
      height: 60,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#D1D1D1',
      backgroundColor: 'white',
      alignItems: 'center',
      padding: 20,
      justifyContent: 'space-between',
      marginBottom: 20,
    },
  });

  return (
    <Sys.Container hasHeader>
      <Sys.Header title={'Cài đặt'} />
      <View style={styles.container}>
        <View style={styles.oneItem}>
          <View>
            <Sys.Text>Nhận thông báo</Sys.Text>
          </View>
          <View>
            <Switch
              value={notifications_enabled}
              onValueChange={handleNotificationToggle}
              renderActiveText={false}
              renderInActiveText={false}
            />
          </View>
        </View>
        <View style={styles.oneItem}>
          <View>
            <Sys.Text>
              Đăng nhập bằng {bioType == 'FaceID' ? 'khuôn mặt' : 'vân tay'}
            </Sys.Text>
          </View>
          <View>
            <Switch
              value={!!bioToken}
              onValueChange={onSwitchBio}
              renderActiveText={false}
              renderInActiveText={false}
              backgroundActive={THEME.Color.primary}
              backgroundInactive={'gray'}
            />
          </View>
        </View>
      </View>
    </Sys.Container>
  );
};

export default SettingScreen;
