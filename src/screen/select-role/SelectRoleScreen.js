import {View, Text} from 'react-native';
import React from 'react';
import ContainerComponent from '../../components/ContainerComponent';
import Button from '../../components/bases/Button';
import {useNavigation} from '@react-navigation/native';

const SelectRoleScreen = () => {
  const navigation = useNavigation();

  const onGoToHome = () => {
    navigation.navigate('DashboardScreen');
  };

  return (
    <ContainerComponent>
      <View>
        <Button onPress={onGoToHome}>Go to Home</Button>
      </View>
    </ContainerComponent>
  );
};

export default SelectRoleScreen;
