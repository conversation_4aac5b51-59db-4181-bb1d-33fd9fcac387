import moment from 'moment';
import React, {useEffect, useMemo, useState} from 'react';
import {Dimensions, FlatList, TouchableOpacity, View, StyleSheet} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Sys from '../../components/Sys';
import LeftPlanIcon from './icons/LeftPlanIcon';
import LocationSmallIcon from './icons/LocationSmallIcon';
import RightPlanIcon from './icons/RightPlanIcon';
import StoreSmallIcon from './icons/StoreSmallIcon';
import {AccountSelectors} from '../account/services/account.slice';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {PosActions} from '../sale-location/services/pos.slice';
import {openGoogleMaps} from '../checkout/Checkout.Infomation';
import Color from '../../components/theme/Color';
import THEME from '../../components/theme/theme'; 

const {width} = Dimensions.get('window');

const DatePlan = ({
  currentTab,
  setStatus,
  style = {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingTop: 10,
    flex: 1,
  },
}) => {
  const {navigate} = useNavigation();
  const profile = useSelector(AccountSelectors.profile);
  const isFocused = useIsFocused();
  const dispatch = useDispatch();
  const [posList, setPosList] = useState([]);
  const [month, setMonth] = useState(moment());

  const {view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);

  useEffect(() => {
    if (profile && isFocused) {
      dispatch(
        PosActions.getDatePlan({
          onSuccess: rs => {
            setPosList(rs.data);
            setStatus(rs?.data[0]?.plan?.plan_status_label || '');
          },
          params: {
            plan_month: month.format('YYYY-MM'),
            ...(isBranchOwner || isBranchManager ? {staff_owner: true} : {}),
            ...(isStaff ? {staff_id: profile?.id} : {}),
            pageSize: -1,
            with: ['agency', 'plans', 'staff'],
          },
          month,
        }),
      );
    }
  }, [currentTab, month, profile, isFocused]);

  const [currentDay, setCurrentDay] = useState(moment(new Date()).format('DD'));

  const addMonth = num => {
    setMonth(moment(month).add(num, 'M'));
  };

  const renderList = useMemo(() => {
    return (
      <FlatList
        showsVerticalScrollIndicator={false}
        ListFooterComponent={
          <View style={{height: 10, backgroundColor: 'white'}} />
        }
        data={(() => {
          try {
            let temp = posList.filter(x => {
              return (
                moment(x.plan.date, 'YYYY-MM-DD').format('DD') == currentDay
              );
            });
            return temp;
          } catch (error) {
            return [];
          }
        })()}
        renderItem={({item, index}) => {
          const checkedIn = isCheckedIn(item?.plan?.checkin_status);
          return (
            <TouchableOpacity
              onPress={() =>
                navigate('SaleInformationScreen', {
                  ...item,
                })
              }
              style={modernStyles.planItem}
              activeOpacity={0.8}>
              <LinearGradient
                colors={['#ffffff', '#f8f9fa']}
                style={modernStyles.planItemGradient}>

                {/* Date Badge */}
                <View style={modernStyles.dateBadgeContainer}>
                  <LinearGradient
                    colors={checkedIn ? [Color.green, '#7CB342'] : [Color.blue, '#4A90E2']}
                    style={modernStyles.dateBadge}>
                    <Sys.Text style={modernStyles.dateBadgeDay}>
                      {item?.plan?.date || item?.last_checkin ?
                        moment(
                          checkedIn ? item?.last_checkin : item.plan.date,
                          'YYYY-MM-DD',
                        ).format('DD') : ''
                      }
                    </Sys.Text>
                    <Sys.Text style={modernStyles.dateBadgeMonth}>
                      {item?.plan?.date || item?.last_checkin ?
                        moment(
                          checkedIn ? item?.last_checkin : item.plan.date,
                          'YYYY-MM-DD',
                        ).format('MM') : ''
                      }
                    </Sys.Text>
                  </LinearGradient>

                  {checkedIn && (
                    <View style={modernStyles.statusBadge}>
                      <MaterialCommunityIcons
                        name="check-circle"
                        size={12}
                        color={Color.green}
                      />
                      <Sys.Text style={modernStyles.statusText}>
                        Đã ghé thăm
                      </Sys.Text>
                    </View>
                  )}
                </View>
                {/* Content */}
                <View style={modernStyles.planItemContent}>
                  {/* Header */}
                  <View style={modernStyles.planItemHeader}>
                    <Sys.Text style={modernStyles.planItemTitle}>
                      {item?.posCode}
                    </Sys.Text>
                    {item?.staff && (
                      <Sys.Text style={modernStyles.planItemStaff}>
                        ({item.staff?.name})
                      </Sys.Text>
                    )}
                  </View>

                  {/* Address */}
                  <View style={modernStyles.planItemRow}>
                    <View style={modernStyles.planItemIcon}>
                      <MaterialCommunityIcons
                        name="map-marker"
                        size={14}
                        color={Color.gray}
                      />
                    </View>
                    <View style={modernStyles.planItemTextContainer}>
                      <Sys.Text style={modernStyles.planItemText}>
                        {item?.address}
                      </Sys.Text>
                      <TouchableOpacity
                        onPress={() => {
                          openGoogleMaps(item?.lat, item?.lon);
                        }}
                        style={modernStyles.directionButton}>
                        <MaterialCommunityIcons
                          name="directions"
                          size={12}
                          color={Color.blue}
                        />
                        <Sys.Text style={modernStyles.directionText}>
                          Chỉ đường
                        </Sys.Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  {/* Agency */}
                  <View style={modernStyles.planItemRow}>
                    <View style={modernStyles.planItemIcon}>
                      <MaterialCommunityIcons
                        name="store"
                        size={14}
                        color={Color.gray}
                      />
                    </View>
                    <Sys.Text style={modernStyles.planItemText}>
                      {item?.agency?.name}
                    </Sys.Text>
                  </View>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          );
        }}
        keyExtractor={(item, index) => index.toString()}
      />
    );
  }, [posList, currentDay]);

  const calendarData = useMemo(() => {
    try {
      const daysInMonth = month.daysInMonth();
      const dayOfWeek = month.startOf('month').isoWeekday(); // Đảm bảo sử dụng isoWeekday() cho tuần bắt đầu từ thứ Hai
      const temp = [];

      for (let i = 1; i < dayOfWeek; i++) {
        temp.push({field: 'NoDate'});
      }

      for (let index = 1; index <= daysInMonth; index++) {
        const dayData = posList.find(
          x =>
            moment(x.plan.date, 'YYYY-MM-DD').format('DD') ===
            String(index).padStart(2, '0'),
        );
        temp.push({
          field: index,
          value: index - 1,
          day: dayData?.plan?.date
            ? moment(dayData.plan.date, 'YYYY-MM-DD').format('DD')
            : null,
          checkin_status: isCheckedIn(dayData?.plan?.checkin_status),
        });
      }

      return temp;
    } catch (error) {
      console.log({error});
      return [];
    }
  }, [month, posList]);

  return (
    <View style={style}>
      {/* Month Navigation */}
      <View style={modernStyles.monthNavigation}>
        <View style={modernStyles.monthHeader}>
          <View style={modernStyles.monthTitleContainer}>
            <MaterialCommunityIcons
              name="calendar-month"
              size={16}
              color={Color.blue}
            />
            <Sys.Text style={modernStyles.monthTitle}>
              {month.format('MM/YYYY')}
            </Sys.Text>
          </View>

          <View style={modernStyles.monthControls}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => addMonth(-1)}
              style={modernStyles.monthButton}>
              <MaterialCommunityIcons
                name="chevron-left"
                size={16}
                color={Color.blue}
              />
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => addMonth(1)}
              style={modernStyles.monthButton}>
              <MaterialCommunityIcons
                name="chevron-right"
                size={16}
                color={Color.blue}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      {/* Calendar */}
      <View style={modernStyles.calendarContainer}>
        {/* Week Days Header */}
        <View style={modernStyles.weekDaysContainer}>
          {['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'].map((day, index) => (
            <View key={index} style={modernStyles.weekDayItem}>
              <Sys.Text style={modernStyles.weekDayText}>
                {day}
              </Sys.Text>
            </View>
          ))}
        </View>

        <RenderCalendar
          posList={posList}
          setCurrentDay={setCurrentDay}
          currentDay={currentDay}
          data={calendarData}
        />
      </View>
      {/* Plan List */}
      <View style={modernStyles.planListContainer}>
        {renderList}
      </View>
    </View>
  );
};

// Modern Styles
const modernStyles = StyleSheet.create({
  // Month Navigation
  monthNavigation: {
    marginBottom: 16,
  },
  monthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  monthTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  monthTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.Color.text,
    marginLeft: 8,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  monthControls: {
    flexDirection: 'row',
    gap: 8,
  },
  monthButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 98, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Calendar
  calendarContainer: {
    marginBottom: 16,
  },
  weekDaysContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  weekDayItem: {
    width: (width - 60) / 7,
    alignItems: 'center',
  },
  weekDayText: {
    fontSize: 12,
    fontWeight: '700',
    color: Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },

  // Plan List
  planListContainer: {
    flex: 1,
  },

  // Plan Items
  planItem: {
    marginBottom: 8,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  planItemGradient: {
    padding: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },

  // Date Badge
  dateBadgeContainer: {
    alignItems: 'center',
    marginRight: 12,
  },
  dateBadge: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  dateBadgeDay: {
    color: 'white',
    fontSize: 14,
    fontWeight: '700',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    lineHeight: 16,
  },
  dateBadgeMonth: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 10,
    fontWeight: '600',
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    lineHeight: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderRadius: 8,
  },
  statusText: {
    fontSize: 8,
    color: Color.green,
    marginLeft: 2,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
  },

  // Plan Item Content
  planItemContent: {
    flex: 1,
  },
  planItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    flexWrap: 'wrap',
  },
  planItemTitle: {
    color: Color.blue,
    fontSize: 16,
    fontWeight: '700',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginRight: 8,
  },
  planItemStaff: {
    color: Color.gray,
    fontSize: 14,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
  },
  planItemRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
  },
  planItemIcon: {
    width: 20,
    alignItems: 'center',
    marginRight: 8,
    marginTop: 2,
  },
  planItemTextContainer: {
    flex: 1,
  },
  planItemText: {
    fontSize: 14,
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    lineHeight: 18,
  },
  directionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    paddingVertical: 4,
    paddingHorizontal: 8,
    backgroundColor: 'rgba(0, 98, 255, 0.1)',
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  directionText: {
    fontSize: 12,
    color: Color.blue,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginLeft: 4,
  },
});

export default DatePlan;

const RenderCalendar = ({data, currentDay, setCurrentDay, posList}) => {
  // Hàm render dấu chấm trạng thái
  const renderStatusDot = (hasDay, isCurrentDay, checkinStatus) => {
    return (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          position: 'absolute',
          bottom: 4,
        }}>
        <View
          style={{
            width: 4,
            height: 4,
            borderRadius: 5,
            backgroundColor: hasDay
              ? checkinStatus
                ? Color.green
                : Color.blue
              : Color.white,
          }}
        />
      </View>
    );
  };

  return (
    <FlatList
      scrollEnabled={false}
      data={data}
      numColumns={7}
      keyExtractor={(item, index) => index.toString()}
      renderItem={({item, index}) => {
        if (item.field === 'NoDate') {
          return (
            <View
              style={{
                padding: 5,
                width: (width - 60) / 7,
                height: (width - 60) / 7,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <View
                style={{
                  borderRadius: 10,
                  width: (width - 150) / 7,
                  height: (width - 150) / 7,
                  backgroundColor: Color.white,
                }}
              />
            </View>
          );
        }

        const isCurrentDay = Number(currentDay) === Number(item.field);
        const hasDay = item.field == item.day;
        return (
          <TouchableOpacity
            onPress={() => setCurrentDay(item.field)}
            style={{
              padding: 5,
              width: (width - 60) / 7,
              height: (width - 60) / 7,
            }}>
            <View
              style={[
                {
                  borderRadius: 10,
                  width: (width - 150) / 7,
                  height: (width - 150) / 7,
                  justifyContent: 'center',
                  alignItems: 'center',
                  overflow: 'hidden',
                  backgroundColor: isCurrentDay
                    ? item.checkin_status
                      ? Color.green
                      : Color.blue
                    : Color.white,
                },
              ]}>
              <Sys.Text
                style={{
                  color: isCurrentDay ? Color.white : Color.black,
                  fontWeight: isCurrentDay ? '700' : '400',
                  fontSize: 13,
                }}>
                {item.field}
              </Sys.Text>
              {hasDay &&
                renderStatusDot(hasDay, isCurrentDay, item.checkin_status)}
            </View>
          </TouchableOpacity>
        );
      }}
    />
  );
};

const isCheckedIn = status => {
  return [2, 3].includes(status);
};
