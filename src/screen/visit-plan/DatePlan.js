import moment from 'moment';
import React, {useEffect, useMemo, useState} from 'react';
import {Dimensions, FlatList, TouchableOpacity, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import LeftPlanIcon from './icons/LeftPlanIcon';
import LocationSmallIcon from './icons/LocationSmallIcon';
import RightPlanIcon from './icons/RightPlanIcon';
import StoreSmallIcon from './icons/StoreSmallIcon';
import {AccountSelectors} from '../account/services/account.slice';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {PosActions} from '../sale-location/services/pos.slice';
import {openGoogleMaps} from '../checkout/Checkout.Infomation';
import Color from '../../components/theme/Color';

const {width} = Dimensions.get('window');

const DatePlan = ({
  currentTab,
  setStatus,
  style = {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingTop: 10,
    flex: 1,
  },
}) => {
  const {navigate} = useNavigation();
  const profile = useSelector(AccountSelectors.profile);
  const isFocused = useIsFocused();
  const dispatch = useDispatch();
  const [posList, setPosList] = useState([]);
  const [month, setMonth] = useState(moment());

  const {view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);

  useEffect(() => {
    if (profile && isFocused) {
      dispatch(
        PosActions.getDatePlan({
          onSuccess: rs => {
            setPosList(rs.data);
            setStatus(rs?.data[0]?.plan?.plan_status_label || '');
          },
          params: {
            plan_month: month.format('YYYY-MM'),
            ...(isBranchOwner || isBranchManager ? {staff_owner: true} : {}),
            ...(isStaff ? {staff_id: profile?.id} : {}),
            pageSize: -1,
            with: ['agency', 'plans', 'staff'],
          },
          month,
        }),
      );
    }
  }, [currentTab, month, profile, isFocused]);

  const [currentDay, setCurrentDay] = useState(moment(new Date()).format('DD'));

  const addMonth = num => {
    setMonth(moment(month).add(num, 'M'));
  };

  const renderList = useMemo(() => {
    return (
      <FlatList
        showsVerticalScrollIndicator={false}
        ListFooterComponent={
          <View style={{height: 10, backgroundColor: 'white'}} />
        }
        data={(() => {
          try {
            let temp = posList.filter(x => {
              return (
                moment(x.plan.date, 'YYYY-MM-DD').format('DD') == currentDay
              );
            });
            return temp;
          } catch (error) {
            return [];
          }
        })()}
        renderItem={({item, index}) => {
          return (
            <TouchableOpacity
              onPress={() =>
                navigate('SaleInformationScreen', {
                  ...item,
                })
              }
              style={{
                backgroundColor: '#FFFFFF99',
                marginBottom: 5,
                paddingVertical: 10,
                flexDirection: 'row',
              }}>
              <View>
                <View
                  style={{
                    backgroundColor: isCheckedIn(item?.plan?.checkin_status)
                      ? Color.green
                      : Color.blue,
                    width: 48,
                    height: 48,
                    borderRadius: 50,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <View>
                    {item?.plan?.date || item?.last_checkin ? (
                      <Sys.Text
                        style={{
                          color: 'white',
                          fontSize: 12,
                          fontWeight: '700',
                          lineHeight: 15,
                        }}>
                        {moment(
                          isCheckedIn(item?.plan?.checkin_status)
                            ? item?.last_checkin
                            : item.plan.date,
                          'YYYY-MM-DD',
                        ).format('DD')}
                      </Sys.Text>
                    ) : (
                      <></>
                    )}
                  </View>
                  <View>
                    {item?.plan?.date || item?.last_checkin ? (
                      <Sys.Text
                        style={{
                          color: 'white',
                          fontSize: 8,
                          fontWeight: '700',
                          lineHeight: 12,
                        }}>
                        {moment(
                          isCheckedIn(item?.plan?.checkin_status)
                            ? item?.last_checkin
                            : item.plan.date,
                          'YYYY-MM-DD',
                        ).format('MM/YYYY')}
                      </Sys.Text>
                    ) : (
                      <></>
                    )}
                  </View>
                </View>
                {isCheckedIn(item?.plan?.checkin_status) ? (
                  <Sys.Text
                    style={{
                      fontSize: 8,
                      color: '#848484',
                      marginTop: 2,
                    }}>
                    Đã ghé thăm
                  </Sys.Text>
                ) : (
                  <></>
                )}
              </View>
              <View
                style={{
                  marginLeft: 15,
                  flex: 1,
                }}>
                <View>
                  <Sys.Text
                    style={{
                      color: '#0062FF',
                      fontWeight: '700',
                      lineHeight: 16,
                    }}>
                    {item?.posCode}{' '}
                    {item?.staff && (
                      <Sys.Text
                        style={{
                          color: '#848484',
                          fontWeight: '400',
                        }}>
                        ({item.staff?.name})
                      </Sys.Text>
                    )}
                  </Sys.Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    marginTop: 4,
                    alignItems: 'flex-start',
                  }}>
                  <LocationSmallIcon />
                  <View style={{flex: 1, marginLeft: 4}}>
                    <Sys.Text style={{lineHeight: 16, fontSize: 12}}>
                      {item?.address}
                    </Sys.Text>
                    <TouchableOpacity
                      onPress={() => {
                        openGoogleMaps(item?.lat, item?.lon);
                      }}>
                      <Sys.Text
                        style={{
                          fontSize: 12,
                          color: '#0062FF',
                          marginTop: 2,
                        }}>
                        (Chỉ đường)
                      </Sys.Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    marginTop: 4,
                    alignItems: 'center',
                  }}>
                  <StoreSmallIcon />
                  <Sys.Text
                    style={{
                      lineHeight: 16,
                      marginLeft: 4,
                      flex: 1,
                    }}>
                    {item?.agency?.name}
                  </Sys.Text>
                </View>
              </View>
            </TouchableOpacity>
          );
        }}
        keyExtractor={(item, index) => index.toString()}
      />
    );
  }, [posList, currentDay]);

  const calendarData = useMemo(() => {
    try {
      const daysInMonth = month.daysInMonth();
      const dayOfWeek = month.startOf('month').isoWeekday(); // Đảm bảo sử dụng isoWeekday() cho tuần bắt đầu từ thứ Hai
      const temp = [];

      for (let i = 1; i < dayOfWeek; i++) {
        temp.push({field: 'NoDate'});
      }

      for (let index = 1; index <= daysInMonth; index++) {
        const dayData = posList.find(
          x =>
            moment(x.plan.date, 'YYYY-MM-DD').format('DD') ===
            String(index).padStart(2, '0'),
        );
        temp.push({
          field: index,
          value: index - 1,
          day: dayData?.plan?.date
            ? moment(dayData.plan.date, 'YYYY-MM-DD').format('DD')
            : null,
          checkin_status: isCheckedIn(dayData?.plan?.checkin_status),
        });
      }

      return temp;
    } catch (error) {
      console.log({error});
      return [];
    }
  }, [month, posList]);

  return (
    <View style={style}>
      <View
        style={{
          marginBottom: 20,
        }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <View>
            <Sys.Text
              style={{
                fontWeight: '600',
              }}>
              {month.format('MM/YYYY')}
            </Sys.Text>
          </View>
          <View
            style={{
              height: 1,
              flex: 1,
              backgroundColor: '#E2E2EA',
              marginHorizontal: 20,
            }}
          />
          <View
            style={{
              flexDirection: 'row',
              width: 70,
              justifyContent: 'space-between',
            }}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => addMonth(-1)}
              style={{
                borderWidth: 1,
                borderColor: '#E2E2EA',
                borderRadius: 8,
                width: 28,
                height: 24,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <LeftPlanIcon />
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => addMonth(1)}
              style={{
                borderWidth: 1,
                borderColor: '#E2E2EA',
                borderRadius: 8,
                width: 28,
                height: 24,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <RightPlanIcon />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      <View
        style={{
          flex: 0,
        }}>
        <FlatList
          scrollEnabled={false}
          horizontal
          data={['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN']}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({item, index}) => {
            return (
              <View
                style={{
                  width: (width - 60) / 7,
                  marginBottom: 5,
                }}>
                <Sys.Text
                  style={{
                    textAlign: 'center',
                    fontWeight: '700',
                    color: '#92929D',
                  }}>
                  {item}
                </Sys.Text>
              </View>
            );
          }}
        />
        <RenderCalendar
          posList={posList}
          setCurrentDay={setCurrentDay}
          currentDay={currentDay}
          data={calendarData}
        />
      </View>
      <View
        style={{
          flex: 1,
        }}>
        {renderList}
      </View>
    </View>
  );
};

export default DatePlan;

const RenderCalendar = ({data, currentDay, setCurrentDay, posList}) => {
  // Hàm render dấu chấm trạng thái
  const renderStatusDot = (hasDay, isCurrentDay, checkinStatus) => {
    return (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          position: 'absolute',
          bottom: 4,
        }}>
        <View
          style={{
            width: 4,
            height: 4,
            borderRadius: 5,
            backgroundColor: hasDay
              ? checkinStatus
                ? Color.green
                : Color.blue
              : Color.white,
          }}
        />
      </View>
    );
  };

  return (
    <FlatList
      scrollEnabled={false}
      data={data}
      numColumns={7}
      keyExtractor={(item, index) => index.toString()}
      renderItem={({item, index}) => {
        if (item.field === 'NoDate') {
          return (
            <View
              style={{
                padding: 5,
                width: (width - 60) / 7,
                height: (width - 60) / 7,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <View
                style={{
                  borderRadius: 10,
                  width: (width - 150) / 7,
                  height: (width - 150) / 7,
                  backgroundColor: Color.white,
                }}
              />
            </View>
          );
        }

        const isCurrentDay = Number(currentDay) === Number(item.field);
        const hasDay = item.field == item.day;
        return (
          <TouchableOpacity
            onPress={() => setCurrentDay(item.field)}
            style={{
              padding: 5,
              width: (width - 60) / 7,
              height: (width - 60) / 7,
            }}>
            <View
              style={[
                {
                  borderRadius: 10,
                  width: (width - 150) / 7,
                  height: (width - 150) / 7,
                  justifyContent: 'center',
                  alignItems: 'center',
                  overflow: 'hidden',
                  backgroundColor: isCurrentDay
                    ? item.checkin_status
                      ? Color.green
                      : Color.blue
                    : Color.white,
                },
              ]}>
              <Sys.Text
                style={{
                  color: isCurrentDay ? Color.white : Color.black,
                  fontWeight: isCurrentDay ? '700' : '400',
                  fontSize: 13,
                }}>
                {item.field}
              </Sys.Text>
              {hasDay &&
                renderStatusDot(hasDay, isCurrentDay, item.checkin_status)}
            </View>
          </TouchableOpacity>
        );
      }}
    />
  );
};

const isCheckedIn = status => {
  return [2, 3].includes(status);
};
