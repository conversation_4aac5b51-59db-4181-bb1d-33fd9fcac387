import {useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useCallback, useEffect, useState} from 'react';
import {FlatList, TouchableOpacity, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import {openGoogleMaps} from '../checkout/Checkout.Infomation';
import {PosActions} from '../sale-location/services/pos.slice';
import LocationSmallIcon from './icons/LocationSmallIcon';
import StoreSmallIcon from './icons/StoreSmallIcon';
import SaleLocationSkeleton from '../sale-location/SaleLocationSkeleton';
import {sortByDateAscending} from '../../services/util';

const PreMonth = ({currentTab, body, setStatus}) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [posList, setPosList] = useState([]);
  const [page, setPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);

  const {view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);

  useEffect(() => {
    if (currentTab === 0) {
      fetchData(1);
    }
  }, [currentTab, body]);

  const fetchData = useCallback(
    pageNum => {
      setLoading(true);
      dispatch(
        PosActions.getPosListByPage({
          onSuccess: rs => {
            if (pageNum === 1) {
              let sortedData = sortByDateAscending(rs?.data);
              setPosList(sortedData);
            } else {
              setPosList(prevPosList =>
                sortByDateAscending([...prevPosList, ...rs?.data]),
              );
            }
            setPage(pageNum);
            setLastPage(rs?.meta?.last_page);
            setStatus(rs?.data[0]?.plan?.plan_status_label || '');
            setLoading(false);
            setLoadingMore(false);
          },
          onFail: () => {
            setLoading(false);
            setLoadingMore(false);
          },
          body: {
            ...body,
            staff_owner: true,
            plan_month: moment().format('YYYY-MM'),
            page: pageNum,
          },
        }),
      );
    },
    [body],
  );

  const loadMoreData = () => {
    if (!loadingMore && page < lastPage) {
      setLoadingMore(true);
      fetchData(page + 1);
    }
  };
  console.log({loading, loadingMore});
  return (
    <View>
      <FlatList
        ListFooterComponent={
          loading ? (
            !loadingMore ? (
              <>
                <SaleLocationSkeleton />
                <SaleLocationSkeleton />
                <SaleLocationSkeleton />
                <SaleLocationSkeleton />
                <SaleLocationSkeleton />
                <SaleLocationSkeleton />
              </>
            ) : (
              <SaleLocationSkeleton />
            )
          ) : null
        }
        ListEmptyComponent={
          !loading &&
          !posList.length && (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                marginVertical: 50,
              }}>
              <Sys.Text
                style={{
                  color: '#00000080',
                }}>
                Không có dữ liệu
              </Sys.Text>
            </View>
          )
        }
        showsVerticalScrollIndicator={false}
        data={posList}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item, index}) => {
          return <OneItem item={item} index={index} />;
        }}
        onEndReachedThreshold={0.2}
        onEndReached={loadMoreData}
        contentContainerStyle={{paddingBottom: 50}}
      />
    </View>
  );
};

export default PreMonth;
const OneItem = ({item, index}) => {
  const {navigate} = useNavigation();

  return (
    <TouchableOpacity
      onPress={() =>
        navigate('SaleInformationScreen', {
          ...item,
        })
      }
      style={{
        backgroundColor: 'white',
        padding: 10,
      }}>
      <View
        style={{
          flexDirection: 'row',
        }}>
        <View>
          <View
            style={{
              borderRadius: 50,
              height: 48,
              width: 48,
              backgroundColor:
                item?.plan?.checkin_status !== 1 ? '#90BB3B' : '#0062FF',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            {item?.plan?.date ? (
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Sys.Text
                  style={{
                    fontWeight: '700',
                    color: 'white',
                    lineHeight: 12,
                    fontSize: 12,
                  }}>
                  {moment(item?.plan?.date, 'YYYY-MM-DD HH:mm:ss').format('DD')}
                </Sys.Text>
                <Sys.Text
                  style={{
                    fontSize: 8,
                    fontWeight: '700',
                    color: 'white',
                    lineHeight: 12,
                  }}>
                  {moment(item?.plan?.date, 'YYYY-MM-DD HH:mm:ss').format(
                    'MM/YYYY',
                  )}
                </Sys.Text>
              </View>
            ) : (
              <></>
            )}
          </View>
          {item?.plan?.checkin_status !== 1 ? (
            <Sys.Text
              style={{
                fontSize: 8,
                color: '#848484',
                marginTop: 2,
              }}>
              Đã ghé thăm
            </Sys.Text>
          ) : (
            <></>
          )}
        </View>
        <View
          style={{
            flex: 1,
          }}>
          <View
            style={{
              marginLeft: 15,
              flex: 1,
            }}>
            <View>
              <Sys.Text
                style={{
                  color: '#0062FF',
                  fontWeight: '700',
                  lineHeight: 16,
                }}>
                {item?.posCode}{' '}
                {item?.staff && (
                  <Sys.Text
                    style={{
                      color: '#ccc',
                      fontWeight: '400',
                    }}>
                    ({item?.staff?.name})
                  </Sys.Text>
                )}
              </Sys.Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
                marginTop: 4,
                alignItems: 'flex-start',
              }}>
              <LocationSmallIcon />
              <View style={{flex: 1, marginLeft: 4}}>
                <Sys.Text style={{lineHeight: 16, fontSize: 12}}>
                  {item?.address}{' '}
                </Sys.Text>

                <TouchableOpacity
                  onPress={() => {
                    openGoogleMaps(item?.lat, item?.lon);
                  }}>
                  <Sys.Text
                    style={{
                      fontSize: 12,
                      color: '#0062FF',
                      top: 2,
                      marginLeft: 4,
                    }}>
                    (Chỉ đường)
                  </Sys.Text>
                </TouchableOpacity>
              </View>
            </View>
            <View
              style={{
                flexDirection: 'row',
                marginTop: 4,
                alignItems: 'center',
              }}>
              <StoreSmallIcon />
              <Sys.Text
                style={{
                  lineHeight: 16,
                  marginLeft: 4,
                  flex: 1,
                }}>
                {item?.agency?.name}
              </Sys.Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};
