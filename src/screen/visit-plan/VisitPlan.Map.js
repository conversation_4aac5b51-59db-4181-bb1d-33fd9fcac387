import moment from 'moment';
import React, {useEffect, useMemo, useState} from 'react';
import {Image, TouchableOpacity, useWindowDimensions, View} from 'react-native';
import MapView, {<PERSON><PERSON>, PROVIDER_GOOGLE} from 'react-native-maps';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import {AccountSelectors} from '../account/services/account.slice';
import {PosActions, PosSelectors} from '../sale-location/services/pos.slice';
import {AppSelectors} from '../../app.slice';
import {openGoogleMaps} from '../checkout/Checkout.Infomation';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useNavigation} from '@react-navigation/native';
import ReactNativeModal from 'react-native-modal';

const VisitPlanMap = () => {
  const dispatch = useDispatch();
  const [currentTab, setCurrentTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [posList, setPosList] = useState([]);
  const profile = useSelector(AccountSelectors.profile);
  const avatar = useSelector(AccountSelectors.avatar);
  const body = useSelector(PosSelectors.body);

  const {location, extras} = useSelector(AppSelectors.current_location);

  // useEffect(() => {
  //   checkLocationAgain();
  // }, []);

  // const checkLocationAgain = useCallback(() => {
  //   getCurrentLocation({
  //     highAccuracy: false,
  //     setLocation,
  //   });
  // }, []);

  useEffect(() => {
    if (profile) {
      if (!loading) {
        setLoading(true);
        dispatch(
          PosActions.getPlanCurrentMonth({
            onSuccess: rs => {
              setPosList(rs?.data);
              setLoading(false);
            },
            onFail: rs => {
              setLoading(false);
            },
            body,
          }),
        );
      }
    }
  }, [currentTab, profile, body]);
  const tabList = [
    {
      name: 'Hạn mức',
    },
    {
      name: 'Doanh thu',
    },
    {
      name: 'Ghé thăm',
    },
  ];

  const renderTab = useMemo(() => {
    return tabList.map((x, index) => {
      return (
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: index === currentTab ? '#0062FF' : 'white',
            borderRadius: 5,
          }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => setCurrentTab(index)}
            key={x.name}>
            <Sys.Text
              style={{
                padding: 10,
                textAlign: 'center',
                color: index === currentTab ? 'white' : '#001451',
              }}>
              {x.name}
            </Sys.Text>
          </TouchableOpacity>
        </View>
      );
    });
  }, [currentTab, posList]);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: 'white',
      }}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderWidth: 1,
          borderColor: '#f0f0f0',
          margin: 10,
          borderRadius: 10,
          padding: 2,
        }}>
        {renderTab}
      </View>
      <View
        style={{
          flex: 1,
        }}>
        <View style={{flex: 1}}>
          <MapView
            provider={PROVIDER_GOOGLE} // remove if not using Google Maps
            style={{
              width: '100%',
              height: '100%',
            }}
            region={{
              latitude: location?.latitude,
              longitude: location?.longitude,
              latitudeDelta: location?.latitude * 0.001,
              longitudeDelta: location?.longitude * 0.001,
            }}>
            {location?.latitude ? (
              <Marker
                coordinate={{
                  latitude: location?.latitude,
                  longitude: location?.longitude,
                }}>
                <Image
                  style={{
                    height: 30,
                    width: 30,
                    borderRadius: 50,
                  }}
                  source={{uri: avatar}}
                />
              </Marker>
            ) : (
              <></>
            )}
            {posList.map((x, index) => {
              return (
                <OneMarker
                  key={`${index}`}
                  x={x}
                  index={`market-${index}`}
                  tab={currentTab}
                />
              );
            })}
          </MapView>
        </View>
      </View>
    </View>
  );
};

export default VisitPlanMap;
const OneMarker = ({x, index, tab}) => {
  const settings = useSelector(AppSelectors.settings);
  const threshold_amount = settings?.pos_warn?.threshold_amount;
  const insets = useSafeAreaInsets();
  const [isShow, setIsShow] = useState(false);
  const {width} = useWindowDimensions();
  const {navigate} = useNavigation();

  const handleColor = () => {
    try {
      if (tab === 0) {
        if (x?.start_threshold_amount < threshold_amount) {
          return 'red';
        } else {
          return 'blue';
        }
      }
      if (tab === 1) {
        if (x?.recent_revenue <= 0) {
          return 'red';
        } else {
          return 'blue';
        }
      }
      if (tab === 2) {
        // Lấy ngày hiện tại
        const currentDate = moment();

        // Lấy ngày quá khứ (ví dụ: 1995-12-25)
        const pastDate = moment(x?.last_checkin, 'YYYY-MM-DD');

        // Tính số ngày chênh lệch
        const daysDifference = currentDate.diff(pastDate, 'days');
        if (daysDifference >= 90 && daysDifference <= 120) {
          return 'orange';
        }
        if (daysDifference < 90) {
          return 'blue';
        }
        if (daysDifference > 120) {
          return 'red';
        }
      }
      return undefined;
    } catch (error) {
      return undefined;
    }
  };

  return (
    <View>
      <ReactNativeModal
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={{
          padding: 0,
          margin: 0,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onBackdropPress={() => setIsShow(false)}
        isVisible={isShow}
        onSwipeComplete={() => {
          setIsShow(false);
        }}
        swipeDirection="down">
        <View
          style={{
            flex: 1,
          }}
        />
        <View
          style={{
            backgroundColor: 'white',
            width: width,
            borderRadius: 20,
            padding: 20,
            paddingBottom: insets.bottom + 20,
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
            }}>
            {x?.posCode}
          </Sys.Text>
          <View style={{paddingTop: 20}}>
            <Sys.Text>{x?.agency?.name}</Sys.Text>
            <Sys.Text
              style={{
                marginTop: 10,
              }}>
              {x?.address}
            </Sys.Text>
            <TouchableOpacity
              onPress={() => {
                openGoogleMaps(x?.lat, x?.lon);
              }}>
              <Sys.Text
                style={{
                  color: '#0062FF',
                  marginTop: 10,
                }}>
                Chỉ đường
              </Sys.Text>
            </TouchableOpacity>
            <View
              style={{
                marginTop: 20,
              }}>
              <Sys.Button
                onPress={() => {
                  setIsShow(false);
                  navigate('SaleInformationScreen', {
                    ...x,
                  });
                }}>
                Xem chi tiết
              </Sys.Button>
            </View>
          </View>
        </View>
      </ReactNativeModal>
      <Marker
        pinColor={handleColor()}
        onPress={() => {
          setIsShow(true);
        }}
        key={`${index}`}
        coordinate={{
          latitude: x?.lat,
          longitude: x?.lon,
        }}
      />
    </View>
  );
};
