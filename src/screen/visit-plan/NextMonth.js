import {useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useCallback, useEffect, useState} from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {Calendar} from 'react-native-calendars';
import ReactNativeModal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {SwipeListView} from 'react-native-swipe-list-view';
import Toast from 'react-native-toast-message';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import {openGoogleMaps} from '../checkout/Checkout.Infomation';
import {PosActions} from '../sale-location/services/pos.slice';
import DeleteIcon from './icons/DeleteIcon';
import LocationSmallIcon from './icons/LocationSmallIcon';
import PlanIcon from './icons/PlanIcon';
import SendIcon from './icons/SendIcon';
import StoreSmallIcon from './icons/StoreSmallIcon';
import SaleLocationSkeleton from '../sale-location/SaleLocationSkeleton';
import Color from '../../components/theme/Color';
import {sortByDateAscending} from '../../services/util';
import _ from 'lodash';

const NextMonth = ({currentTab, body, setStatus}) => {
  const insets = useSafeAreaInsets();
  const dispatch = useDispatch();
  const [posList, setPosList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);
  const {view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);

  useEffect(() => {
    if (currentTab === 2) {
      fetchData(1);
    }
  }, [currentTab, body]);

  const fetchData = useCallback(
    pageNum => {
      setLoading(true);
      dispatch(
        PosActions.getPosListByPage({
          onSuccess: rs => {
            if (pageNum === 1) {
              let sortedData = sortByDateAscending(rs?.data);
              setPosList(sortedData);
            } else {
              setPosList(prevPosList =>
                sortByDateAscending([...prevPosList, ...rs?.data]),
              );
            }
            setPage(pageNum);
            setLastPage(rs?.meta?.last_page);
            setStatus(rs?.data[0]?.plan?.plan_status_label || '');
            setLoading(false);
            setLoadingMore(false);
          },
          onFail: () => {
            setLoading(false);
            setLoadingMore(false);
          },
          body: {
            ...body,
            plan_month: moment().add(1, 'months').format('YYYY-MM'),
            page: pageNum,
            pageSize: 50,
          },
        }),
      );
    },
    [body],
  );
  const loadMoreData = () => {
    if (!loadingMore && page < lastPage) {
      setLoadingMore(true);
      fetchData(page + 1);
    }
  };

  const onRequest = useCallback(() => {
    if (posList.length > 0) {
      dispatch(
        PosActions.requestPlan({
          onSuccess: rs => {
            Toast.show({
              type: 'success',
              text1: 'Thông báo',
              text2: 'Gửi yêu cầu thành công',
            });
          },
          onFail: rs => {
            Toast.show({
              type: 'error',
              text1: 'Thông báo',
              text2: rs,
            });
          },
          planId: (() => {
            try {
              return posList[0].plan?.plan_id;
            } catch (error) {
              return 0;
            }
          })(),
        }),
      );
    }
  }, [posList]);

  const onRequestDelete = item => {
    dispatch(
      PosActions.deletePlan({
        onSuccess: rs => {
          Toast.show({
            type: 'success',
            text1: 'Thông báo',
            text2: 'Xóa kế hoạch thành công',
          });
          setPosList(posList.filter(x => x.posCode != item?.item?.posCode));
        },
        onFail: rs => {
          Toast.show({
            type: 'error',
            text1: 'Thông báo',
            text2: rs,
          });
        },

        body: {
          posId: item?.item?.posCode,
          planId: item?.item?.plan?.plan_id,
        },
      }),
    );
  };

  const renderFooter = () => {
    if (loading) {
      return !loadingMore ? (
        Array(6)
          .fill(null)
          .map((_, i) => <SaleLocationSkeleton key={i} />)
      ) : (
        <SaleLocationSkeleton />
      );
    }
    return null;
  };

  const renderEmptyComponent = () => {
    if (!loading && !posList.length) {
      return (
        <View style={styles.centeredView}>
          <Sys.Text style={styles.noDataText}>Không có dữ liệu</Sys.Text>
        </View>
      );
    }
    return null;
  };

  const getPlanStatusId =
    _.isArray(posList) && posList[0]?.plan?.plan_status_id
      ? posList[0]?.plan?.plan_status_id
      : 0;

  const getPlanStatusLabel =
    _.isArray(posList) && posList[0]?.plan?.plan_status_label
      ? posList[0]?.plan?.plan_status_label
      : '';

  return (
    <View style={styles.container}>
      <SwipeListView
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmptyComponent}
        showsVerticalScrollIndicator={false}
        data={posList}
        keyExtractor={(item, index) => index.toString()}
        onEndReachedThreshold={0.2}
        onEndReached={loadMoreData}
        contentContainerStyle={styles.contentContainer}
        renderItem={({item, index}) => (
          <OneItem item={item} index={index} onReload={() => {}} />
        )}
        renderHiddenItem={(item, rowMap) =>
          item?.item?.plan?.plan_status_id === 1 && (
            <View style={styles.hiddenItem}>
              <TouchableOpacity
                onPress={() => {
                  rowMap[item?.index].closeRow();
                  onRequestDelete(item);
                }}
                style={styles.deleteButton}>
                <DeleteIcon />
                <Sys.Text style={styles.deleteButtonText}>
                  Xóa khỏi kế hoạch
                </Sys.Text>
              </TouchableOpacity>
            </View>
          )
        }
        rightOpenValue={-75}
      />
      {isStaff &&
        posList?.length > 0 &&
        (getPlanStatusId === 1 ? (
          <View style={styles.submitButtonContainer}>
            <TouchableOpacity onPress={onRequest} style={[styles.submitButton]}>
              <SendIcon />
              <Sys.Text style={[styles.submitButtonText]}>
                Gửi đề xuất kế hoạch
              </Sys.Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View
            style={[
              styles.statusBar,
              {height: 50 + insets.bottom, bottom: -insets.bottom},
              getPlanStatusId === 2
                ? {backgroundColor: Color.yellow}
                : {backgroundColor: Color.green},
            ]}>
            <Sys.Text style={styles.statusText}>{getPlanStatusLabel}</Sys.Text>
          </View>
        ))}
    </View>
  );
};

export default NextMonth;

const OneItem = ({item, index, onReload}) => {
  const {navigate} = useNavigation();
  const [isShow, setIsShow] = useState(false);
  const {width} = useWindowDimensions();
  const insets = useSafeAreaInsets();
  const dispatch = useDispatch();
  const onCreatePlan = date => {
    dispatch(
      PosActions.planCreate({
        onSuccess: rs => {
          Toast.show({
            type: 'success',
            text1: 'Thông báo',
            text2: 'Thêm kế hoạch thành công',
          });
          setIsShow(false);
          onReload();
        },
        onFail: rs => {
          setIsShow(false);
          Toast.show({
            type: 'success',
            text1: 'Thông báo',
            text2: rs?.error?.message,
          });
        },
        posCode: item?.posCode,
        date,
      }),
    );
  };

  return (
    <View>
      <ReactNativeModal
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={{
          padding: 0,
          margin: 0,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onBackdropPress={() => setIsShow(false)}
        isVisible={isShow}
        onSwipeComplete={() => {
          setIsShow(false);
        }}
        swipeDirection="down">
        <View
          style={{
            flex: 1,
          }}
        />
        <View
          style={{
            backgroundColor: 'white',
            width: width,
            borderRadius: 20,
            padding: 20,
            paddingBottom: insets.bottom + 20,
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
            }}>
            Thêm vào kế hoạch
          </Sys.Text>
          <Calendar
            hideArrows
            initialDate={moment()
              .add('M', 1)
              .startOf('month')
              .format('YYYY-MM-DD')}
            minDate={moment().add('M', 1).startOf('month').format('YYYY-MM-DD')}
            maxDate={moment().add('M', 1).endOf('month').format('YYYY-MM-DD')}
            onDayPress={day => {
              onCreatePlan(day?.dateString);
            }}
          />
        </View>
      </ReactNativeModal>
      <TouchableOpacity
        activeOpacity={1}
        onPress={() =>
          navigate('SaleInformationScreen', {
            ...item,
          })
        }
        style={{
          backgroundColor: 'white',
          padding: 10,
        }}>
        <View
          style={{
            flexDirection: 'row',
          }}>
          <TouchableOpacity
            onPress={() => {
              setIsShow(true);
            }}>
            {item?.plan?.date ? (
              <View
                style={{
                  borderRadius: 50,
                  height: 48,
                  width: 48,
                  backgroundColor: '#0062FF',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Sys.Text
                  style={{
                    fontWeight: '700',
                    color: 'white',
                    lineHeight: 12,
                    fontSize: 12,
                  }}>
                  {moment(item?.plan?.date, 'YYYY-MM-DD').format('DD')}
                </Sys.Text>
                <Sys.Text
                  style={{
                    fontSize: 8,
                    fontWeight: '700',
                    color: 'white',
                    lineHeight: 12,
                  }}>
                  {moment(item?.plan?.date, 'YYYY-MM-DD').format('MM/YYYY')}
                </Sys.Text>
              </View>
            ) : (
              <View>
                <View
                  style={{
                    borderRadius: 50,
                    height: 48,
                    width: 48,
                    backgroundColor: '#001451',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <PlanIcon />
                </View>
                <TouchableOpacity
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: 5,
                    backgroundColor: '#0062FF',
                    borderRadius: 50,
                    paddingVertical: 2,
                    paddingHorizontal: 5,
                  }}>
                  <Sys.Text
                    style={{
                      fontSize: 8,
                      color: 'white',
                    }}>
                    Đặt lịch đi
                  </Sys.Text>
                </TouchableOpacity>
              </View>
            )}
          </TouchableOpacity>
          <View
            style={{
              flex: 1,
            }}>
            <View
              style={{
                marginLeft: 15,
                flex: 1,
              }}>
              <View>
                <Sys.Text
                  style={{
                    color: '#0062FF',
                    fontWeight: '700',
                    lineHeight: 16,
                  }}>
                  {item?.posCode}{' '}
                  {item?.staff && (
                    <Sys.Text
                      style={{
                        fontWeight: '400',
                      }}>
                      ({item?.staff?.name})
                    </Sys.Text>
                  )}
                </Sys.Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  marginTop: 4,
                  alignItems: 'flex-start',
                }}>
                <LocationSmallIcon />
                <View style={{flex: 1, marginLeft: 4}}>
                  <Sys.Text style={{lineHeight: 16, fontSize: 12}}>
                    {item?.address}{' '}
                  </Sys.Text>
                  <TouchableOpacity
                    onPress={() => {
                      openGoogleMaps(item?.lat, item?.lon);
                    }}>
                    <Sys.Text
                      style={{
                        fontSize: 12,
                        color: '#0062FF',
                        top: 2,
                        marginLeft: 4,
                      }}>
                      (Chỉ đường)
                    </Sys.Text>
                  </TouchableOpacity>
                </View>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  marginTop: 4,
                  alignItems: 'center',
                }}>
                <StoreSmallIcon />
                <Sys.Text
                  style={{
                    lineHeight: 16,
                    marginLeft: 4,
                    flex: 1,
                  }}>
                  {item?.agency?.name}
                </Sys.Text>
              </View>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1},
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 50,
  },
  noDataText: {color: '#00000080'},
  contentContainer: {paddingBottom: 50},
  hiddenItem: {
    justifyContent: 'center',
    alignItems: 'flex-end',
    flex: 1,
    backgroundColor: 'white',
  },
  deleteButton: {
    width: 75,
    padding: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteButtonText: {
    fontSize: 10,
    color: '#848484',
    textAlign: 'center',
    marginTop: 2,
  },
  submitButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
  submitButton: {
    flexDirection: 'row',
    backgroundColor: '#0062FF',
    margin: 6,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 50,
  },
  submitButtonText: {color: '#FFF', marginLeft: 10},
  statusBar: {backgroundColor: '#90BB3B', paddingTop: 17},
  statusText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
    textAlign: 'center',
  },
});
