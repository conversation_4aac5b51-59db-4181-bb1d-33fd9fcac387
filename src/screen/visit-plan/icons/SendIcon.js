import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const SendIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}>
    <Path
      fill="#fff"
      fillRule="evenodd"
      d="m9.667 16.801-.708 1.14a.785.785 0 0 1-.167.19c-.72.595-1.82.562-2.497-.077-.356-.337-.546-.8-.518-1.231l-.001-3.007-3.478-2.795a2.517 2.517 0 0 1-.767-1.042 2.506 2.506 0 0 1 1.457-3.255L16.88 1.42a.77.77 0 0 1 .717.071.78.78 0 0 1 .333.874L14.14 15.852c-.374 1.33-1.775 2.11-3.13 1.747a2.568 2.568 0 0 1-.933-.47l-.411-.328Zm-1.241-.988-1.069-.852v1.91c0 .**************.**************.088.316.05l.722-1.162Zm-.653-2.527 3.297 2.626a.978.978 0 0 0 .356.18c.518.138 1.051-.158 1.191-.658l2.924-10.41-7.768 8.262Zm-1.293-.919L14.257 4.1 3.558 8.18a.949.949 0 0 0-.262 1.628l3.184 2.558Z"
      clipRule="evenodd"
    />
  </Svg>
);
export default SendIcon;
