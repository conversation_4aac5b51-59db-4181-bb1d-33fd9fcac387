import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const PlanIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}>
    <Path
      fill="#fff"
      fillRule="evenodd"
      d="M8.495 5.8a1 1 0 0 1-1.99 0H4v1.6h16V5.8h-1.605a1 1 0 0 1-1.99 0h-7.91ZM6.5 3.8V3a1 1 0 0 1 2 0v.8h7.9V3a1 1 0 1 1 2 0v.8h1.7c1.05 0 1.9.85 1.9 1.9V19a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V5.7c0-1.05.85-1.9 1.9-1.9h2.6ZM20 9.4H4V19a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V9.4ZM6.6 13a1 1 0 1 1 0-2h3.6a1 1 0 1 1 0 2H6.6Zm0 3.6a1 1 0 1 1 0-2h7.2a1 1 0 1 1 0 2H6.6Z"
      clipRule="evenodd"
    />
  </Svg>
);
export default PlanIcon;
