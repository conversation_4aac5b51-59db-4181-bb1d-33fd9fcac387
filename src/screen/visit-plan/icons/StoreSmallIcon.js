import * as React from 'react';
import Svg, {Circle, Path} from 'react-native-svg';
const StoreSmallIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={18}
    height={18}
    fill="none"
    {...props}>
    <Circle cx={9} cy={9} r={8.5} fill="#001451" stroke="#F1F1F1" />
    <Path
      stroke="#F1F1F1"
      strokeWidth={0.5}
      d="M5.143 8.143V12a.857.857 0 0 0 .857.857h6a.857.857 0 0 0 .857-.857V8.143"
    />
    <Path
      stroke="#F1F1F1"
      strokeMiterlimit={16}
      strokeWidth={0.5}
      d="M10.214 12.857v-2.571a.857.857 0 0 0-.857-.857H8.5a.857.857 0 0 0-.857.857v2.571"
    />
    <Path
      stroke="#F1F1F1"
      strokeWidth={0.5}
      d="m13.208 7.87-.726-2.54a.257.257 0 0 0-.248-.187H10.5l.204 2.444a.247.247 0 0 0 .119.193c.167.1.493.284.748.363.436.134 1.072.086 1.434.041a.245.245 0 0 0 .203-.314Z"
    />
    <Path
      stroke="#F1F1F1"
      strokeWidth={0.5}
      d="M9.857 8.143c.244-.075.552-.246.725-.348a.248.248 0 0 0 .12-.235L10.5 5.143h-3L7.299 7.56a.248.248 0 0 0 .12.235c.172.102.48.273.724.348.64.197 1.074.197 1.714 0Z"
    />
    <Path
      stroke="#F1F1F1"
      strokeWidth={0.5}
      d="m5.518 5.33-.726 2.54a.244.244 0 0 0 .203.314c.362.045.998.093 1.434-.041.255-.079.582-.263.748-.362a.248.248 0 0 0 .12-.194L7.5 5.143H5.766a.257.257 0 0 0-.248.186Z"
    />
  </Svg>
);
export default StoreSmallIcon;
