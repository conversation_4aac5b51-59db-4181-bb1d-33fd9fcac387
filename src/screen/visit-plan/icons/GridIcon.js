import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const GridIcon = props => {
  const {color = '#D1D1D1'} = props;
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={29}
      fill="none"
      {...props}>
      <Path
        fill={color}
        fillRule="evenodd"
        d="M8.3 16.13v-4.003H4v4.003h4.3Zm2 0H20v-4.003h-9.7v4.003Zm-2-11.42H5c-.552 0-1 .527-1 1.177v3.885h4.3V4.71Zm2 0v5.062H20V5.887c0-.65-.448-1.178-1-1.178h-8.7Zm-2 18.837v-5.062H4v3.885c0 .65.448 1.177 1 1.177h3.3Zm2 0H19c.552 0 1-.527 1-1.177v-3.885h-9.7v5.062ZM2 22.37V5.887c0-1.95 1.343-3.532 3-3.532h14c1.657 0 3 1.581 3 3.532V22.37c0 1.95-1.343 3.532-3 3.532H5c-1.657 0-3-1.581-3-3.532Z"
        clipRule="evenodd"
      />
    </Svg>
  );
};
export default GridIcon;
