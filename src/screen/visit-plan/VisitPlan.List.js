import moment from 'moment';
import React, {useEffect, useMemo} from 'react';
import {TouchableOpacity, View} from 'react-native';
import * as Ant from 'react-native-animatable';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import {PosActions} from '../sale-location/services/pos.slice';
import NextMonth from './NextMonth';
import Personal from './Personal';
import PreMonth from './PreMonth';

const VisitPlanList = ({body, setCurrentTab, currentTab, setStatus}) => {
  const dispatch = useDispatch();

  const {view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);

  useEffect(() => {
    if (currentTab === 0) {
      dispatch(
        PosActions.setBody({
          ...body,
          plan_month: moment().format('YYYY-MM'),
        }),
      );
    }
    if (currentTab === 2) {
      dispatch(
        PosActions.setBody({
          ...body,
          plan_month: moment().add('months', 1).format('YYYY-MM'),
        }),
      );
    }
  }, [currentTab]);

  const tabList = [
    {
      name: `Tháng ${moment().format('MM/YYYY')}`,
      content: (
        <PreMonth currentTab={currentTab} body={body} setStatus={setStatus} />
      ),
    },
    {
      name: isStaff ? 'Cá nhân' : '',
      content: isStaff ? <Personal body={body} setStatus={setStatus} /> : <></>,
    },
    {
      name: `Tháng ${moment().add('M', 1).format('MM/YYYY')}`,
      content: (
        <NextMonth currentTab={currentTab} body={body} setStatus={setStatus} />
      ),
    },
  ];

  const renderTab = useMemo(() => {
    return tabList.map((x, index) => {
      return (
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            borderBottomWidth: 1,
            borderBottomColor: '#F1F1F5',
            marginBottom: 5,
          }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => {
              if (index === 1 && !isStaff) {
                return;
              }
              setCurrentTab(index);
              dispatch(
                PosActions.setBody({
                  ...body,
                }),
              );
            }}
            key={x.name}>
            <Sys.Text
              style={{
                padding: 10,
                textAlign: 'center',
                fontWeight: '600',
                lineHeight: 24,
                color: index === currentTab ? '#0062FF' : 'black',
              }}>
              {x.name}
            </Sys.Text>
            {index === currentTab ? (
              <Ant.View
                currentTab
                duration={300}
                animation={'zoomIn'}
                style={{
                  height: 4,
                  backgroundColor: '#0062FF',
                  borderTopEndRadius: 20,
                  borderTopStartRadius: 20,
                }}
              />
            ) : (
              <Ant.View
                currentTab
                duration={300}
                animation={'zoomIn'}
                style={{
                  height: 4,
                  backgroundColor: 'white',
                  borderTopEndRadius: 20,
                  borderTopStartRadius: 20,
                }}
              />
            )}
          </TouchableOpacity>
        </View>
      );
    });
  }, [currentTab, body]);

  const renderTabContent = useMemo(() => {
    return tabList[currentTab].content;
  }, [currentTab, body]);

  return (
    <View
      style={{
        flex: 1,
      }}>
      <View
        style={{
          backgroundColor: 'white',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {renderTab}
      </View>
      <View
        style={{
          flex: 1,
        }}>
        {renderTabContent}
      </View>
    </View>
  );
};

export default VisitPlanList;
