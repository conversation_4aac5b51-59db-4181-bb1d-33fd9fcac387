import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useMemo, useState} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import FilterIcon from '../notification/icons/FilterIcon';
import {PosActions, PosSelectors} from '../sale-location/services/pos.slice';
import VisitPlanList from './VisitPlan.List';
import VisitPlanMap from './VisitPlan.Map';
import GridIcon from './icons/GridIcon';
import MapIcon from './icons/MapIcon';
import Color from '../../components/theme/Color';

const VisitPlanScreen = () => {
  const dispatch = useDispatch();
  const [currentState, setCurrentState] = useState(true);
  const [currentTab, setCurrentTab] = useState(0);
  const navigation = useNavigation();
  const [status, setStatus] = useState('');
  const {view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);
  // const profile = useSelector(AccountSelectors.profile);
  const body = useSelector(PosSelectors.body);

  useEffect(() => {
    return () => {
      dispatch(PosActions.resetBody());
    };
  }, []);

  const renderContent = useMemo(() => {
    return (
      <View
        style={{
          flex: 1,
        }}>
        {currentState ? (
          <VisitPlanList
            setStatus={setStatus}
            body={body}
            currentTab={currentTab}
            setCurrentTab={setCurrentTab}
          />
        ) : (
          <VisitPlanMap body={body} />
        )}
      </View>
    );
  }, [body, currentState, currentTab]);

  return (
    <Sys.Container hasHeader backgroundColor="white">
      <Sys.Header
        right={
          <View>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('SaleLocationFilterScreen', {
                  body,
                  onAccept: newBody => {
                    dispatch(PosActions.setBody(newBody));
                  },
                });
              }}
              style={{
                paddingHorizontal: 20,
              }}>
              <FilterIcon />
            </TouchableOpacity>
          </View>
        }
      />
      <View
        style={{
          backgroundColor: '#f1f1f1',
          padding: 10,
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
        <View
          style={{
            flexDirection: 'row',
          }}>
          <Sys.Text
            style={{
              color: Color.second,
              fontWeight: '600',
              textTransform: 'uppercase',
            }}>
            KẾ HOẠCH GHÉ THĂM{' '}
          </Sys.Text>
          {isStaff && status && (
            <Sys.Text
              style={{
                color: Color.yellow,
                fontWeight: '600',
                textTransform: 'uppercase',
              }}>
              ({status})
            </Sys.Text>
          )}
        </View>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => setCurrentState(false)}>
            <MapIcon color={currentState ? '#0062FF' : '#D1D1D1'} />
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              marginLeft: 10,
            }}
            activeOpacity={0.8}
            onPress={() => setCurrentState(true)}>
            <GridIcon color={!currentState ? '#0062FF' : '#D1D1D1'} />
          </TouchableOpacity>
        </View>
      </View>
      {renderContent}
    </Sys.Container>
  );
};

export default VisitPlanScreen;
