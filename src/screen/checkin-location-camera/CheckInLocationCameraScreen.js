import {useNavigation} from '@react-navigation/native';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {
  Image,
  Platform,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import * as ANT from 'react-native-animatable';
import Modal from 'react-native-modal';
import {
  Camera,
  useCameraDevice,
  useCameraFormat,
} from 'react-native-vision-camera';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import CameraButtonIcon from './icons/CameraButtonIcon';
import ChangeCameraIcon from './icons/ChangeCameraIcon';
import RemoveIcon from './icons/RemoveIcon';
import RightIcon from './icons/RightIcon';
import {
  addPhotoToCurrentIndex,
  initializePhotos,
  removePhotoFromCurrentIndex,
} from '../device-list/services/inventory.slice';
import {
  getCameraHeight,
  isIOS,
  resizeAndCompressImage,
} from '../../services/util';
import {AppSelectors} from '../../app.slice';
import Color from '../../components/theme/Color';
import {Front} from '../../components/theme/FontFamily';

const CheckInLocationCameraScreen = () => {
  const {images} = useSelector(state => state.inventory);
  const {image_rules, checkin_config} = useSelector(AppSelectors.settings);

  const [cameraPosition, setCameraPosition] = useState('back');
  const device = useCameraDevice(cameraPosition, {
    physicalDevices: ['wide-angle-camera'],
  });

  const resolutionSettings = Platform.select({
    ios: {
      videoResolution: {
        width: image_rules?.max_width || 1920,
        height: image_rules?.max_height || 1080,
      },
    },
    android: {
      photoResolution: {
        width: image_rules?.max_width || 1920,
        height: image_rules?.max_height || 1080,
      },
    },
  });

  const cameraProps = Platform.select({
    ios: {video: true},
    android: {photo: true},
  });

  const format = useCameraFormat(device, [resolutionSettings]);
  const {width, height} = useWindowDimensions();
  const camera = useRef(null);

  const [isShow, setIsShow] = useState(true);
  const [isActive, setIsActive] = useState(false);
  const [action, setAction] = useState('fadeInRight');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [step, setStep] = useState(0);
  const {navigate, goBack} = useNavigation();
  const [isAnimating, setIsAnimating] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    if (checkin_config) {
      const checkinPhoto = checkin_config.filter(x => x.type === 'file');
      dispatch(initializePhotos(checkinPhoto));
      setStep(checkinPhoto.length);
    }
  }, [dispatch, checkin_config]);

  useEffect(() => {
    if (isShow) {
      setIsShow(false);
    }
  }, [currentIndex]);

  useEffect(() => {
    if (!isShow) {
      setTimeout(() => setIsShow(true), 100);
    }
  }, [isShow]);

  const isCameraReady = useRef(false);

  useEffect(() => {
    if (!isCameraReady.current && device && isShow) {
      isCameraReady.current = true;
    }
  }, [device, isShow]);

  const handleImageRemoval = useCallback(
    photoIndex => {
      dispatch(removePhotoFromCurrentIndex({index: currentIndex, photoIndex}));
    },
    [currentIndex, dispatch],
  );

  const onTakePhoto = useCallback(async () => {
    try {
      const photo = await camera.current.takeSnapshot({
        quality: image_rules.quality || 60,
      });
      if (photo && photo.path) {
        const uri = `file://${photo.path}`;
        const currentImageConfig = images[currentIndex];
        let result = {uri};
        const response = await fetch(uri);
        const blob = await response.blob();
        const fileSizeBytes = blob.size;
        const fileSizeKilobytes = fileSizeBytes / 1024;

        console.log(`Original file size: ${fileSizeKilobytes.toFixed(2)} KB`);

        // Check if the image needs to be resized or compressed
        if (
          (currentImageConfig && currentImageConfig.size < fileSizeKilobytes) ||
          image_rules.isWebp ||
          isIOS
        ) {
          result = await resizeAndCompressImage(
            uri,
            currentImageConfig?.max_width || 1920,
            currentImageConfig?.max_height || 1080,
            image_rules.quality || 60,
            image_rules.isWebp,
            photo.orientation,
          );

          // Fetch the new file size after resize
          const newUri = result.uri;
          const newResponse = await fetch(newUri);
          const newBlob = await newResponse.blob();
          const newFileSizeBytes = newBlob.size;
          const newFileSizeKilobytes = newFileSizeBytes / 1024;

          console.log(
            `Resized file size: ${newFileSizeKilobytes.toFixed(2)} KB`,
          );
        }

        if (currentImageConfig.photo.length < currentImageConfig.max) {
          dispatch(
            addPhotoToCurrentIndex({index: currentIndex, photo: result}),
          );
        }
      }
    } catch (error) {
      console.error('Failed to take photo', error);
    }
  }, [currentIndex, images]);

  const onNext = useCallback(() => {
    if (images[currentIndex].photo.length > 0) {
      if (currentIndex + 1 < step) {
        setAction('fadeInRight');
        setCurrentIndex(currentIndex + 1);
        setIsActive(false);
      } else {
        setIsActive(false);
        navigate('DeviceListScreen');
      }
    }
  }, [currentIndex, checkin_config, images, navigate]);

  useEffect(() => {
    if (currentIndex + 1 < step) {
      setIsActive(false);
    }
  }, [currentIndex]);

  const getDataFromObject = useCallback(
    fieldName => {
      try {
        return images[currentIndex][fieldName];
      } catch {
        return '';
      }
    },
    [currentIndex, images],
  );

  const currentPhotos = useMemo(() => {
    try {
      return images[currentIndex].photo;
    } catch {
      return [];
    }
  }, [images, currentIndex]);

  const handleAnimationStart = () => {
    setIsAnimating(true);
  };

  const handleAnimationEnd = () => {
    setTimeout(() => {
      setIsAnimating(false);
    }, 500);
  };
  const maxPhotosReached = useMemo(() => {
    const _currentPhotos = images[currentIndex]?.photo || [];
    const maxPhotosAllowed = images[currentIndex]?.max || 3;
    return _currentPhotos.length >= maxPhotosAllowed;
  }, [images, currentIndex]);

  const handleStartCamera = () => {
    isCameraReady.current && setIsActive(true);
  };

  const isDisable = !isActive || isAnimating || !isCameraReady.current;
  return (
    <Sys.Container
      hasHeader
      headerColor={'#848484'}
      footerColor={'#848484'}
      hasFooter
      backgroundColor={'#848484'}>
      <Sys.Header
        onBack={() => {
          if (currentIndex > 0) {
            setAction('fadeInLeft');
            setCurrentIndex(currentIndex - 1);
          } else {
            goBack();
          }
        }}
        color={'#00000000'}
        title={getDataFromObject('name')}
      />
      {isShow ? (
        <ANT.View
          onAnimationBegin={handleAnimationStart}
          onAnimationEnd={handleAnimationEnd}
          animation={action}
          duration={200}
          style={{flex: 1, backgroundColor: '#848484'}}>
          <View style={{flex: 1, paddingHorizontal: 20}}>
            <View
              style={{
                marginTop: 5,
                backgroundColor: 'white',
                paddingHorizontal: 10,
                paddingVertical: 10,
                borderRadius: 20,
                flexDirection: 'row',
              }}>
              <View style={{flex: 1}}>
                <Sys.Text
                  style={{
                    lineHeight: 20,
                    marginTop: 10,
                    marginBottom: 10,
                    color: '#001451',
                  }}>
                  {getDataFromObject('description')}
                </Sys.Text>
              </View>
              <View
                style={{
                  flex: 0,
                  borderRadius: 10,
                  width: 60,
                  height: 60,
                }}>
                <Image
                  source={{uri: getDataFromObject('illustration')}}
                  style={{width: 60, height: 60}}
                />
              </View>
            </View>
            <View
              style={{
                marginTop: 15,
                alignItems: 'center',
                backgroundColor: 'black',
                width: width - 40,
                height: getCameraHeight(),
                justifyContent: 'center',
              }}>
              {!isActive ? (
                <TouchableOpacity
                  onPress={handleStartCamera}
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: Color.blue,
                    borderRadius: 10,
                    padding: 15,
                  }}>
                  <Sys.Text
                    style={[
                      Front.bold,
                      {
                        color: 'white',
                        fontSize: 18,
                      },
                    ]}>
                    Bắt đầu chụp
                  </Sys.Text>
                </TouchableOpacity>
              ) : (
                <Camera
                  style={{
                    width: width - 40,
                    height: getCameraHeight(),
                  }}
                  device={device}
                  format={format}
                  isActive={isActive}
                  ref={camera}
                  photoQualityBalance="speed"
                  {...cameraProps}
                  enableZoomGesture={true}
                />
              )}
            </View>
            <View style={{flexDirection: 'row', marginTop: 10, height: 50}}>
              {currentPhotos.map((photo, index) => (
                <OnePic
                  key={`${index}`}
                  item={photo}
                  removeImage={() => handleImageRemoval(index)}
                />
              ))}
            </View>
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: 20,
            }}>
            <TouchableOpacity
              onPress={() => {
                setIsAnimating(true);
                setCameraPosition(cameraPosition === 'back' ? 'front' : 'back');
                setTimeout(() => {
                  setIsAnimating(false);
                }, 1000);
              }}
              style={{
                width: 110,
                opacity: isDisable ? 0.5 : 1, // Adjust opacity based on disabled state
              }}
              disabled={isDisable}>
              <ChangeCameraIcon />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={onTakePhoto}
              style={{
                opacity: isDisable || maxPhotosReached ? 0.5 : 1,
              }}
              disabled={isDisable || maxPhotosReached}>
              <CameraButtonIcon />
            </TouchableOpacity>
            {currentPhotos.length > 0 ? (
              <TouchableOpacity
                onPress={onNext}
                style={{
                  flexDirection: 'row',
                  backgroundColor: '#0062FF',
                  height: 50,
                  alignItems: 'center',
                  borderRadius: 50,
                  width: 110,
                  justifyContent: 'center',
                  opacity: isAnimating || !isCameraReady ? 0.5 : 1, // Adjust opacity based on disabled state
                }}
                disabled={isAnimating || !isCameraReady}>
                <Sys.Text
                  style={{
                    color: 'white',
                    fontWeight: '600',
                    fontSize: 20,
                    lineHeight: 28,
                    marginRight: 10,
                  }}>
                  Tiếp
                </Sys.Text>
                <RightIcon />
              </TouchableOpacity>
            ) : (
              <View style={{width: 110}} />
            )}
          </View>
        </ANT.View>
      ) : (
        <View style={{flex: 1, paddingHorizontal: 20}} />
      )}
    </Sys.Container>
  );
};

export default CheckInLocationCameraScreen;

const OnePic = ({item, removeImage}) => {
  const {height, width} = useWindowDimensions();
  const [isShow, setIsShow] = useState(false);

  return (
    <>
      <Modal
        animationIn="fadeInUp"
        animationOut="fadeOutDown"
        style={{
          padding: 0,
          margin: 0,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          height,
          width,
        }}
        onBackdropPress={() => setIsShow(false)}
        isVisible={isShow}
        onSwipeComplete={() => setIsShow(false)}
        swipeDirection="down">
        <View style={{zIndex: 99}}>
          <Image
            style={{width: width - 40, height: width - 40, borderRadius: 10}}
            source={{uri: item.uri}}
          />
        </View>
      </Modal>
      <TouchableOpacity
        onPress={() => setIsShow(true)}
        activeOpacity={0.8}
        style={{marginRight: 20}}>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={removeImage}
          style={{
            position: 'absolute',
            right: -10,
            top: -10,
            zIndex: 9,
          }}>
          <RemoveIcon />
        </TouchableOpacity>
        <Image
          style={{width: 100, height: 100, borderRadius: 10}}
          source={{uri: item.uri}}
        />
      </TouchableOpacity>
    </>
  );
};
