import React from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';
import ContentLoader, {Circle, Rect} from 'react-content-loader/native';

const {width} = Dimensions.get('window');

const BranchManagerSkeletonScreen = () => {
  return (
    <View
      style={{
        flex: 1,
        paddingHorizontal: 10,
      }}>
      {/* Top KPI Rows */}
      <View style={styles.rowContainer}>
        <View style={styles.chartContainer}>
          <ContentLoader
            speed={2}
            width={width / 2 - 15}
            height={130}
            viewBox={`0 0 ${width / 2 - 15} 130`}
            backgroundColor="#f3f3f3"
            foregroundColor="#ecebeb">
            {/* KPI Circle */}
            <Circle cx="50%" cy="40" r="30" />
            {/* KPI Rectangles */}
            <Rect x="25%" y="90" rx="4" ry="4" width="50%" height="12" />
            <Rect x="35%" y="110" rx="4" ry="4" width="30%" height="12" />
          </ContentLoader>
        </View>
        <View style={styles.chartContainer}>
          <ContentLoader
            speed={2}
            width={width / 2 - 15}
            height={130}
            viewBox={`0 0 ${width / 2 - 15} 130`}
            backgroundColor="#f3f3f3"
            foregroundColor="#ecebeb">
            <Circle cx="50%" cy="40" r="30" />
            <Rect x="25%" y="90" rx="4" ry="4" width="50%" height="12" />
            <Rect x="35%" y="110" rx="4" ry="4" width="30%" height="12" />
          </ContentLoader>
        </View>
      </View>

      {/* Channel & Product Pie Charts */}
      <View style={styles.rowContainer}>
        <View style={styles.chartContainer}>
          <ContentLoader
            speed={2}
            width={width / 2 - 15}
            height={170}
            viewBox={`0 0 ${width / 2 - 15} 170`}
            backgroundColor="#f3f3f3"
            foregroundColor="#ecebeb">
            <Circle cx="50%" cy="70" r="50" />
            <Rect x="25%" y="130" rx="4" ry="4" width="50%" height="12" />
          </ContentLoader>
        </View>
        <View style={styles.chartContainer}>
          <ContentLoader
            speed={2}
            width={width / 2 - 15}
            height={170}
            viewBox={`0 0 ${width / 2 - 15} 170`}
            backgroundColor="#f3f3f3"
            foregroundColor="#ecebeb">
            <Circle cx="50%" cy="70" r="50" />
            <Rect x="25%" y="130" rx="4" ry="4" width="50%" height="12" />
          </ContentLoader>
        </View>
      </View>

      {/* Bar Chart for Products by Channel */}
      <View style={styles.fullWidthContainer}>
        <ContentLoader
          speed={2}
          width={width - 20}
          height={200}
          viewBox={`0 0 ${width - 20} 200`}
          backgroundColor="#f3f3f3"
          foregroundColor="#ecebeb">
          {/* Bar Chart Rectangles */}
          <Rect x="5%" y="40" rx="4" ry="4" width="10%" height="120" />
          <Rect x="20%" y="70" rx="4" ry="4" width="10%" height="90" />
          <Rect x="35%" y="90" rx="4" ry="4" width="10%" height="70" />
          <Rect x="50%" y="50" rx="4" ry="4" width="10%" height="110" />
          <Rect x="65%" y="30" rx="4" ry="4" width="10%" height="130" />
          <Rect x="80%" y="60" rx="4" ry="4" width="10%" height="100" />
        </ContentLoader>
      </View>

      {/* Branch Revenue Rank List */}
      <View style={styles.fullWidthContainer}>
        <ContentLoader
          speed={2}
          width={width - 20}
          height={150}
          viewBox={`0 0 ${width - 20} 150`}
          backgroundColor="#f3f3f3"
          foregroundColor="#ecebeb">
          {/* Revenue Rank Rectangles */}
          <Rect x="5%" y="20" rx="4" ry="4" width="90%" height="20" />
          <Rect x="5%" y="50" rx="4" ry="4" width="90%" height="20" />
          <Rect x="5%" y="80" rx="4" ry="4" width="90%" height="20" />
          <Rect x="5%" y="110" rx="4" ry="4" width="90%" height="20" />
        </ContentLoader>
      </View>

      <View style={{height: 150}} />
    </View>
  );
};

const styles = StyleSheet.create({
  rowContainer: {
    flexDirection: 'row',
    marginVertical: 10,
  },
  chartContainer: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 10,
    marginHorizontal: 5,
  },
  fullWidthContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 10,
    marginVertical: 10,
  },
});

export default BranchManagerSkeletonScreen;
