import React, {useRef, useState} from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';
import WebView from 'react-native-webview';
import {useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {AppSelectors} from '../../app.slice';
import BranchManagerSkeletonScreen from './BranchManagerSkeletonScreen';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const {height} = Dimensions.get('window');

const BranchManagerChartBarBranch = () => {
  const bioToken = useSelector(AppSelectors.bioToken);
  const {baseUrlWebview = 'https://dms.vietlott.vn'} = useSelector(
    AppSelectors.settings,
  );
  const navigation = useNavigation();
  const webviewUrl = `${baseUrlWebview}/webview/dashboard/daily?type_report=daily&webview=1&token=${bioToken}`;
  const webViewRef = useRef(null);
  const [loading, setLoading] = useState(true);
  const insets = useSafeAreaInsets();

  // JavaScript to inject into the WebView
  const injectedJavaScript = `
    // Hide the header if it exists
    const header = document.getElementById('kt_app_header_container');
    if (header) {
      header.style.display = 'none';
    }

    // Handle <a> tag clicks
    const links = document.querySelectorAll('a');
    links.forEach(link => {
      // If the <a> tag contains data-posid, handle the click
      if (link.hasAttribute('data-posid')) {
        link.addEventListener('click', function(event) {
          event.preventDefault();  // Prevent default behavior
          const posId = link.getAttribute('data-posid');
          // Send the posId to React Native
          window.ReactNativeWebView.postMessage(JSON.stringify({ posId }));
        });
      } else {
        // Disable all other <a> tags
        link.style.pointerEvents = 'none';
        link.style.color = 'gray';  // Optionally, you can change the color to indicate it's disabled
      }
    });
  `;

  // Handle the messages sent from the WebView
  const onMessage = event => {
    try {
      const {posId} = JSON.parse(event.nativeEvent.data);
      if (posId) {
        // Navigate to SaleInformationScreen with the posId
        navigation.navigate('SaleInformationScreen', {
          posCode: posId,
        });
      }
    } catch (error) {
      console.warn('Error parsing WebView message', error);
    }
  };

  return (
    <View
      style={[
        styles.container,
        {
          height: loading ? height * 1.5 : height - insets.top - 100,
        },
      ]}>
      {loading && <BranchManagerSkeletonScreen />}
      <WebView
        ref={webViewRef}
        source={{
          uri: webviewUrl,
          headers: {
            Authorization: `Bearer ${bioToken}`,
          },
        }}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        style={[styles.webview]}
        bounces={false}
        injectedJavaScript={injectedJavaScript} // Inject the JavaScript into the WebView
        onLoadStart={() => setLoading(true)}
        onLoadEnd={() => setLoading(false)}
        onError={syntheticEvent => {
          const {nativeEvent} = syntheticEvent;
          console.warn('WebView error: ', nativeEvent);
          setLoading(false);
        }}
        onHttpError={syntheticEvent => {
          const {nativeEvent} = syntheticEvent;
          console.warn('WebView HTTP error: ', nativeEvent);
          setLoading(false);
        }}
        onMessage={onMessage} // Handle messages from the WebView
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webview: {
    flex: 1,
  },
});

export default BranchManagerChartBarBranch;
