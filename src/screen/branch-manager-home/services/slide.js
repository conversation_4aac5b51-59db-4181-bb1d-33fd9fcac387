import {createSlice} from '@reduxjs/toolkit';

const initialState = {};

const BranchManagerSlice = createSlice({
  name: 'branchManager',
  initialState,
  reducers: {
    getDataChannel: () => {},
    getDataProduct: () => {},
    getDataChart: () => {},
    getDataChartBranch: () => {},
    getDataRankPOS: () => {},
    getDataRankProvince: () => {},
  },
});

const BranchManagerReducer = BranchManagerSlice.reducer;
export default BranchManagerReducer;

export const BranchManagerActions = BranchManagerSlice.actions;
