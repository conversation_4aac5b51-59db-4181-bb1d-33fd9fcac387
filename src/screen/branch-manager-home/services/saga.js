import {takeLatest} from 'redux-saga/effects';
import {BranchManagerActions} from './slide';
import SysFetch from '../../../services/fetch';

function* branchManagerSaga() {
  yield takeLatest(BranchManagerActions.getDataChannel, getDataChannel);
  yield takeLatest(BranchManagerActions.getDataProduct, getDataProduct);
  yield takeLatest(BranchManagerActions.getDataChart, getDataChart);
  yield takeLatest(BranchManagerActions.getDataChartBranch, getDataChartBranch);
  yield takeLatest(
    BranchManagerActions.getDataRankProvince,
    getDataRankProvince,
  );
  yield takeLatest(BranchManagerActions.getDataRankPOS, getDataRankPOS);
}

export default branchManagerSaga;

function* getDataRankProvince({payload}) {
  try {
    const {body, onSuccess} = payload;
    const rs = yield SysFetch.get('report/stats/daily/province', body);
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}
function* getDataRankPOS({payload}) {
  try {
    const {body, onSuccess} = payload;
    const rs = yield SysFetch.get('report/stats/daily/pos', body);
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getDataChartBranch({payload}) {
  try {
    const {body, onSuccess} = payload;
    const rs = yield SysFetch.get('report/stats/daily/game', body);
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getDataChart({payload}) {
  try {
    const {body, onSuccess} = payload;
    const rs = yield SysFetch.get('report/stats/daily/game', body);
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}
function* getDataProduct({payload}) {
  try {
    const {body, onSuccess} = payload;
    const rs = yield SysFetch.get('report/stats/daily/game', body);
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getDataChannel({payload}) {
  try {
    const {body, onSuccess} = payload;
    const rs = yield SysFetch.get('report/stats/daily/ticket_type', body);
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}
