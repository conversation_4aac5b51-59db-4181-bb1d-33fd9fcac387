import React from 'react';
import {StyleSheet, View} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';

const BranchListSkeleton = () => {
  return (
    <View style={styles.skeletonContainer}>
      <ContentLoader
        speed={2}
        width={'100%'}
        height={30}
        viewBox="0 0 400 30"
        backgroundColor="#f3f3f3"
        foregroundColor="#ecebeb">
        <Rect x="0" y="10" rx="5" ry="5" width="20" height="20" />
        <Rect x="30" y="10" rx="5" ry="5" width="250" height="20" />
      </ContentLoader>
    </View>
  );
};

const styles = StyleSheet.create({
  skeletonContainer: {
    flex: 1,
    padding: 5,
    backgroundColor: 'white',
    borderRadius: 10,
    marginBottom: 5,
  },
});

export default BranchListSkeleton;
