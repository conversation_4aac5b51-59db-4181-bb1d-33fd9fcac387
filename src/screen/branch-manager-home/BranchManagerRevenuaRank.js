import moment from 'moment';
import React, {useState} from 'react';
import {FlatList, TouchableOpacity, View} from 'react-native';
import Sys from '../../components/Sys';
import UpIcon from './icons/UpIcon';
import {formatVND} from '../../services/util';
import DownsIcon from './icons/DownsIcon';
import {useSelector} from 'react-redux';
import {AccountSelectors} from '../account/services/account.slice';
import Icons from 'react-native-vector-icons/MaterialCommunityIcons';

const BranchManagerRevenuaRank = ({dataRank, dataProvince}) => {
  const globalFilter = useSelector(AccountSelectors.globalFilter);

  return (
    <View
      style={{
        backgroundColor: 'white',
        borderRadius: 20,
      }}>
      <View style={{}}>
        <Sys.Text
          style={{
            color: '#0062FF',
            fontSize: 12,
            fontWeight: '600',
            textAlign: 'center',
            paddingVertical: 15,
          }}>
          Bảng xếp hạng doanh thu ngày{' '}
          {moment(globalFilter.date, 'YYYY-MM-DD').format('DD/MM/YYYY')}
        </Sys.Text>
      </View>
      <View>
        <FlatList
          data={[
            {
              label: 'Điểm bán hàng',
              data: dataRank.map(x => {
                return {
                  ...x,
                  label: x.address,
                };
              }),
            },
            {
              label: 'Tỉnh/Thành phố',
              data: dataProvince.map(x => {
                return {
                  ...x,
                  label: x.name,
                };
              }),
            },
          ]}
          keyExtractor={(item, index) => `${index}`}
          renderItem={props => <OneItem {...props} />}
        />
      </View>
    </View>
  );
};

export default BranchManagerRevenuaRank;

const OneItem = ({item, index}) => {
  const [isShow, setIsShow] = useState(false);

  return (
    <View>
      <View
        style={{
          flexDirection: 'row',
          backgroundColor: '#001451',
          padding: 7,
        }}>
        <View
          style={{
            flex: 1,
          }}>
          <Sys.Text
            style={{
              fontSize: 12,
              color: 'white',
              fontWeight: '500',
              lineHeight: 20,
            }}>
            {item?.label}
          </Sys.Text>
        </View>
        <View style={{width: 100}}>
          <Sys.Text
            style={{
              fontSize: 12,
              color: 'white',
              fontWeight: '500',
              lineHeight: 20,
            }}>
            Doanh thu
          </Sys.Text>
        </View>
        <View style={{width: 70}}>
          <Sys.Text
            style={{
              fontSize: 12,
              color: 'white',
              fontWeight: '500',
              lineHeight: 20,
            }}>
            Thay đổi
          </Sys.Text>
        </View>
      </View>
      <View
        style={{
          paddingVertical: 5,
        }}>
        {(() => {
          try {
            return isShow && item?.data ? item?.data : item?.data?.slice(0, 5);
          } catch (error) {
            return [];
          }
        })().map((x, index) => {
          return <OneSubItem item={x} index={index} key={`${index}`} />;
        })}
      </View>
      <TouchableOpacity
        onPress={() => setIsShow(!isShow)}
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          paddingVertical: 3,
        }}>
        {isShow ? (
          <Icons color={'#0062FF'} size={24} name="chevron-up" />
        ) : (
          <Icons color={'#0062FF'} size={24} name="chevron-down" />
        )}
      </TouchableOpacity>
    </View>
  );
};

const OneSubItem = ({item, index}) => {
  return (
    <View
      style={{
        flex: 1,
        paddingVertical: 7,
        marginHorizontal: 7,
        borderBottomColor: '#cccccc20',
        borderBottomWidth: 1,
        flexDirection: 'row',
      }}>
      <View
        style={{
          flex: 1,
        }}>
        <Sys.Text
          style={{
            fontSize: 12,
            lineHeight: 20,
          }}>
          {item.name}
        </Sys.Text>
      </View>
      <View style={{width: 100}}>
        <Sys.Text
          style={{
            fontSize: 12,
            lineHeight: 20,
          }}>
          {formatVND(item?.total_revenue)}
        </Sys.Text>
      </View>
      <View style={{width: 70}}>
        <View>
          <Sys.Text
            style={{
              fontSize: 10,
            }}>
            {formatVND(item?.previous_revenue)}
          </Sys.Text>
        </View>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {item?.percentage_change === 0 ? (
            <></>
          ) : item?.percentage_change > 0 ? (
            <UpIcon />
          ) : (
            <DownsIcon />
          )}
          <Sys.Text
            style={{
              fontSize: 12,
              lineHeight: 20,
              marginLeft: 4,
            }}>
            {item?.percentage_change}%
          </Sys.Text>
        </View>
      </View>
    </View>
  );
};
