import React, {useEffect, useState} from 'react';
import {
  FlatList,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {
  VictoryAxis,
  VictoryBar,
  VictoryChart,
  VictoryGroup,
  VictoryLabel,
  VictoryLine,
  VictoryTheme,
} from 'victory-native';
import Sys from '../../components/Sys';
import {formatVND} from '../../services/util';

const BranchManagerChartBarAndLine = ({
  game = [],
  data = [],
  title = '',
  city = false,
}) => {
  const {width} = useWindowDimensions();
  const [currentGame, setCurrentGame] = useState([]);
  const [currentData, setCurrentData] = useState([]);
  useEffect(() => {
    setCurrentGame(
      game.map((x, index) => {
        return {
          ...x,
          isShow: true,
          index,
        };
      }),
    );
  }, [game, data]);

  useEffect(() => {
    try {
      setCurrentData(
        data.filter((x, index) => {
          return currentGame[x.index].isShow;
        }),
      );
    } catch (error) {}
  }, [currentGame]);
  return (
    <View
      style={{
        backgroundColor: 'white',
        borderRadius: 20,
      }}>
      <Sys.Text
        style={{
          color: '#0062FF',
          fontSize: 12,
          fontWeight: '600',
          textAlign: 'center',
          paddingVertical: 15,
        }}>
        {title}
      </Sys.Text>
      <VictoryChart
        theme={VictoryTheme.material}
        width={width - 20}
        domainPadding={20}
        padding={{
          left: 40,
          top: 0,
          bottom: 50,
          right: 40,
        }}>
        <VictoryAxis
          tickLabelComponent={
            !city ? <CustomTickLabelX /> : <CustomTickLabelXCity />
          }
          // tickComponent={<></>}
          axisComponent={<></>}
        />
        <VictoryAxis
          dependentAxis
          axisComponent={<></>}
          //   tickComponent={<></>}
          tickLabelComponent={<CustomTickLabelY />}
          //   labelComponent={<></>}
        />
        <VictoryAxis
          orientation="right"
          dependentAxis
          axisComponent={<></>}
          //   tickComponent={<></>}
          tickLabelComponent={<CustomTickLabelY />}
          //   labelComponent={<></>}
        />
        <VictoryGroup offset={13}>
          {currentData
            .filter(x => x.type == 'bar')
            .map((x, index) => {
              return (
                <VictoryBar
                  style={{
                    data: {
                      fill: currentGame[x.index].color,
                      zIndex: 0,
                      // width: 12,
                    },
                  }}
                  data={x.data}
                  labels={({datum}) => `${formatVND(datum.y)}`}
                  labelComponent={
                    <VictoryLabel
                      dy={0}
                      style={{
                        fill: 'black',
                      }}
                    />
                  }
                />
              );
            })}
        </VictoryGroup>
        <VictoryGroup>
          {currentData
            .filter(x => x.type == 'line')
            .map((x, index) => {
              return (
                <VictoryLine
                  style={{
                    data: {
                      stroke: currentGame[x.index].color,
                    },
                  }}
                  data={x.data}
                />
              );
            })}
        </VictoryGroup>
      </VictoryChart>
      <View
        style={{
          paddingHorizontal: 10,
        }}>
        <FlatList
          scrollEnabled={false}
          data={currentGame}
          numColumns={4}
          keyExtractor={(item, index) => `${index}`}
          renderItem={props => {
            return (
              <TouchableOpacity
                onPress={() => {
                  setCurrentGame(
                    currentGame.map((x, index) => {
                      return {
                        ...x,
                        isShow:
                          x.index == props?.index ? !x?.isShow : x?.isShow,
                      };
                    }),
                  );
                }}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                  marginBottom: 10,
                }}>
                <View
                  style={{
                    backgroundColor: props?.item?.isShow
                      ? props?.item?.color
                      : `${props?.item?.color}50`,
                    height: 10,
                    width: 10,
                  }}
                />
                <Sys.Text
                  style={{
                    fontSize: 10,
                    marginLeft: 5,
                  }}>
                  {props?.item?.label}
                </Sys.Text>
              </TouchableOpacity>
            );
          }}
        />
      </View>
    </View>
  );
};

export default BranchManagerChartBarAndLine;
const CustomTickLabelY = props => {
  return (
    <VictoryLabel
      {...props}
      style={{
        fill: '#666',
        fontSize: 10,
      }}
    />
  );
};
const CustomTickLabelX = props => {
  return (
    <VictoryLabel
      {...props}
      style={{
        fill: '#666',
        fontSize: 8,
      }}
    />
  );
};

const CustomTickLabelXCity = props => {
  return (
    <VictoryLabel
      {...props}
      style={{
        fill: '#666',
        fontSize: 8,
      }}
      y={props.y + 10}
      x={props.x - 20}
      verticalAnchor={'start'}
      angle={-45}
    />
  );
};
