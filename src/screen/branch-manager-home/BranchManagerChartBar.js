import * as _ from 'lodash';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {
  FlatList,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {
  VictoryAxis,
  VictoryBar,
  VictoryChart,
  VictoryLabel,
  VictoryStack,
  VictoryTheme,
} from 'victory-native';
import Sys from '../../components/Sys';
import {formatVNDForChart} from '../../services/util';

const BranchManagerChartBar = ({
  game = [],
  data = [],
  title = '',
  city = false,
}) => {
  const {width} = useWindowDimensions();
  const [currentGame, setCurrentGame] = useState([]);
  const [currentData, setCurrentData] = useState([]);
  const [highestColumnTotal, setHighestColumnTotal] = useState(0);
  const [totalData, setTotalData] = useState([]); // Store total values

  useEffect(() => {
    setCurrentGame(game);
  }, [game, data]);

  const calculateHighestColumnTotal = useCallback(data => {
    const sumsByX = {};
    data.forEach(array => {
      array.forEach(item => {
        const {x, y} = item;
        if (!sumsByX[x]) {
          sumsByX[x] = 0;
        }
        sumsByX[x] += y;
      });
    });

    let maxValue = 0;
    Object.values(sumsByX).forEach(value => {
      if (value > maxValue) {
        maxValue = value;
      }
    });
    setHighestColumnTotal(maxValue);

    // Set total data for each branch
    const totals = Object.keys(sumsByX).map(branch => ({
      x: branch,
      y: sumsByX[branch],
    }));
    setTotalData(totals);
  }, []);

  const renderChart = useMemo(() => {
    return (
      <VictoryChart
        theme={VictoryTheme.material}
        width={width - 40}
        domainPadding={20}
        padding={{
          left: 60,
          top: 10,
          bottom: !city ? 50 : 70,
          right: 0,
        }}>
        <VictoryAxis
          tickLabelComponent={
            !city ? <CustomTickLabelX /> : <CustomTickLabelXCity />
          }
          axisComponent={<></>}
        />
        <VictoryAxis
          dependentAxis
          axisComponent={<></>}
          tickLabelComponent={<CustomTickLabelY />}
        />
        <VictoryStack
          colorScale={(currentGame || [])
            .filter(x => !!x?.color && x?.isShow)
            .map(x => x.color)}>
          {currentData.map((x, index) => {
            return (
              <VictoryBar
                key={index}
                style={{
                  data: {
                    width: 30,
                  },
                }}
                data={x}
                labels={({datum}) =>
                  `${
                    datum.y / (highestColumnTotal / 100) > 15
                      ? formatVNDForChart(datum.y)
                      : ''
                  }`
                }
                labelComponent={
                  <VictoryLabel
                    dy={20}
                    style={{
                      fill: 'white',
                      fontSize: 10,
                    }}
                  />
                }
              />
            );
          })}
        </VictoryStack>

        {/* Render total bars as transparent */}
        <VictoryBar
          data={totalData} // Total values for each branch
          labels={({datum}) => `${formatVNDForChart(datum.y)}`}
          labelComponent={
            <VictoryLabel
              dy={-15} // Position the label slightly above the total bar
              style={{fill: 'black', fontSize: 12, fontWeight: 'bold'}}
            />
          }
          style={{
            data: {
              fill: 'transparent', // Transparent fill for the total bars
              // stroke: 'red', // Red border to highlight the total
              // strokeWidth: 2, // Thicker stroke to make the total bar stand out
              width: 30, // Match the width of the other bars
            },
          }}
        />
      </VictoryChart>
    );
  }, [currentData, totalData]);

  useEffect(() => {
    try {
      const temps = [];
      currentGame.forEach(gameItem => {
        if (gameItem.isShow) {
          const tempGame = [];
          data.forEach(dataItem => {
            const items = Array.isArray(dataItem.items)
              ? dataItem.items
              : Object.values(dataItem.items);
            tempGame.push({
              x: dataItem.name,
              y: _.find(items, {name: gameItem.name})?.total_revenue || 0,
            });
          });
          temps.push(tempGame);
        }
      });

      setCurrentData(temps);
      calculateHighestColumnTotal(temps);
    } catch (error) {
      console.error(error);
    }
  }, [currentGame, data]);

  return (
    <View
      style={{
        backgroundColor: 'white',
        borderRadius: 20,
      }}>
      <Sys.Text
        style={{
          color: '#0062FF',
          fontSize: 12,
          fontWeight: '600',
          textAlign: 'center',
          paddingVertical: 15,
        }}>
        {title}
      </Sys.Text>
      {renderChart}
      <View
        style={{
          paddingHorizontal: 40,
        }}>
        <FlatList
          scrollEnabled={false}
          data={currentGame}
          numColumns={2}
          keyExtractor={(item, index) => `${index}`}
          renderItem={({item}) => (
            <TouchableOpacity
              onPress={() => {
                setCurrentGame(prevState =>
                  prevState.map(x =>
                    x.id === item.id ? {...x, isShow: !x.isShow} : x,
                  ),
                );
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                flex: 1,
                marginBottom: 10,
              }}>
              <View
                style={{
                  backgroundColor: item?.isShow
                    ? item?.color
                    : `${item?.color}50`,
                  height: 10,
                  width: 10,
                }}
              />
              <Sys.Text
                style={{
                  fontSize: 10,
                  marginLeft: 10,
                }}>
                {item?.name}
              </Sys.Text>
            </TouchableOpacity>
          )}
        />
      </View>
    </View>
  );
};

export default BranchManagerChartBar;

const CustomTickLabelY = props => {
  function formatVND(value) {
    if (value < 1000) {
      return '';
    }
    let formattedValue = Number(value.replace(/,/g, '')) / 1000000;

    if (formattedValue > 999) {
      return `${(formattedValue / 1000).toFixed(2)} Tỷ`;
    } else {
      return `${formattedValue} Tr`;
    }
  }

  return (
    <VictoryLabel
      {...props}
      style={{
        fill: '#666',
        fontSize: 10,
      }}
      text={`${formatVND(props.text)}`}
    />
  );
};
const CustomTickLabelX = props => {
  return (
    <VictoryLabel
      {...props}
      style={{
        fill: '#666',
        fontSize: 8,
      }}
    />
  );
};

const CustomTickLabelXCity = props => {
  return (
    <VictoryLabel
      {...props}
      style={{
        fill: '#666',
        fontSize: 8,
      }}
      y={props.y + 20}
      x={props.x - 20}
      verticalAnchor={'start'}
      angle={-45}
    />
  );
};
