import * as _ from 'lodash';
import React, {useEffect, useMemo, useState} from 'react';
import {FlatList, TouchableOpacity, View} from 'react-native';
import {VictoryPie} from 'victory-native';
import Sys from '../../components/Sys';
import {formatMoney} from '../../services/util';

const BranchManagerChartProduct = ({text1, text2, game, data}) => {
  const [currentGame, setCurrentGame] = useState([]);
  const [currentData, setCurrentData] = useState([]);

  useEffect(() => {
    setCurrentGame(
      game.map(x => {
        return {
          ...x,
          isShow: true,
        };
      }),
    );
  }, [game, data]);

  useEffect(() => {
    try {
      const tempData = data.filter((x, index) => {
        return (
          _.find(
            currentGame,
            {
              id: x.code,
            },
            'isShow',
            true,
          ).isShow === true
        );
      });
      setCurrentData(tempData);
    } catch (error) {}
  }, [currentGame]);

  const total = currentData.reduce((acc, datum) => acc + datum.y, 0);
  const calculateLabel = (datum, total) => {
    const percentage = ((datum.y / total) * 100).toFixed(1);
    const amount = formatMoney(datum.y);
    return (datum.y / total) * 100 < 15 ? '' : `${percentage}%\n${amount}`;
  };

  const renderChart = useMemo(() => {
    return (
      <View
        style={{
          padding: 10,
        }}>
        <Sys.Text
          style={{
            fontSize: 12,
            textAlign: 'center',
            lineHeight: 16,
          }}>
          {text1}
        </Sys.Text>
        <Sys.Text
          style={{
            fontSize: 12,
            textAlign: 'center',
            lineHeight: 16,
          }}>
          {text2}
        </Sys.Text>
        <View
          style={{
            height: 170,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <View
            style={{
              position: 'absolute',
              top: -40,
            }}>
            <VictoryPie
              labels={({datum}) => calculateLabel(datum, total)}
              width={240}
              height={240}
              labelRadius={({innerRadius}) => 25}
              data={currentData}
              colorScale={currentData.map(x => {
                return _.find(currentGame, {
                  id: x.code,
                }).color;
              })}
              style={{
                labels: {fill: 'white', fontSize: 12},
              }}
            />
          </View>
        </View>
        <View
          style={{
            paddingHorizontal: 5,
          }}>
          <FlatList
            scrollEnabled={false}
            data={currentGame}
            numColumns={2}
            keyExtractor={(item, index) => `${index}`}
            renderItem={props => {
              return (
                <TouchableOpacity
                  onPress={() => {
                    setCurrentGame(
                      currentGame.map((x, index) => {
                        return {
                          ...x,
                          isShow:
                            x.id == props?.item?.id ? !x?.isShow : x?.isShow,
                        };
                      }),
                    );
                  }}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    flex: 1,
                    marginBottom: 10,
                  }}>
                  <View
                    style={{
                      backgroundColor: props?.item?.isShow
                        ? props?.item?.color
                        : `${props?.item?.color}50`,
                      height: 10,
                      width: 10,
                    }}
                  />
                  <Sys.Text
                    style={{
                      fontSize: 10,
                      marginLeft: 10,
                    }}>
                    {props?.item?.name}
                  </Sys.Text>
                </TouchableOpacity>
              );
            }}
          />
        </View>
      </View>
    );
  }, [currentData]);

  return renderChart;
};

export default BranchManagerChartProduct;
