import React, {useEffect, useState} from 'react';
import {ScrollView, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {AccountSelectors} from '../account/services/account.slice';
import {ReportActions} from '../report/services/report.slice';
import BranchManagerChart from './BranchManagerChart';
import BranchManagerChartBar from './BranchManagerChartBar';
import BranchManagerRevenuaRank from './BranchManagerRevenuaRank';
import {BranchManagerActions} from './services/slide';
import BranchManagerChartBarBranch from './BranchManagerChartBarBranch';
import BranchManagerChartProduct from './BranchManagerChartProduct';
import moment from 'moment';

const BranchManagerHomeScreen = () => {
  const dispatch = useDispatch();
  const globalFilter = useSelector(AccountSelectors.globalFilter);
  const body = {
    event_time: globalFilter.date,
    branch: globalFilter?.branch?.length > 0 ? globalFilter?.branch : [],
  };

  const {view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);

  const [dataChannel, setDataChannel] = useState([]);
  const [dataProduct, setDataProduct] = useState([
    {x: 1, y: 120},
    {x: 2, y: 320},
    {x: 3, y: 30},
    {x: 4, y: 50},
    {x: 5, y: 200},
    {x: 6, y: 80},
  ]);
  const [dataChart, setDataChart] = useState([]);
  const [dataChartBranch, setDataChartBranch] = useState([]);

  const [gameListChannel, setGameListChannel] = useState([
    {color: '#eabc61', id: 0, isShow: true, name: 'TBĐC'},
    {color: '#44c8a1', id: 1, isShow: true, name: 'SMS'},
  ]);
  const [gameListProduct, setGameListProduct] = useState([
    {
      color: '#DA2037',
      label: 'Mega 645',
      index: 0,
    },
    {
      color: '#E8B768',
      label: 'Power 655',
      index: 1,
    },
    {
      color: '#E9098D',
      label: 'Max 3D',
      index: 2,
    },
    {
      color: '#92278F',
      label: 'Max 3D Pro',
      index: 3,
    },
    {
      color: '#EF7721',
      label: 'Keno',
      index: 4,
    },
    {
      color: '#369017',
      label: 'Bingo',
      index: 5,
    },
  ]);

  const [gameListPos, setGameListPos] = useState([
    {
      label: 'Mở mới',
      color: '#90BB3B',
      type: 'bar',
    },
    {
      label: 'Đóng',
      color: '#D30B0D',
      type: 'bar',
    },
    {
      label: 'Phát sinh DT',
      color: '#0062FF',
      type: 'bar',
    },
    {
      label: 'Đang hoạt động',
      color: '#EF7721',
      type: 'bar',
    },
  ]);

  const [gameListSMS, setGameListSMS] = useState([
    {
      label: 'Tổng số',
      color: '#90BB3B',
      type: 'bar',
    },
    {
      label: 'Phát sinh DT',
      color: '#0062FF',
      type: 'bar',
    },
    {
      label: 'Đăng ký mới',
      color: '#EF7721',
      type: 'bar',
    },
  ]);
  const [dataRankPOS, setDataRankPOS] = useState([]);
  const [dataRankProvince, setDataRankProvince] = useState([]);

  useEffect(() => {
    getDataChannel();
    getDataProduct();
    getDataChart();
    getDataChartBranch();
    getDataRankPOS();
    getDataRankProvince();
    //
  }, [globalFilter]);

  // lấy dữ liệu chart province
  const getDataRankProvince = () => {
    dispatch(
      BranchManagerActions.getDataRankProvince({
        body: {
          ...body,
          sort_by: 'total_revenue',
          sort_order: 'desc',
          pageSize: 10,
        },
        onSuccess: rs => {
          const temp = rs?.data?.items;
          if (temp) {
            setDataRankProvince(temp);
          }
        },
      }),
    );
  };

  // lấy dữ liệu chart branch
  const getDataRankPOS = () => {
    dispatch(
      BranchManagerActions.getDataRankPOS({
        body: {
          ...body,
          sort_by: 'total_revenue',
          sort_order: 'desc',
          pageSize: 10,
        },
        onSuccess: rs => {
          const temp = rs?.data?.items;
          if (temp) {
            setDataRankPOS(temp);
          }
        },
      }),
    );
  };

  // lấy dữ liệu chart branch
  const getDataChartBranch = () => {
    dispatch(
      BranchManagerActions.getDataChartBranch({
        body: {
          ...body,
          stacked_by: 'branch',
        },
        onSuccess: rs => {
          const temp = rs?.data?.items;
          if (temp) {
            if (Array.isArray(temp)) {
              setDataChartBranch(temp);
            } else {
              setDataChartBranch(Object.values(temp));
            }
          }
        },
      }),
    );
  };

  // lấy dữ liệu chart
  const getDataChart = () => {
    dispatch(
      BranchManagerActions.getDataChart({
        body: {
          ...body,
          stacked_by: 'ticket_type',
          ticket_type: [0, 1],
        },
        onSuccess: rs => {
          const temp = rs?.data?.items;
          if (temp) {
            if (Array.isArray(temp)) {
              setDataChart(temp);
            } else {
              setDataChart(Object.values(temp));
            }
          }
        },
      }),
    );
  };
  // lấy dữ liệu sản phẩm
  const getDataProduct = () => {
    dispatch(
      ReportActions.getGames({
        body,
        onSuccess: rs => {
          setGameListProduct(rs?.data.filter(x => x.color !== null));
          dispatch(
            BranchManagerActions.getDataProduct({
              body: {
                ...body,
              },
              onSuccess: rs => {
                const temp = rs?.data?.items;
                if (temp) {
                  setDataProduct(
                    temp.map((x, index) => {
                      return {x: index + 1, y: x?.total_revenue, code: x?.code};
                    }),
                  );
                }
              },
            }),
          );
        },
        onFail: rs => {},
      }),
    );
  };
  // lấy dữ liệu với kênh phân phối
  const getDataChannel = () => {
    dispatch(
      BranchManagerActions.getDataChannel({
        body: {
          ...body,
          ticket_type: [0, 1],
        },
        onSuccess: rs => {
          const temp = rs?.data?.items;
          if (temp) {
            setDataChannel(
              temp.map((x, index) => {
                return {x: index + 1, y: x?.total_revenue, code: x?.code};
              }),
            );
          }
        },
      }),
    );
  };
  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={{
        flex: 1,
        paddingHorizontal: 10,
      }}>
      <View
        style={{
          flexDirection: 'row',
        }}>
        <View
          style={{
            marginRight: 5,
            flex: 1,
            backgroundColor: 'white',
            borderRadius: 20,
          }}>
          <BranchManagerChart
            game={gameListChannel}
            data={dataChannel}
            text1="Tỉ lệ doanh thu"
            text2={`theo kênh phân phối ngày ${moment(
              body.event_time,
              'YYYY-MM-DD',
            ).format('DD/MM/YYYY')}`}
          />
        </View>
        <View
          style={{
            marginLeft: 5,
            flex: 1,
            backgroundColor: 'white',
            borderRadius: 20,
          }}>
          <BranchManagerChartProduct
            game={gameListProduct}
            data={dataProduct}
            text1="Tỉ lệ doanh thu"
            text2={`theo sản phẩm ${moment(
              body.event_time,
              'YYYY-MM-DD',
            ).format('DD/MM/YYYY')}`}
          />
        </View>
      </View>
      <View
        style={{
          marginTop: 10,
        }}>
        <BranchManagerChartBar
          title={`Doanh thu sản phẩm theo kênh phân phối ${moment(
            body.event_time,
            'YYYY-MM-DD',
          ).format('DD/MM/YYYY')}`}
          game={gameListChannel}
          data={dataChart}
        />
      </View>
      <View
        style={{
          marginTop: 10,
        }}>
        <BranchManagerChartBarBranch
          title={`Doanh thu theo sản phẩm theo chi nhánh ${moment(
            body.event_time,
            'YYYY-MM-DD',
          ).format('DD/MM/YYYY')}`}
          game={gameListProduct}
          data={dataChartBranch}
        />
      </View>
      <View
        style={{
          marginTop: 10,
        }}>
        <BranchManagerRevenuaRank
          dataRank={dataRankPOS}
          dataProvince={dataRankProvince}
        />
      </View>
      {/* <View
        style={{
          marginTop: 10,
        }}>
        <BranchManagerChartBarAndLine
          title="Thống kê số lượng Điểm bán hàng"
          game={gameListPos}
          data={dataChartPos}
        />
      </View>
      <View
        style={{
          marginTop: 10,
        }}>
        <BranchManagerChartBarAndLine
          title="Thống kê số lượng tài khoản kênh SMS"
          game={gameListSMS}
          data={dataChartSMS}
        />
      </View> */}
      <View style={{height: 150}} />
    </ScrollView>
  );
};

export default BranchManagerHomeScreen;
