import React, {useEffect, useMemo, useState} from 'react';
import {
  FlatList,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {
  VictoryAxis,
  VictoryBar,
  VictoryChart,
  VictoryLabel,
  VictoryStack,
  VictoryTheme,
} from 'victory-native';
import Sys from '../../components/Sys';
import {formatVNDForChart} from '../../services/util';

const BranchManagerChartBarBranch = ({
  game = [],
  data = [],
  title = '',
  city = true,
}) => {
  const {width} = useWindowDimensions();
  const [currentGame, setCurrentGame] = useState([]);
  const [currentData, setCurrentData] = useState([]);
  const [highestColumnTotal, setHighestColumnTotal] = useState(0);
  const [currentBranchTotals, setCurrentBranchTotals] = useState({});
  const [maximumRevenue, setMaximumRevenue] = useState(0);

  useEffect(() => {
    setCurrentGame(
      game.map((x, index) => {
        return {
          ...x,
          isShow: true,
        };
      }),
    );
  }, [game]);

  const calculateTotalsPerBranch = data => {
    const totals = {};
    data.forEach(gameData => {
      gameData.forEach(({x, y}) => {
        totals[x] = (totals[x] || 0) + y;
      });
    });
    return totals;
  };

  const calculateMaxRevenue = data => {
    let max = 0;
    data.forEach(gameData => {
      gameData.forEach(({y}) => {
        if (y > max) {
          max = y;
        }
      });
    });
    return max;
  };

  const calculateHighestColumnTotal = data => {
    let maxTotal = 0;
    data.forEach(array => {
      array.forEach(item => {
        if (item.y > maxTotal) {
          maxTotal = item.y;
        }
      });
    });
    setHighestColumnTotal(maxTotal);
  };

  const chartHeight = useMemo(() => {
    return width + (maximumRevenue > 2000000000 ? 50 : 10); // Adjusting height based on maximum revenue
  }, [maximumRevenue, width]);

  const renderChart = useMemo(() => {
    const filteredData = currentData.filter(
      (_, index) => currentGame[index]?.isShow,
    );

    return (
      <VictoryChart
        theme={VictoryTheme.material}
        width={width - 40}
        height={chartHeight}
        domainPadding={{x: 20, y: 40}}
        padding={{left: 60, top: 10, bottom: 70, right: 10}}
        domain={{y: [0, maximumRevenue * 2.5]}}>
        <VictoryAxis
          tickLabelComponent={
            !city ? <CustomTickLabelX /> : <CustomTickLabelXCity />
          }
          axisComponent={<></>}
        />
        <VictoryAxis
          dependentAxis
          axisComponent={<></>}
          tickLabelComponent={<CustomTickLabelY />}
        />
        <VictoryStack
          colorScale={(currentGame || [])
            .filter(x => !!x?.color && x?.isShow)
            .map(x => x.color)}>
          {filteredData.map((x, index) => (
            <VictoryBar
              key={index}
              style={{data: {width: 30}}}
              data={x}
              labels={({datum}) =>
                `${
                  datum.y / (highestColumnTotal / 100) > 15
                    ? formatVNDForChart(datum.y)
                    : ''
                }`
              }
              labelComponent={
                <VictoryLabel
                  dy={12}
                  style={{fill: 'black', fontSize: 8}}
                  textAnchor="middle"
                />
              }
            />
          ))}
          <VictoryBar
            data={Object.keys(currentBranchTotals).map(branch => ({
              x: branch,
              y: currentBranchTotals[branch],
            }))}
            labels={({datum}) => `${formatVNDForChart(datum.y)}`}
            labelComponent={
              <VictoryLabel
                dy={({datum}) => {
                  const yDomainMax = maximumRevenue * 2.5;
                  const pixelPerUnit = chartHeight / yDomainMax;
                  const offset = datum.y * pixelPerUnit * 0.1;
                  return offset * 6;
                }}
                style={{fill: 'black', fontSize: 10, fontWeight: 'bold'}}
              />
            }
            style={{
              data: {
                fill: 'transparent',
                // stroke: 'red',
                // strokeWidth: 3,
                width: 30,
              },
            }} // Red border for total bars
          />
        </VictoryStack>
      </VictoryChart>
    );
  }, [
    width,
    currentData,
    currentGame,
    currentBranchTotals,
    maximumRevenue,
    chartHeight,
  ]);

  useEffect(() => {
    try {
      const temps = [];
      let listbranch = [];
      if (data.length > 0) {
        const y = data[0];
        let items = [];
        if (Array.isArray(y.items)) {
          listbranch = y.items.map(x => x.name);
        } else {
          listbranch = Object.values(y.items).map(x => x.name);
        }
      }

      game.map(x => {
        let tempGame = [];

        data
          .filter(y => y.code === x.id)
          .map(y => {
            let items = [];
            if (Array.isArray(y.items)) {
              items = y.items;
            } else {
              items = Object.values(y.items);
            }

            items.forEach(element => {
              tempGame.push({
                x: element.name,
                y: element.total_revenue,
              });
            });
          });
        if (tempGame.length > 0) {
          const sortedData = tempGame.sort((a, b) => {
            const indexA = listbranch.indexOf(a.x);
            const indexB = listbranch.indexOf(b.x);
            if (indexA === -1 && indexB === -1) {
              return 0;
            } // Cả hai không có trong branchList
            if (indexA === -1) {
              return 1;
            } // a không có trong branchList, b ở vị trí cao hơn
            if (indexB === -1) {
              return -1;
            } // b không có trong branchList, a ở vị trí cao hơn
            return indexA - indexB; // So sánh vị trí trong branchList
          });
          temps.push(tempGame);
        }
      });
      calculateHighestColumnTotal(temps);
      const branchTotals = calculateTotalsPerBranch(temps);
      setCurrentBranchTotals(branchTotals);
      setCurrentData(temps);

      const maxRevenue = calculateMaxRevenue(temps);
      setMaximumRevenue(maxRevenue);
    } catch (error) {
      console.log(error);
    }
  }, [data]);

  const toggleGameVisibility = id => {
    const updatedGame = currentGame.map(_game =>
      _game.id === id ? {..._game, isShow: !_game.isShow} : _game,
    );

    setCurrentGame(updatedGame);

    const updatedTotals = {};

    updatedGame.forEach((_game, gameIndex) => {
      if (_game.isShow && currentData[gameIndex]) {
        currentData[gameIndex].forEach(({x, y}) => {
          updatedTotals[x] = (updatedTotals[x] || 0) + y;
        });
      }
    });

    setCurrentBranchTotals(updatedTotals);
  };

  return (
    <View style={{backgroundColor: 'white', borderRadius: 20}}>
      <Sys.Text
        style={{
          color: '#0062FF',
          fontSize: 12,
          fontWeight: '600',
          textAlign: 'center',
          paddingVertical: 15,
        }}>
        {title}
      </Sys.Text>
      {renderChart}
      <View style={{paddingHorizontal: 40}}>
        <FlatList
          scrollEnabled={false}
          data={currentGame}
          numColumns={2}
          keyExtractor={(item, index) => `${index}`}
          renderItem={({item}) => (
            <TouchableOpacity
              onPress={() => toggleGameVisibility(item.id)}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                flex: 1,
                marginBottom: 10,
              }}>
              <View
                style={{
                  backgroundColor: item.isShow ? item.color : `${item.color}50`,
                  height: 10,
                  width: 10,
                }}
              />
              <Sys.Text style={{fontSize: 10, marginLeft: 10}}>
                {item.name}
              </Sys.Text>
            </TouchableOpacity>
          )}
        />
      </View>
    </View>
  );
};

function formatVNDShort(value) {
  if (value === undefined || value < 1000) {
    return '';
  }
  let formattedValue = Number(value.toString().replace(/,/g, '')) / 1000000;
  return `${(formattedValue / 1000).toFixed(2)} Tỷ`;

  // if (formattedValue > 999) {
  //   return `${(formattedValue / 1000).toFixed(2)} Tỷ`;
  // } else {
  //   return `${formattedValue} Tr`;
  // }
}

export default BranchManagerChartBarBranch;
const CustomTickLabelY = props => {
  return (
    <VictoryLabel
      {...props}
      style={{
        fill: '#666',
        fontSize: 10,
      }}
      text={`${formatVNDShort(props.text)}`}
    />
  );
};
const CustomTickLabelX = props => {
  return (
    <VictoryLabel
      {...props}
      style={{
        fill: '#666',
        fontSize: 8,
      }}
    />
  );
};

const CustomTickLabelXCity = props => {
  return (
    <VictoryLabel
      {...props}
      style={{
        fill: '#666',
        fontSize: 8,
      }}
      y={props.y + 20}
      x={props.x - 20}
      verticalAnchor={'start'}
      angle={-45}
    />
  );
};
