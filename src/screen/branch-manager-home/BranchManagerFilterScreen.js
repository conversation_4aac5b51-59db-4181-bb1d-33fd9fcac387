import {useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {
  FlatList,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {Calendar} from 'react-native-calendars';
import ReactNativeModal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import {
  AccountActions,
  AccountSelectors,
} from '../account/services/account.slice';
import CloseIcon from '../report/icons/CloseIcon';
import SelectIcon from '../report/icons/SelectIcon';
import {PosActions} from '../sale-location/services/pos.slice';
import BranchListSkeleton from './BranchListSkeleton';

const BranchManagerFilterScreen = () => {
  const dispatch = useDispatch();
  const {width} = useWindowDimensions();
  const insets = useSafeAreaInsets();
  const {goBack} = useNavigation();
  const globalFilter = useSelector(AccountSelectors.globalFilter);
  const {branches, isLoadingBranch} = useSelector(state => state.pos);
  const [branch, setBranch] = useState(branches);
  const [currentDate, setCurrentDate] = useState({
    isShow: false,
    date: undefined,
  });
  const [branchList, setBranchList] = useState(branches);
  useEffect(() => {
    if (branchList?.length === 0) {
      dispatch(PosActions.getBranch());
    }
    setBranch(globalFilter?.branch || []);
    setCurrentDate({
      isShow: false,
      date: globalFilter?.date,
    });
  }, [globalFilter]);

  useEffect(() => {
    setBranchList(branches);
  }, [branches]);

  const onSave = async () => {
    const currentFilter = {
      date: currentDate.date,
      branch,
    };
    dispatch(AccountActions.setGlobalFilter(currentFilter));
    goBack();
  };

  const onReset = async () => {
    const date = moment().subtract('days', 1).format('YYYY-MM-DD');
    setCurrentDate({
      isShow: false,
      date,
    });
    setBranch([]);
    dispatch(AccountActions.resetGlobalFilter({}));
  };

  return (
    <Sys.Container
      headerColor="white"
      hasHeader
      hasFooter
      backgroundColor="white"
      footerColor="white">
      <ReactNativeModal
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={{
          padding: 0,
          margin: 0,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onBackdropPress={() =>
          setCurrentDate({
            ...currentDate,
            isShow: false,
          })
        }
        isVisible={currentDate.isShow}
        onSwipeComplete={() => {
          setCurrentDate({
            ...currentDate,
            isShow: false,
          });
        }}
        swipeDirection="down">
        <View
          style={{
            flex: 1,
          }}
        />
        <View
          style={{
            backgroundColor: 'white',
            width: width,
            borderRadius: 20,
            padding: 20,
            paddingBottom: insets.bottom + 20,
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
            }}>
            Chọn ngày
          </Sys.Text>
          <Calendar
            markingType={'custom'}
            markedDates={{
              [`${currentDate.date}`]: {
                customStyles: {
                  container: {
                    backgroundColor: 'blue',
                  },
                  text: {
                    color: 'white',
                  },
                },
              },
            }}
            onDayPress={day => {
              setCurrentDate({
                isShow: false,
                date: day.dateString,
              });
            }}
          />
        </View>
      </ReactNativeModal>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <TouchableOpacity
          onPress={goBack}
          activeOpacity={0.8}
          style={{
            padding: 20,
          }}>
          <CloseIcon />
        </TouchableOpacity>
        <Sys.Text
          style={{
            flex: 1,
            textAlign: 'center',
            color: '#0062FF',
            fontSize: 18,
            fontWeight: '600',
          }}>
          Thiết lập báo cáo
        </Sys.Text>
        <View style={{width: 54}} />
      </View>
      <View
        style={{
          paddingTop: 10,
          paddingBottom: 20,
          paddingHorizontal: 20,
          flex: 1,
        }}>
        <View style={{marginBottom: 10}}>
          <Sys.Text
            style={{
              marginBottom: 5,
              textTransform: 'uppercase',
              fontWeight: '600',
              color: '#44444F',
            }}>
            Chọn ngày
          </Sys.Text>
          <TouchableOpacity
            onPress={() => {
              setCurrentDate({
                ...currentDate,
                isShow: true,
              });
            }}
            activeOpacity={0.8}
            style={{
              backgroundColor: 'white',
              borderRadius: 8,
              borderWidth: 1,
              borderColor: '#D1D1D1',
              paddingVertical: 10,
              paddingHorizontal: 20,
            }}>
            <Sys.Text
              style={{
                color: currentDate?.date ? 'black' : '#D1D1D1',
              }}>
              {currentDate?.date
                ? moment(currentDate?.date, 'YYYY-MM-DD').format('DD/MM/YYYY')
                : 'Chọn ngày'}
            </Sys.Text>
            <View
              style={{
                position: 'absolute',
                right: 10,
                top: 10,
              }}>
              <SelectIcon />
            </View>
          </TouchableOpacity>
        </View>
        <View style={{marginBottom: 10}}>
          <Sys.Text
            style={{
              marginBottom: 5,
              textTransform: 'uppercase',
              fontWeight: '600',
              color: '#44444F',
            }}>
            Chi nhánh
          </Sys.Text>
          {isLoadingBranch ? (
            <FlatList
              data={[...Array(5)]}
              keyExtractor={(item, index) => `${index}`}
              renderItem={() => <BranchListSkeleton />}
            />
          ) : (
            <FlatList
              showsVerticalScrollIndicator={false}
              data={branchList}
              keyExtractor={(item, index) => `${index}`}
              renderItem={({item, index}) => {
                return (
                  <OneItem
                    item={item}
                    isChecked={branch.indexOf(item?.id) > -1}
                    onSelected={id => {
                      if (branch.indexOf(id) > -1) {
                        setBranch(branch.filter(x => x !== id));
                      } else {
                        setBranch([...branch, id]);
                      }
                    }}
                  />
                );
              }}
            />
          )}
        </View>
      </View>
      <View
        style={{
          paddingHorizontal: 20,
          paddingVertical: 10,
          backgroundColor: 'white',
          flexDirection: 'row',
        }}>
        <View
          style={{
            marginRight: 10,
            flex: 1,
          }}>
          <TouchableOpacity
            onPress={onReset}
            style={{
              borderWidth: 1,
              borderRadius: 50,
              padding: 10,
              borderColor: '#0062FF',
              alignItems: 'center',
            }}>
            <Sys.Text
              style={{
                color: '#0062FF',
                fontWeight: '600',
              }}>
              Thiết lập lại
            </Sys.Text>
          </TouchableOpacity>
        </View>
        <View
          style={{
            marginLeft: 10,
            flex: 1,
          }}>
          <TouchableOpacity
            onPress={onSave}
            style={{
              borderWidth: 1,
              borderRadius: 50,
              padding: 10,
              borderColor: '#0062FF',
              alignItems: 'center',
              backgroundColor: '#0062FF',
            }}>
            <Sys.Text
              style={{
                color: 'white',
                fontWeight: '600',
              }}>
              Áp dụng
            </Sys.Text>
          </TouchableOpacity>
        </View>
      </View>
    </Sys.Container>
  );
};

export default BranchManagerFilterScreen;

const OneItem = ({item, isChecked, onSelected, index}) => {
  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 10,
      }}>
      <Sys.Checkbox onPress={() => onSelected(item.id)} checked={isChecked} />
      <Sys.Text
        style={{
          marginLeft: 10,
        }}>
        {item?.text}
      </Sys.Text>
    </View>
  );
};
