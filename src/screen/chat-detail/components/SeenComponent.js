import {View, Text, StyleSheet} from 'react-native';
import React from 'react';
import Sys from '../../../components/Sys';
import SeenIcon from '../icons/ScreenIcon';

const SeenComponent = () => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
      marginTop: 10,
    },
    text: {
      fontSize: 10,
      lineHeight: 23,
      color: '#D1D1D1',
      marginLeft: 5,
    },
  });

  return (
    <View style={styles.container}>
      <SeenIcon />
      <Sys.Text style={styles.text}>Đã xem</Sys.Text>
    </View>
  );
};

export default SeenComponent;
