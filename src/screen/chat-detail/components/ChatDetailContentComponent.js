import {View, Text, StyleSheet, FlatList, Image} from 'react-native';
import React from 'react';
import Sys from '../../../components/Sys';
import SeenComponent from './SeenComponent';
import MessagingComponent from './MessagingComponent';

const ChatDetailContentComponent = () => {
  const styles = StyleSheet.create({
    container: {
      padding: 20,
      flex: 1,
    },
    lastTimeView: {
      backgroundColor: '#D1D1D1',
      borderRadius: 50,
      paddingHorizontal: 10,
    },
    lastTime: {color: 'white', lineHeight: 20, fontSize: 12},
    lastTimeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    content: {
      flex: 1,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.lastTimeContainer}>
        <View style={styles.lastTimeView}>
          <Sys.Text style={styles.lastTime}>17:30 01/01/2024</Sys.Text>
        </View>
      </View>
      <View style={styles.content}>
        <FlatList
          data={data}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({item}) => <OneItem item={item} />}
          ListFooterComponent={<SeenComponent />}
        />
      </View>
      <MessagingComponent />
    </View>
  );
};

export default ChatDetailContentComponent;

const OneItem = ({item}) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      marginTop: 20,
    },
    avatar: {
      width: 20,
      height: 20,
      borderRadius: 50,
      marginRight: 5,
    },
    notAvatar: {
      width: 40,
      height: 40,
    },
    blockAvatarRight: {
      width: 20,
      height: 20,
    },
    messageView: {
      paddingHorizontal: 15,
      paddingVertical: 10,
      backgroundColor: '#F1F1F1',
      borderRadius: 10,
      flex: 1,
    },
    messageMeView: {
      paddingHorizontal: 15,
      paddingVertical: 10,
      backgroundColor: '#FFE6E7',
      borderRadius: 10,
      flex: 1,
    },
    message: {lineHeight: 24},
    time: {color: '#B5B8BF', lineHeight: 20, fontSize: 12},
  });
  return (
    <View style={styles.container}>
      {item.me ? (
        <View style={styles.notAvatar} />
      ) : (
        <Image style={styles.avatar} source={{uri: item.avatar}} />
      )}
      <View style={item.me ? styles.messageMeView : styles.messageView}>
        <Sys.Text style={styles.message}>{item.message}</Sys.Text>
        <Sys.Text style={styles.time}>{item.time}</Sys.Text>
      </View>
      {item.me ? <></> : <View style={styles.blockAvatarRight} />}
    </View>
  );
};

const data = [
  {
    id: 1,
    me: false,
    avatar:
      'https://mediacms.thethaovanhoa.vn/Upload/QDN4HPIpMrJuoPNyIvLDA/files/2021/12/Jimin-Insta5.jpg',
    message:
      'Check out our new font generator and level up your social bios. Need more?',
    time: '14:00',
  },
  {
    id: 2,
    me: true,
    avatar: '',
    message:
      'Check out our new font generator and level up your social bios. Need more?',
    time: '14:00',
  },
  {
    id: 3,
    me: false,
    avatar:
      'https://mediacms.thethaovanhoa.vn/Upload/QDN4HPIpMrJuoPNyIvLDA/files/2021/12/Jimin-Insta5.jpg',
    message:
      'Check out our new font generator and level up your social bios. Need more?',
    time: '14:00',
  },
];
