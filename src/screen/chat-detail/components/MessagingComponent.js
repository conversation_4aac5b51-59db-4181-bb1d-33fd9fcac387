import {StyleSheet, View} from 'react-native';
import React from 'react';
import Sys from '../../../components/Sys';
import ImageIcon from '../icons/ImageIcon';
import FileIcon from '../icons/FileIcon';

const MessagingComponent = () => {
  const styles = StyleSheet.create({
    container: {flexDirection: 'row', alignItems: 'center'},
    input: {flex: 1},
    iconView: {marginLeft: 20},
  });

  return (
    <View style={styles.container}>
      <View style={styles.input}>
        <Sys.Input placeholder={'Nhập phản ánh'} />
      </View>
      <View style={styles.iconView}>
        <FileIcon />
      </View>
      <View style={styles.iconView}>
        <ImageIcon />
      </View>
    </View>
  );
};

export default MessagingComponent;
