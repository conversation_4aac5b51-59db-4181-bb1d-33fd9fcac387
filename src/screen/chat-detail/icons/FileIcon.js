import * as React from 'react';
import Svg, {Circle, Path} from 'react-native-svg';

function FileIcon(props) {
  return (
    <Svg
      width={30}
      height={30}
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <Circle cx={15} cy={15} r={15} fill="#F5000D" />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.65 19.126a1.875 1.875 0 01-2.612-2.69l5.833-5.834a.375.375 0 01.53.53l-5.833 5.834a1.125 1.125 0 001.591 1.591l5.834-5.834a2.625 2.625 0 10-3.713-3.712l-5.833 5.834a4.125 4.125 0 005.833 5.833l4.773-4.773a.375.375 0 01.53.53l-4.772 4.774a4.875 4.875 0 01-6.934-6.855L15.75 8.48a3.375 3.375 0 014.773 4.773l-5.873 5.872z"
        fill="#fff"
      />
    </Svg>
  );
}

export default FileIcon;
