import * as React from 'react';
import Svg, {Circle, Path} from 'react-native-svg';

function ImageIcon(props) {
  return (
    <Svg
      width={30}
      height={30}
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <Circle cx={15} cy={15} r={15} fill="#F5000D" />
      <Path
        d="M21.333 8H9.667C8.747 8 8 8.746 8 9.667v11.666C8 22.253 8.746 23 9.667 23h11.666c.92 0 1.667-.746 1.667-1.667V9.667C23 8.747 22.254 8 21.333 8z"
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M12.583 13.833a1.25 1.25 0 100-2.5 1.25 1.25 0 000 2.5zM23 18l-4.167-4.167L9.667 23"
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}

export default ImageIcon;
