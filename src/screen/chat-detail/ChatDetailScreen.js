import {View, Text} from 'react-native';
import React from 'react';
import Sys from '../../components/Sys';
import ChatDetailContentComponent from './components/ChatDetailContentComponent';

const ChatDetailScreen = ({route}) => {
  const {params} = route;

  return (
    <Sys.Container
      hasHeader
      hasFooter
      backgroundColor="white"
      footerColor="white">
      <Sys.Header title={params?.name} />
      <ChatDetailContentComponent />
    </Sys.Container>
  );
};

export default ChatDetailScreen;
