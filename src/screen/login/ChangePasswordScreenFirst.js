import React, {useState} from 'react';
import {StyleSheet, View} from 'react-native';
import Sys from '../../components/Sys';
import THEME from '../../components/theme/theme';
import {useDispatch} from 'react-redux';
import Toast from 'react-native-toast-message';
import {resetNavigation} from '../../services/navigation';
import {LoginActions} from './services/login.slice';
import {AppActions} from '../../app.slice';

export default function ChangePasswordScreenFirst() {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const dispatch = useDispatch();

  const validatePassword = password => {
    return (
      password.length >= 8 &&
      password.length <= 20 &&
      /[A-Z]/.test(password) &&
      /[0-9]/.test(password) &&
      /[!@#$%^&*(),.?":{}|<>]/.test(password)
    );
  };

  const handleSubmit = () => {
    if (!validatePassword(newPassword)) {
      Toast.show({
        type: 'error',
        text1: 'Lỗi',
        text2: '<PERSON><PERSON><PERSON> khẩu không đáp ứng các điều kiện yêu cầu.',
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      Toast.show({
        type: 'error',
        text1: 'Lỗi',
        text2: 'Mật khẩu xác nhận không trùng khớp.',
      });
      return;
    }
    dispatch(AppActions.setLoading(true));

    // Dispatch action để gọi saga
    dispatch(
      LoginActions.changeFirstTimePassword({
        password: newPassword,
        password_confirmation: confirmPassword,
        onSuccess: () => {
          dispatch(AppActions.setLoading(false));

          resetNavigation('DashboardScreen');
        },
        onRequireVerification: () => {
          dispatch(AppActions.setLoading(false));
          resetNavigation('AccountConfirmationScreen');
        },
        onFail: errorMessage => {
          dispatch(AppActions.setLoading(false));
          Toast.show({
            type: 'error',
            text1: 'Lỗi',
            text2: errorMessage || 'Đã xảy ra lỗi khi cập nhật mật khẩu.',
          });
        },
      }),
    );
  };

  return (
    <Sys.Container hasHeader>
      <Sys.Header
        onBack={() => resetNavigation('LoginScreen')}
        title={'Thay đổi mật khẩu'}
      />
      <View style={styles.container}>
        <View style={styles.oneRow}>
          <Sys.Input
            label={'Mật khẩu mới'}
            placeholder={'Nhập mật khẩu mới'}
            value={newPassword}
            onChangeText={setNewPassword}
          />
        </View>
        <View style={styles.oneRow}>
          <Sys.Input
            label={'Nhập lại mật khẩu mới'}
            placeholder={'Nhập lại mật khẩu mới'}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
          />
        </View>
        <View style={styles.btnArea}>
          <Sys.Button onPress={handleSubmit}>Xác nhận</Sys.Button>
        </View>
        <View>
          <Sys.Text style={styles.title}>
            Mật khẩu phải thỏa mãn các điều kiện sau:
          </Sys.Text>
          <Sys.Text style={styles.text}>Có độ dài từ 8 đến 20 kí tự.</Sys.Text>
          <Sys.Text style={styles.text}>
            Chứa ít nhất 01 ký tự số, 01 ký tự chữ Hoa, 01 ký tự đặc biệt
          </Sys.Text>
          <Sys.Text style={styles.text}>
            Ví dụ: V@123456; 123456@V; 123456V@
          </Sys.Text>
        </View>
      </View>
    </Sys.Container>
  );
}

const styles = StyleSheet.create({
  container: {
    margin: 20,
  },
  oneRow: {
    marginBottom: 20,
  },
  btnArea: {
    marginTop: 20,
    marginBottom: 50,
  },
  title: {
    color: '#848484',
    lineHeight: 24,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  text: {
    color: '#848484',
    lineHeight: 24,
    marginLeft: 12,
  },
});
