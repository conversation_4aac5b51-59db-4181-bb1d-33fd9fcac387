import React from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Sys from '../../components/Sys';
import {useDispatch, useSelector} from 'react-redux';
import Toast from 'react-native-toast-message';
import {resetNavigation} from '../../services/navigation';
import {LoginActions} from './services/login.slice';
import {AccountSelectors} from '../account/services/account.slice';
import {AppActions} from '../../app.slice';

export default function AccountConfirmationScreen() {
  const dispatch = useDispatch();

  const userInfo = useSelector(AccountSelectors.profile);

  const handleConfirm = () => {
    dispatch(AppActions.setLoading(true));

    dispatch(
      LoginActions.confirmVerification({
        onSuccess: () => {
          dispatch(AppActions.setLoading(false));

          Toast.show({
            type: 'success',
            text1: '<PERSON>á<PERSON> nhận thành công',
          });
          resetNavigation('DashboardScreen');
        },
        onFail: error => {
          dispatch(AppActions.setLoading(false));

          Toast.show({
            type: 'error',
            text1: 'Lỗi xác nhận',
            text2: error,
          });
        },
      }),
    );
  };

  return (
    <Sys.Container hasHeader>
      <Sys.Header
        onBack={() => resetNavigation('LoginScreen')}
        title={'Xác minh tài khoản'}
      />
      <ScrollView style={styles.scrollView}>
        <View style={{marginTop: 20}} />

        {/* Account Details Section */}
        <Text style={styles.label}>Tên tài khoản</Text>
        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            {userInfo?.username || 'Chưa có email'}
          </Text>
        </View>

        <Text style={styles.label}>Tên người dùng*</Text>
        <View style={styles.infoBox}>
          <Text style={styles.infoText}>{userInfo?.name || 'Chưa có tên'}</Text>
        </View>

        <Text style={styles.label}>Chi nhánh</Text>
        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            {userInfo?.branch?.name || 'Chưa có chi nhánh'}
          </Text>
        </View>

        <Text style={styles.label}>Chức vụ</Text>
        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            {userInfo?.role?.display_name || 'Chưa có chức vụ'}
          </Text>
        </View>

        <Text style={styles.label}>Số điện thoại*</Text>
        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            {userInfo?.phone || 'Chưa có số điện thoại'}
          </Text>
        </View>

        <Text style={styles.label}>Email*</Text>
        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            {userInfo?.email || 'Chưa có email'}
          </Text>
        </View>

        {/* Confirmation Button */}
        <TouchableOpacity style={styles.confirmButton} onPress={handleConfirm}>
          <Text style={styles.confirmButtonText}>Xác nhận</Text>
        </TouchableOpacity>
      </ScrollView>
    </Sys.Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  label: {
    color: '#000000',
    fontSize: 14,
    marginBottom: 10,
    marginLeft: 20,
  },
  infoBox: {
    backgroundColor: '#F1F1F1',
    borderColor: '#D1D1D1',
    borderRadius: 8,
    borderWidth: 1,
    paddingVertical: 16,
    paddingHorizontal: 22,
    marginBottom: 20,
    marginHorizontal: 20,
  },
  infoText: {
    color: '#000000',
    fontSize: 14,
  },
  confirmButton: {
    alignItems: 'center',
    backgroundColor: '#001451',
    borderRadius: 8,
    paddingVertical: 15,
    marginHorizontal: 20,
    marginBottom: 50,
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
