import {useNavigation} from '@react-navigation/native';
import {isEmpty} from 'lodash';
import React, {useState} from 'react';
import {StyleSheet, View} from 'react-native';
import Sys from '../../../components/Sys';

const ConformInformationLogin = () => {
  const {navigate} = useNavigation();
  const username = 'Demo01';

  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');

  const onChangeEmail = e => {
    setEmail(e);
  };

  const onChangePhoneNumber = e => {
    setPhoneNumber(e);
  };

  const onNext = () => {
    navigate('CreatePasswordScreen');
  };

  return (
    <View style={styles.container}>
      <View style={styles.oneField}>
        <Sys.Input label={'Tên đăng nhập'} value={username} editable={false} />
      </View>
      <View style={styles.oneField}>
        <Sys.Input
          placeholder={'Nhập tên người dùng'}
          label={'Tên người dùng'}
          required
          onChangeText={onChangeEmail}
          value={email}
        />
      </View>
      <View style={styles.oneField}>
        <Sys.Input
          placeholder={'Nhập số điện thoại'}
          label={'Số điện thoại'}
          required
          onChangeText={onChangePhoneNumber}
          value={phoneNumber}
        />
      </View>
      <View style={styles.oneField}>
        <Sys.Input
          placeholder={'Nhập Email'}
          label={'Email'}
          required
          onChangeText={onChangeEmail}
          value={email}
        />
      </View>
      <View style={styles.btnArea}>
        <Sys.Button
          onPress={onNext}
          disabled={isEmpty(email) || isEmpty(phoneNumber)}>
          Xác nhận
        </Sys.Button>
      </View>
    </View>
  );
};

export default ConformInformationLogin;

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  oneField: {
    marginBottom: 15,
  },
  btnArea: {
    marginTop: 25,
  },
});
