import {PASSWORD_DEMO, USERNAME_DEMO} from '@env';
import messaging from '@react-native-firebase/messaging';
import {useNavigation} from '@react-navigation/native';
import * as _ from 'lodash';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {
  Alert,
  Image,
  Platform,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import * as Ant from 'react-native-animatable';
import DeviceInfo, {
  getDeviceId,
  getSystemVersion,
} from 'react-native-device-info';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import TouchID from 'react-native-touch-id';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useDispatch, useSelector} from 'react-redux';
import {AppActions, AppSelectors} from '../../app.slice';
import Sys from '../../components/Sys';
import {isIOS, isValidEmail} from '../../services/util';
import {AccountActions} from '../account/services/account.slice';
import ShowPasswordIcon from './icons/ShowPasswordIcon';
import {LoginActions} from './services/login.slice';
import FaceIdIcon from './icons/FaceIdIcon';
import StorageService from '../../services/storageService';
import {resetNavigation} from '../../services/navigation';

const LoginScreen = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const {width} = useWindowDimensions();
  const [checkPassword, setCheckPassword] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isFocus, setIsFocus] = useState(false);
  const [blueBorder, setBlueBorder] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [fbToken, setFbToken] = useState('');
  // const [fbToken, setFbToken] = useState(
  //   'cb7j3KckSVaUzzKAzD9Gpa:APA91bFkWgZ4WlpW-F8pQ_R2JOlIDXx7B8yyeQzHBUjtcb-HvGHR0YnWKXCFgjViZj2dJR1bZ08BAYfDP3o_lJmBTHVeB0VD0WtEJZc4VlPnYpHolcSIwt3krtyycyrUyZ04ZnWdHvpN',
  // );
  const appVersion = DeviceInfo.getVersion();
  const buildNumber = DeviceInfo.getBuildNumber();
  const insets = useSafeAreaInsets();
  const [isBioAuthEnabled, setIsBioAuthEnabled] = useState(false);
  const isConnected = useSelector(AppSelectors.isConnected);

  useEffect(() => {
    if (__DEV__) {
      setUsername(USERNAME_DEMO || '');
      setPassword(PASSWORD_DEMO || '');
    }
    initFilter();
    initUsername();
  }, []);

  const initUsername = async () => {
    const unSS = await StorageService.getUserName();
    if (unSS) {
      setUsername(unSS);
      if (!username.includes('@vietlott.vn')) {
        dispatch(
          LoginActions.checkFirstLogin({
            username: unSS,
            device_id: await getDeviceId(),
            device_token: fbToken,
            onSuccess: data => {
              if (!data?.require_password_change) {
                setCheckPassword(true);
              }
            },
          }),
        );
      } else {
        setCheckPassword(true);
      }
    }
  };

  const initFilter = async () => {
    const filter = await StorageService.getFilters();
    let temp = {
      date: moment().subtract('days', 1).format('YYYY-MM-DD'),
      branch: filter?.branch || [],
    };
    dispatch(AccountActions.setGlobalFilter(temp));
  };

  const initCheckBio = async () => {
    const currentUsername = await StorageService.getUserName();
    const currentBioCheck = await StorageService.getBio();
    const bioStatus =
      currentBioCheck &&
      currentUsername.toLowerCase() === username.toLowerCase();
    setIsBioAuthEnabled(bioStatus);
    return bioStatus;
  };

  const handleTokenError = () => {
    Alert.alert(
      'Thông báo',
      'Không thể lấy Token từ Firebase. Các tính năng thông báo sẽ không hoạt động do thiếu Token. vui lòng thử lại.',
      [
        {
          text: 'Thử lại',
          onPress: () => {
            requestFirebaseToken(); // Retry fetching the token
          },
        },
        {
          text: 'Hủy',
          onPress: () => resetNavigation('LoginScreen'),
          style: 'cancel',
        },
      ],
      {cancelable: false},
    );
  };

  const requestFirebaseToken = (retryCount = 0) => {
    // console.log('requestFirebaseToken');
    messaging()
      .getToken()
      .then(token => {
        if (token) {
          // console.log('FCM Token:', token);
          setFbToken(token);
          dispatch(AppActions.setDeviceToken(token));
        } else if (retryCount < 3) {
          console.log('FCM Token not available, retrying...');
          setTimeout(() => requestFirebaseToken(retryCount + 1), 1000);
        } else {
          handleTokenError();
        }
      })
      .catch(error => {
        console.error('FCM Token error:', error);
        if (retryCount < 3) {
          setTimeout(() => requestFirebaseToken(retryCount + 1), 1000);
        } else {
          handleTokenError();
        }
      });
  };

  async function requestUserPermission() {
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;
    // console.log('requestUserPermission', {enabled});
    if (enabled) {
      // console.log('Authorization status:', authStatus);
      requestFirebaseToken();
    }
  }

  const getDeviceInfo = async () => {
    const device_id = `${getDeviceId()} ${getSystemVersion()}`;
    const device_name = await DeviceInfo.getDeviceName();
    const deviceUniqueId = await DeviceInfo.getUniqueId();
    const device_token = fbToken;

    return {device_id, device_token, device_name, deviceUniqueId};
  };

  useEffect(() => {
    requestUserPermission();
    requestFirebaseToken();
    initCheckBio();
  }, [username, isConnected]);

  const handleBio = async () => {
    if (isBioAuthEnabled) {
      const checkBio = await TouchID.authenticate(
        'Đăng nhập bằng sinh trắc học',
      );
      if (checkBio) {
        const {device_id, device_token, device_name, deviceUniqueId} =
          await getDeviceInfo();
        if (!device_token) {
          requestFirebaseToken();
          return;
        }
        dispatch(
          LoginActions.getRefreshToken({
            body: {
              email: username.toLocaleLowerCase(),
              password,
              device_id,
              deviceUniqueId,
              device_token,
              platform: Platform.OS,
              device_name,
            },
            onSuccess: rs => {
              navigation.navigate('DashboardScreen');
            },
            onFail: message => {
              Toast.show({
                type: 'error',
                text1: message,
              });
            },
          }),
        );
      }
    }
  };

  const {navigate} = useNavigation();
  const onLogin = async () => {
    const {device_id, device_token, device_name, deviceUniqueId} =
      await getDeviceInfo();

    if (checkPassword) {
      if (!_.isEmpty(password)) {
        try {
          if (!device_token) {
            requestFirebaseToken();
            return;
          }
          const requestObject = {
            onSuccess: rs => {
              // navigate('DashboardScreen');
              // dispatch(AccountActions.getProfile());
            },
            onFail: error => {
              console.log('onLogin onFail', error);
              Toast.show({
                type: 'error',
                text1: 'Thông báo',
                text2: error,
              });
            },
          };
          if (isValidEmail(username)) {
            console.log('loginByPassword');
            dispatch(
              LoginActions.loginByPassword({
                ...requestObject,
                body: {
                  email: username.toLocaleLowerCase(),
                  password,
                  device_id,
                  deviceUniqueId,
                  device_token,
                  platform: Platform.OS,
                  device_name,
                },
              }),
            );
          } else {
            console.log('guestLoginByPassword');

            dispatch(
              LoginActions.guestLoginByPassword({
                ...requestObject,
                body: {
                  username: username.toLocaleLowerCase(),
                  password,
                  device_id,
                  deviceUniqueId,
                  device_token,
                  platform: Platform.OS,
                  device_name,
                },
              }),
            );
          }
        } catch (error) {
          console.log('onLogin catch', error);
        }
      }
    } else {
      if (_.isEmpty(username)) {
        return;
      }

      if (!username.includes('@vietlott.vn')) {
        try {
          dispatch(AppActions.setLoading(true)); // Show loading effect
          dispatch(
            LoginActions.checkFirstLogin({
              username,
              device_id,
              device_token,
              onRequireOtp: ({expires_at, phone}) => {
                dispatch(AppActions.setLoading(false));

                navigate('OtpInputScreen', {
                  username,
                  expires_at,
                  phone,
                  device_token,
                  device_id,
                });
              },
              onSuccess: () => {
                dispatch(AppActions.setLoading(false));
                setIsFocus(false);
                setCheckPassword(true);
              },
              onFail: error => {
                dispatch(AppActions.setLoading(false));
                Toast.show({type: 'error', text1: 'Error', text2: error});
              },
            }),
          );
        } catch (error) {
          dispatch(AppActions.setLoading(false));
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: error?.message || 'Failed to check login status.',
          });
        }
        return;
      }

      setIsFocus(false);
      setCheckPassword(true);
    }
  };

  const togglePasswordVisibility = () => {
    if (isBioAuthEnabled) {
      handleBio();
    } else {
      setIsShow(!isShow);
    }
  };

  return (
    <Sys.Container backgroundColor="white">
      {checkPassword && (
        <Ant.View
          animation={'fadeIn'}
          style={{
            flex: 1,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              height: 30,
              top: 10,
              zIndex: 20,
            }}>
            <TouchableOpacity
              style={{
                left: 10,
                zIndex: 20,
                height: 30,
              }}
              onPress={() => {
                setCheckPassword(false);
              }}>
              <MaterialCommunityIcons
                color={'black'}
                size={30}
                name="chevron-left"
              />
            </TouchableOpacity>
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                height: 30,
                flex: 1,
              }}>
              <Sys.Text
                style={{
                  color: '#001451',
                  fontSize: 18,
                  fontWeight: '600',
                }}>
                Đăng nhập
              </Sys.Text>
            </View>
            <View style={{width: 30}} />
          </View>
          <View
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              marginTop: 240,
              alignItems: 'center',
              zIndex: 18,
            }}>
            <View
              style={{
                width: width - 40,
                marginHorizontal: 20,
              }}>
              {checkPassword ? (
                <View>
                  <Sys.Text
                    style={{
                      marginBottom: 20,
                      fontSize: 16,
                      fontWeight: '500',
                      color: '#001451',
                      textAlign: 'center',
                    }}>
                    {username}
                  </Sys.Text>
                </View>
              ) : (
                <></>
              )}

              {checkPassword ? (
                <View
                  style={{
                    // borderRadius: 10,
                    // borderWidth: 1,
                    // borderColor: blueBorder ? '#0062FF' : '#848484',
                    marginBottom: 20,
                    height: 50,
                    backgroundColor: 'white',
                    justifyContent: 'center',
                    position: 'relative',
                  }}>
                  {(isFocus || !_.isEmpty(password)) && (
                    <Sys.Text
                      style={{
                        fontSize: 12,
                        color: '#848484',
                        lineHeight: 20,
                      }}>
                      Mật khẩu đăng nhập
                    </Sys.Text>
                  )}
                  <Sys.Input
                    onFocus={() => {
                      setIsFocus(true);
                      setBlueBorder(true);
                    }}
                    onBlur={() => {
                      setIsFocus(false);
                      setBlueBorder(false);
                    }}
                    secureTextEntry={!isShow}
                    value={password}
                    onChangeText={e => setPassword(e)}
                    style={Platform.select({
                      ios: {
                        paddingHorizontal: 20,
                      },
                      android: {
                        padding: 0,
                      },
                    })}
                    placeholder={
                      !isFocus && _.isEmpty(password)
                        ? 'Mật khẩu đăng nhập'
                        : ''
                    }
                  />
                  <TouchableOpacity
                    style={{
                      position: 'absolute',
                      right: 10,
                      top: _.isEmpty(password)
                        ? isFocus
                          ? '40%'
                          : '30%'
                        : '45%',
                    }}
                    onPress={togglePasswordVisibility}>
                    {isBioAuthEnabled ? <FaceIdIcon /> : <ShowPasswordIcon />}
                  </TouchableOpacity>
                </View>
              ) : (
                <></>
              )}

              <TouchableOpacity
                style={{
                  backgroundColor: checkPassword
                    ? _.isEmpty(password)
                      ? '#848484'
                      : '#0062FF'
                    : _.isEmpty(username)
                    ? '#848484'
                    : '#0062FF',
                  height: isIOS ? 50 : 40,
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: 10,
                }}
                onPress={onLogin}>
                <Sys.Text
                  style={{
                    color: 'white',
                  }}>
                  Tiếp tục
                </Sys.Text>
              </TouchableOpacity>
            </View>
          </View>
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Image source={require('./icons/banner.png')} />
          </View>
          <View style={{flex: 1}} />
          <Ant.View
            duration={500}
            animation={'fadeIn'}
            style={{
              flex: 2,
              bottom: -insets.bottom,
            }}>
            <Image
              style={{
                width: '100%',
                height: '100%',
              }}
              source={require('./icons/Logo.png')}
            />
          </Ant.View>
        </Ant.View>
      )}
      {!checkPassword && (
        <Ant.View
          style={{
            flex: 1,
          }}
          animation={'slideInUp'}>
          <View
            style={{
              top: 10,
              left: 10,
              zIndex: 20,
              height: 30,
            }}
          />
          <View
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              marginTop: 240,
              alignItems: 'center',
              zIndex: 18,
            }}>
            <View
              style={{
                width: width - 40,
                marginHorizontal: 20,
              }}>
              {checkPassword ? (
                <></>
              ) : (
                <TouchableOpacity
                  onPress={() => {
                    setIsFocus(false);
                    setBlueBorder(false);
                  }}
                  activeOpacity={1}
                  style={{
                    // borderRadius: 10,
                    // borderWidth: 1,
                    // borderColor: blueBorder ? '#0062FF' : '#848484',
                    marginBottom: 20,
                    backgroundColor: 'white',
                    justifyContent: 'center',
                  }}>
                  {(isFocus || !_.isEmpty(username)) && (
                    <Sys.Text
                      style={{
                        fontSize: 12,
                        color: '#848484',
                        lineHeight: 20,
                        // paddingHorizontal: 20,
                      }}>
                      Tài khoản đăng nhập
                    </Sys.Text>
                  )}
                  <Sys.Input
                    onFocus={() => {
                      setIsFocus(true);
                      setBlueBorder(true);
                    }}
                    onBlur={() => {
                      setIsFocus(false);
                      setBlueBorder(false);
                    }}
                    value={username}
                    onChangeText={e => setUsername(e)}
                    style={Platform.select({
                      ios: {
                        paddingHorizontal: 20,
                      },
                      android: {
                        padding: 0,
                        margin: 0,
                      },
                    })}
                    placeholder={
                      !isFocus && _.isEmpty(username)
                        ? 'Tài khoản đăng nhập'
                        : ''
                    }
                  />
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={{
                  backgroundColor: checkPassword
                    ? _.isEmpty(password)
                      ? '#848484'
                      : '#0062FF'
                    : _.isEmpty(username)
                    ? '#848484'
                    : '#0062FF',
                  height: isIOS ? 50 : 40,
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: 10,
                }}
                onPress={onLogin}>
                <Sys.Text
                  style={{
                    color: 'white',
                  }}>
                  Tiếp tục
                </Sys.Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  navigate('RegisterAccountScreen');
                }}
                style={{
                  alignItems: 'center',
                  padding: 10,
                  marginTop: 20,
                }}>
                <Sys.Text>Đăng ký tài khoản</Sys.Text>
              </TouchableOpacity>
            </View>
          </View>
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Image source={require('./icons/banner.png')} />
          </View>
          <View style={{flex: 1}} />
          <View
            style={{
              position: 'absolute',
              bottom: isIOS ? 0 : insets.bottom + 10,
              right: 10,
              zIndex: 20,
            }}>
            <Sys.Text
              style={{
                fontSize: 8,
              }}>
              version: {appVersion}-{buildNumber}
            </Sys.Text>
          </View>
          <Ant.View
            duration={500}
            animation={'slideInUp'}
            style={{
              flex: 2,
              bottom: -insets.bottom,
            }}>
            <Image
              style={{
                width: '100%',
                height: '100%',
              }}
              source={require('./icons/Logo.png')}
            />
          </Ant.View>
        </Ant.View>
      )}
    </Sys.Container>
  );
};
export default LoginScreen;
