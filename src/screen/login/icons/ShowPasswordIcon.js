import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const ShowPasswordIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={23}
    fill="none"
    {...props}>
    <Path
      fill="#848484"
      d="m18.724 11.758 1.704-1.741c.124.474.197.97.197 1.483 0 3.17-2.523 5.75-5.625 5.75-.502 0-.987-.074-1.451-.201l1.704-1.742a3.697 3.697 0 0 0 2.387-1.109 3.854 3.854 0 0 0 1.084-2.44Zm11.158-.723c-.115-.213-1.75-3.142-4.948-5.624L23.57 6.805a19.019 19.019 0 0 1 4.39 4.697c-1.112 1.729-5.47 7.665-12.96 7.665-1.123 0-2.168-.147-3.15-.382l-1.54 1.575c1.415.444 2.972.723 4.69.723 9.814 0 14.68-8.745 14.882-9.117a.975.975 0 0 0 0-.931Zm-3.907-9.4L5.35 22.72a.925.925 0 0 1-1.325 0 .958.958 0 0 1-.275-.677.976.976 0 0 1 .275-.678l2.624-2.682C2.407 16.064.25 12.21.119 11.966a.975.975 0 0 1 0-.932C.32 10.662 5.185 1.917 15 1.917a15.5 15.5 0 0 1 6.614 1.466L24.65.281a.923.923 0 0 1 1.325 0 .973.973 0 0 1 0 1.355ZM8.047 17.254l2.373-2.427A5.798 5.798 0 0 1 9.375 11.5c0-3.17 2.523-5.75 5.625-5.75 1.213 0 2.334.398 3.254 1.068l1.92-1.962A13.543 13.543 0 0 0 15 3.833C7.51 3.833 3.152 9.77 2.04 11.5c.706 1.093 2.734 3.86 6.007 5.754Zm3.734-3.818 5.112-5.225A3.648 3.648 0 0 0 15 7.667c-2.068 0-3.75 1.72-3.75 3.833 0 .709.202 1.365.531 1.935Z"
    />
  </Svg>
);
export default ShowPasswordIcon;
