import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

function TouchIdIcon(props) {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={23}
      height={25}
      viewBox="0 0 23 25"
      fill="none"
      {...props}>
      <Path
        d="M20.333 5.017a.773.773 0 01-.526-.205c-2.299-2.095-5.249-3.25-8.307-3.25-3.058 0-6.008 1.155-8.307 3.252A.783.783 0 012.14 3.658C4.728 1.3 8.052 0 11.5 0c3.448 0 6.772 1.299 9.36 3.658a.781.781 0 01-.527 1.359z"
        fill="#000"
      />
      <Path
        d="M21.656 10.122a.777.777 0 01-.649-.346c-2.169-3.23-5.723-5.157-9.507-5.157S4.161 6.547 1.993 9.776a.781.781 0 01-1.298-.87C3.155 5.243 7.194 3.056 11.5 3.056s8.346 2.187 10.805 5.85a.78.78 0 01-.649 1.215z"
        fill="#000"
      />
      <Path
        d="M9.032 25a.78.78 0 01-.555-.231c-.17-.172-4.174-4.259-4.174-8.193 0-4.083 3.228-7.405 7.197-7.405s7.197 3.322 7.197 7.405a.781.781 0 01-1.563 0c0-3.222-2.527-5.843-5.634-5.843s-5.634 2.621-5.634 5.843c0 3.299 3.684 7.055 3.721 7.093a.781.781 0 01-.555 1.33z"
        fill="#000"
      />
      <Path
        d="M13.968 25c-1.88 0-6.236-3.575-6.658-7.556-.149-1.408.3-2.847 1.197-3.851.782-.873 1.837-1.358 2.971-1.364h.022a4.16 4.16 0 012.948 1.225 4.37 4.37 0 011.288 3.123v.51c0 1.255.978 2.276 2.18 2.276 1.202 0 2.18-1.021 2.18-2.277v-.178c0-4.864-3.524-8.911-8.023-9.211-2.346-.16-4.596.65-6.332 2.274-1.802 1.685-2.836 4.094-2.836 6.606a9.104 9.104 0 001.971 5.67.78.78 0 11-1.222.975 10.68 10.68 0 01-2.312-6.646c0-2.944 1.214-5.768 3.33-7.747 2.055-1.923 4.72-2.879 7.503-2.693 5.317.356 9.482 5.087 9.482 10.771v.178c0 2.117-1.679 3.839-3.742 3.839-2.064 0-3.742-1.722-3.742-3.839v-.509c0-.769-.294-1.483-.827-2.015a2.601 2.601 0 00-1.846-.77h-.015c-.88.004-1.473.461-1.814.843-.61.68-.912 1.67-.808 2.645.344 3.248 4.077 6.16 5.104 6.16a.781.781 0 010 1.561z"
        fill="#000"
      />
      <Path
        d="M16.867 23.472c-2.796 0-6.21-3.417-6.21-7.405a.781.781 0 011.563 0c0 3.091 2.599 5.842 4.647 5.842a.781.781 0 110 1.563z"
        fill="#000"
      />
    </Svg>
  );
}

export default TouchIdIcon;
