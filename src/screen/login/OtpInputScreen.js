import React, {useEffect, useRef, useState} from 'react';
import {
  AppState,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {useNavigation, useRoute} from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import {LoginActions} from './services/login.slice';
import {AppActions} from '../../app.slice';
import Sys from '../../components/Sys';
import {resetNavigation} from '../../services/navigation';

const OtpInputScreen = () => {
  const {params} = useRoute();
  const {username, expires_at, phone, device_token, device_id} = params;
  const [otp, setOtp] = useState(Array(6).fill(''));
  const [timer, setTimer] = useState(0);
  const [expiresAt, setExpiresAt] = useState(expires_at);
  const dispatch = useDispatch();
  const {navigate} = useNavigation();
  const inputRefs = useRef([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const appState = useRef(AppState.currentState);
  useEffect(() => {
    const calculateTimer = () => {
      if (expiresAt) {
        const currentTime = Math.floor(Date.now() / 1000); // Convert to seconds
        const remainingTime = expiresAt - currentTime;
        setTimer(remainingTime > 0 ? remainingTime : 0);
      }
    };

    // Initial calculation
    calculateTimer();
    const interval = setInterval(() => {
      setTimer(prev => (prev > 0 ? prev - 1 : 0));
    }, 1000);

    const appStateListener = AppState.addEventListener(
      'change',
      nextAppState => {
        if (
          appState.current.match(/inactive|background/) &&
          nextAppState === 'active'
        ) {
          // Recalculate timer when returning to the app
          calculateTimer();
        }
        appState.current = nextAppState;
      },
    );

    return () => {
      clearInterval(interval);
      appStateListener.remove();
    };
  }, [expiresAt]);

  const handleOtpSubmit = otpCode => {
    if (isSubmitting) {
      return;
    }
    setIsSubmitting(true);
    dispatch(AppActions.setLoading(true));

    dispatch(
      LoginActions.confirmOtp({
        username,
        device_token,
        device_id,
        otp: otpCode,
        onRequirePasswordChange: () => {
          dispatch(AppActions.setLoading(false));
          navigate('ChangePasswordScreenFirst');
          setIsSubmitting(false);
        },
        onSuccess: () => {
          setIsSubmitting(false);
          dispatch(AppActions.setLoading(false));
          navigate('DashboardScreen');
        },
        onFail: error => {
          setIsSubmitting(false);
          dispatch(AppActions.setLoading(false));
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: error || 'OTP verification failed.',
          });
        },
      }),
    );
  };

  const handleOtpChange = (value, index) => {
    if (!/^\d*$/.test(value)) {
      return;
    } // Ensure only numeric input is allowed.

    const newOtp = [...otp];

    // Handle pasting or fast typing multiple digits
    const digits = value.split('');
    for (let i = 0; i < digits.length; i++) {
      if (index + i < 6) {
        newOtp[index + i] = digits[i];
      }
    }

    setOtp(newOtp);

    // Move focus to the next input that is empty
    const nextIndex = index + digits.length;
    if (nextIndex < 6 && digits.length === 1) {
      inputRefs.current[nextIndex].focus();
    }

    // Submit OTP when all fields are filled.
    if (newOtp.every(char => char !== '') && newOtp.join('').length === 6) {
      handleOtpSubmit(newOtp.join(''));
    }
  };

  const handleKeyPress = (e, index) => {
    if (e.nativeEvent.key === 'Backspace') {
      e.preventDefault(); // Prevent default backspace behavior

      const newOtp = [...otp];

      if (otp[index] === '') {
        // If current input is empty, clear the previous input and move focus back
        if (index > 0) {
          newOtp[index - 1] = '';
          setOtp(newOtp);
          inputRefs.current[index - 1].focus();
        }
      } else {
        // If current input has a value, clear it
        newOtp[index] = '';
        setOtp(newOtp);
      }
    }
  };

  const resendOtp = () => {
    if (timer === 0) {
      dispatch(AppActions.setLoading(true)); // Show loading indicator for resend

      dispatch(
        LoginActions.loginByOtp({
          username,
          device_token,
          device_id,
          onSuccess: ({expires_at}) => {
            setExpiresAt(expires_at);
            dispatch(AppActions.setLoading(false));
          },
          onFail: error => {
            dispatch(AppActions.setLoading(false));
            Toast.show({
              type: 'error',
              text1: 'Error',
              text2: error || 'Failed to resend OTP.',
            });
          },
        }),
      );
    }
  };

  const formatTimer = () => {
    const minutes = Math.floor(timer / 60);
    const seconds = timer % 60;
    return `${
      minutes > 0 ? `${minutes < 10 ? `0${minutes}` : minutes}` : '00'
    }:${seconds < 10 ? `0${seconds}` : seconds}`;
  };

  return (
    <Sys.Container hasHeader>
      <Sys.Header
        onBack={() => resetNavigation('LoginScreen')}
        title={'Nhập mã OTP'}
      />
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Nhập mã OTP</Text>
          <Text style={styles.subtitle}>
            Nhập mã xác thực OTP được gửi đến số điện thoại
          </Text>
          <Text style={styles.phoneNumber}>{phone}</Text>
        </View>

        <View style={styles.otpContainer}>
          {otp.map((_, index) => (
            <TextInput
              key={index}
              ref={el => (inputRefs.current[index] = el)}
              style={styles.otpBox}
              maxLength={1}
              keyboardType="number-pad"
              value={otp[index]}
              onChangeText={value => handleOtpChange(value, index)}
              onKeyPress={e => handleKeyPress(e, index)}
              blurOnSubmit={false}
              autoFocus={index === 0}
            />
          ))}
        </View>

        {timer > 0 ? (
          <Text style={styles.resendText}>Gửi lại OTP ({formatTimer()})</Text>
        ) : (
          <TouchableOpacity onPress={resendOtp} style={styles.resendButton}>
            <Text style={styles.resendButtonText}>Gửi lại OTP</Text>
          </TouchableOpacity>
        )}
      </View>
    </Sys.Container>
  );
};

export default React.memo(OtpInputScreen);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#001451',
    padding: 20,
    alignItems: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  subtitle: {
    color: '#FFFFFF',
    fontSize: 15,
    textAlign: 'center',
  },
  phoneNumber: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 10,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 20,
    gap: 5,
  },
  otpBox: {
    width: 50,
    height: 50,
    backgroundColor: '#FDFDFD',
    borderColor: '#D1D1D1',
    borderRadius: 10,
    borderWidth: 1,
    textAlign: 'center',
    fontSize: 18,
    color: '#000000',
    fontWeight: 'bold',
  },
  resendText: {
    color: '#FFFFFF',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
  resendButton: {
    alignItems: 'center',
    paddingVertical: 10,
  },
  resendButtonText: {
    color: '#00A8FF',
    fontSize: 16,
  },
});
