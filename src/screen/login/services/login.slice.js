import {createSlice} from '@reduxjs/toolkit';

const initialState = {};

const reducers = {
  handleRegistration: (state, payload) => {},
  loginByPassword: (state, payload) => {},
  getRefreshToken: (state, payload) => {},
  guestLoginByPassword: (state, payload) => {},
  checkFirstLogin: (state, action) => {},
  loginByOtp: (state, action) => {},
  confirmOtp: (state, action) => {},
  confirmVerification: (state, action) => {},
  changeFirstTimePassword: (state, action) => {},
};

const LoginSlice = createSlice({
  name: 'login',
  initialState,
  reducers,
});

const LoginReducers = LoginSlice.reducer;

export default LoginReducers;

export const LoginActions = LoginSlice.actions;

export const LoginSelectors = LoginSlice.selectors;
