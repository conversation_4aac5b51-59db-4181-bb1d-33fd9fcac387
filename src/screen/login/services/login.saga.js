import {
  all,
  call,
  delay,
  put,
  takeLatest,
  takeLeading,
} from 'redux-saga/effects';
import {AppActions} from '../../../app.slice';
import SysFetch, {setConfigAxios} from '../../../services/fetch';
import {LoginActions} from './login.slice';
import {AccountActions} from '../../account/services/account.slice';
import StorageService from '../../../services/storageService';
import {resetNavigation} from '../../../services/navigation';

function* LoginSaga() {
  yield takeLeading(LoginActions.loginByPassword, loginByPassword);
  yield takeLeading(LoginActions.getRefreshToken, getRefreshToken);
  yield takeLeading(LoginActions.guestLoginByPassword, guestLoginByPassword);
  yield takeLeading(LoginActions.handleRegistration, handleRegistration);
  yield takeLatest(LoginActions.checkFirstLogin, checkFirstLoginSaga);
  yield takeLatest(LoginActions.loginByOtp, loginByOtpSaga);
  yield takeLatest(LoginActions.confirmOtp, confirmOtpSaga);
  yield takeLatest(
    LoginActions.changeFirstTimePassword,
    changeFirstTimePasswordSaga,
  );
  yield takeLatest(LoginActions.confirmVerification, confirmVerificationSaga);
}

export default LoginSaga;

function* confirmVerificationSaga({payload}) {
  const {onSuccess, onFail} = payload;

  try {
    const response = yield call(SysFetch.post, 'update-verify-first-login');

    if (response.success) {
      onSuccess(); // Chuyển hướng đến DashboardScreen
    } else {
      onFail(response.error || 'Xác minh tài khoản thất bại.');
    }
  } catch (error) {
    onFail(
      error?.response?.data?.message ||
        'Đã xảy ra lỗi trong quá trình xác minh tài khoản.',
    );
  }
}

function* changeFirstTimePasswordSaga({payload}) {
  const {
    password,
    password_confirmation,
    token,
    onFail,
    onRequireVerification,
    onSuccess,
  } = payload;

  try {
    setConfigAxios(token);
    const response = yield call(SysFetch.post, 'first-time-password-change', {
      password,
      password_confirmation,
    });

    if (response.success) {
      if (response.require_verification) {
        onRequireVerification();
      } else {
        onSuccess();
      }
    } else {
      onFail(response.error || 'Đã xảy ra lỗi khi cập nhật mật khẩu.');
    }
  } catch (error) {
    onFail(
      error?.response?.data?.error ||
        error?.response?.data?.message ||
        'Đã xảy ra lỗi khi cập nhật mật khẩu.',
    );
  }
}

function* checkFirstLoginSaga({payload}) {
  const {
    username,
    device_id,
    device_token,
    onFail,
    onRequireOtp,
    onRequireVerification,
    onLoginSuccess,
    onSuccess,
  } = payload;

  try {
    const response = yield call(SysFetch.post, 'check-first-login', {username});
    const {require_password_change, require_verification} = response;

    if (require_password_change && onRequireOtp) {
      yield call(loginByOtpSaga, {
        payload: {
          username,
          device_id,
          device_token,
          onFail,
          onSuccess: onRequireOtp,
        },
      });
    } else if (require_verification && onRequireVerification) {
      onRequireVerification();
    } else if (onLoginSuccess) {
      onLoginSuccess();
    } else if (onSuccess) {
      onSuccess(response);
    }
  } catch (error) {
    console.log(error);
    if (onFail) {
      onFail(
        error?.response?.data?.message ||
          error?.message ||
          'Failed to check login status.',
      );
    }
  }
}

function* loginByOtpSaga({payload}) {
  const {username, device_id, device_token, onFail, onSuccess} = payload;

  try {
    const response = yield call(SysFetch.post, 'login/required-otp', {
      username,
      device_id,
      device_token,
    });
    if (onSuccess) {
      onSuccess(response);
    }
  } catch (error) {
    if (onFail) {
      onFail(error?.response?.data?.message || 'Failed to send OTP.');
    }
  }
}

function* confirmOtpSaga({payload}) {
  const {
    username,
    otp,
    onRequirePasswordChange,
    onSuccess,
    onFail,
    device_token,
    device_id,
  } = payload;

  try {
    const response = yield call(SysFetch.post, 'login/login-by-otp', {
      username,
      otp,
      device_token,
      device_id,
    });
    console.log('response confirmOtpSaga', response);

    if (response.require_password_change) {
      const {success, error} = yield call(
        fetchBioLoginData,
        response.token,
        device_token,
        username,
      );
      if (success && onRequirePasswordChange) {
        onRequirePasswordChange();
      } else {
        throw new Error(error);
      }
    } else {
      if (onSuccess) {
        onSuccess();
      }
    }
  } catch (error) {
    console.log('EX confirmOtpSaga', error);
    if (onFail) {
      onFail(error?.response?.data?.message || 'OTP verification failed.');
    }
  }
}

function* getRefreshToken({payload}) {
  const {onSuccess, onFail, body} = payload;

  try {
    yield put(AppActions.setLoading(true));
    yield delay(100);

    const [refreshToken, deviceToken, accessToken] = yield all([
      call(StorageService.getRefreshToken),
      call(StorageService.getDeviceToken),
      call(StorageService.getAccessToken),
    ]);

    setConfigAxios(accessToken);

    const {token, refresh_token, view, user} = yield call(
      SysFetch.post,
      'bio/refresh',
      {
        refresh_token: refreshToken,
        device_token: body?.device_token || deviceToken,
      },
    );

    yield put(AccountActions.setView(view));
    yield put(AccountActions.setProfile(user));
    yield call(StorageService.setAccessToken, token);
    yield call(StorageService.setRefreshToken, refresh_token);
    setConfigAxios(token);
    yield put(AppActions.setLoading(false));

    if (onSuccess) {
      onSuccess(token);
    }
  } catch (error) {
    console.log('getRefreshToken EX', {error});
    yield put(AppActions.setLoading(false));
    const message = error?.response?.data?.message || 'Có lỗi vui lòng thử lại';
    if (onFail) {
      onFail(message);
    }
  }
}

function* loginByPassword({payload}) {
  console.log('saga loginByPassword');
  yield call(handleLogin, 'login/vietlott', payload);
}

function* guestLoginByPassword({payload}) {
  yield call(handleLogin, 'login/dms', payload);
}

function* handleLogin(endpoint, {body, onSuccess, onFail}) {
  try {
    yield put(AppActions.setLoading(true));
    yield delay(100);

    const rs = yield call(SysFetch.post, endpoint, body);

    const [userNameSS, bioCheck] = yield all([
      call(StorageService.getUserName),
      call(StorageService.getBio),
    ]);
    const _username = body.email?.toLowerCase() || body.username?.toLowerCase();

    yield call(
      StorageService.setBioEnable,
      bioCheck && _username === userNameSS?.toLowerCase(),
    );

    if (rs?.token) {
      const {success, error} = yield call(
        fetchBioLoginData,
        rs.token,
        body.device_token,
        _username,
      );
      if (success) {
        // Gọi checkFirstLoginSaga sau khi đăng nhập thành công
        yield call(checkFirstLoginSaga, {
          payload: {
            username: _username,
            device_id: body.device_id,
            device_token: body.device_token,
            onRequireVerification: () => {
              resetNavigation('AccountConfirmationScreen');
            },
            onLoginSuccess: () => {
              resetNavigation('DashboardScreen');
            },
            onFail: onFail,
          },
        });
        yield put(AppActions.setLoading(false));
      } else {
        throw new Error(error);
      }
    } else {
      yield put(AppActions.setLoading(false));
      if (onFail) {
        onFail(rs?.message || 'Có lỗi vui lòng thử lại');
      }
    }
  } catch (error) {
    console.error('Login error:', error);
    yield put(AppActions.setLoading(false));
    if (onFail) {
      onFail(error?.response?.data?.message || 'Có lỗi vui lòng thử lại');
    }
  }
}

function* handleRegistration({payload}) {
  const {body, onSuccess, onFail} = payload;

  try {
    yield put(AppActions.setLoading(true));
    yield delay(100);

    // Call the registration API
    const rs = yield call(SysFetch.post, 'register', body);

    if (rs?.token) {
      const {success, error} = yield call(
        fetchBioLoginData,
        rs.token,
        body.device_token,
        body.username,
      );
      if (success) {
        resetNavigation('DashboardScreen');
        if (onSuccess) {
          onSuccess();
        }
      } else {
        throw new Error(error);
      }
    } else {
      yield put(AppActions.setLoading(false));
      if (onFail) {
        onFail(rs?.message || 'Có lỗi vui lòng thử lại');
      }
    }
  } catch (error) {
    console.error('Registration error:', error);
    yield put(AppActions.setLoading(false));
    if (onFail) {
      onFail(error?.response?.data?.message || 'Có lỗi vui lòng thử lại');
    }
  }
}

function* fetchBioLoginData(token, deviceToken, username) {
  try {
    setConfigAxios(token);
    const response = yield call(SysFetch.post, 'bio/login');
    const {token: newToken, refresh_token, view, user} = response;

    setConfigAxios(newToken);
    yield put(AccountActions.setProfile(user));
    yield put(AccountActions.setView(view));
    yield call(StorageService.setAccessToken, newToken);
    yield call(StorageService.setRefreshToken, refresh_token);
    yield call(StorageService.setDeviceToken, deviceToken || '');

    const [userNameSS, bioCheck] = yield all([
      call(StorageService.getUserName),
      call(StorageService.getBio),
    ]);
    const _username = username?.toLowerCase();

    yield call(StorageService.setUserName, username);

    yield call(
      StorageService.setBioEnable,
      bioCheck && _username === userNameSS?.toLowerCase(),
    );

    return {success: true};
  } catch (error) {
    console.error('Failed to fetch bio login data:', error);
    return {success: false, error: 'Failed to complete bio flow'};
  }
}
