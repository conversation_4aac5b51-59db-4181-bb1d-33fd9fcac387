import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import THEME from '../../components/theme/theme';
import {useNavigation} from '@react-navigation/native';
import HomeIcon from './icons/HomeIcon';
const HomeButton = () => {
  const {navigate} = useNavigation();

  const onPressHome = () => {
    navigate('HomeScreen');
  };

  return (
    <TouchableOpacity
      style={styles.container}
      activeOpacity={0.8}
      onPress={onPressHome}>
      <View style={styles.content}>
        <HomeIcon />
      </View>
    </TouchableOpacity>
  );
};

export default HomeButton;

const styles = StyleSheet.create({
  container: {
    width: 90,
    height: 90,
    backgroundColor: '#F1F1F1',
    borderRadius: 50,
    top: -45,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    width: 70,
    height: 70,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: THEME.Color.primary,
  },
});
