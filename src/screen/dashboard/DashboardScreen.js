import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import React from 'react';
import AccountScreen from '../account/AccountScreen';
import HomeScreen from '../home/<USER>';
import HomeMenu from '../home/<USER>/Home.Menu';
import MessageSupportScreen from '../message-support/MessageSupportScreen';
import ReportScreen from '../report/ReportScreen';
import ReportOwnerScreen from '../report/ReportOwnerScreen';
import BranchManagerMessageScreen from '../branch-manager-message/BranchManagerMessageScreen';
const Tab = createBottomTabNavigator();
const DashboardScreen = () => {
  return (
    <Tab.Navigator
      tabBar={props => <HomeMenu {...props} />}
      initialRouteName="HomeScreen"
      screenOptions={{
        animation: 'flip',
        headerShown: false,
      }}>
      <Tab.Screen name="ReportScreen" component={ReportScreen} />
      <Tab.Screen name="ReportOwnerScreen" component={ReportOwnerScreen} />
      <Tab.Screen name="HomeScreen" component={HomeScreen} />
      <Tab.Screen
        name="MessageSupportScreen"
        component={MessageSupportScreen}
      />
      <Tab.Screen name="AccountScreen" component={AccountScreen} />
      <Tab.Screen
        name="BranchManagerMessageScreen"
        component={BranchManagerMessageScreen}
      />
    </Tab.Navigator>
  );
};

export default DashboardScreen;
