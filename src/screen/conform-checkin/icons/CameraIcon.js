import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const CameraIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={38}
    height={38}
    fill="none"
    {...props}>
    <Path
      stroke="#000"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="M36.416 30.083a3.167 3.167 0 0 1-3.166 3.167H4.75a3.167 3.167 0 0 1-3.167-3.167V12.667A3.167 3.167 0 0 1 4.75 9.5h6.333l3.167-4.75h9.5l3.166 4.75h6.334a3.166 3.166 0 0 1 3.166 3.167v17.416Z"
    />
    <Path
      stroke="#000"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="M19 26.917a6.333 6.333 0 1 0 0-12.667 6.333 6.333 0 0 0 0 12.667Z"
    />
  </Svg>
);
export default CameraIcon;
