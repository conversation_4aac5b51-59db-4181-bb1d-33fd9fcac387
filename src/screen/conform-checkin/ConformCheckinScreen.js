import React, {useCallback, useEffect, useState} from 'react';
import Sys from '../../components/Sys';
import {
  Alert,
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import SuccessIcon from './icons/SuccessIcon';
import {useNavigation} from '@react-navigation/native';
import RightIcon from './icons/RightIcon';
import {useDispatch, useSelector} from 'react-redux';
import Modal from 'react-native-modal';
import CameraIcon from './icons/CameraIcon';
import CloseIcon from './icons/CloseIcon';
import FailIcon from './icons/FailIcon';
import {updateChecklists} from '../device-list/services/inventory.slice';

const ConformCheckinScreen = () => {
  const dispatch = useDispatch();

  const {checklists} = useSelector(state => state.inventory);
  const {navigate, ...navigation} = useNavigation();
  const [renderKey, setRenderKey] = useState(0);
  const [currentItem, setCurrentItem] = useState(null);
  const [isShowUpdate, setIsShowUpdate] = useState(false);

  const handleNext = useCallback(() => {
    navigate('CheckinResultScreen');
  }, [navigate]);

  const renderItem = useCallback(
    ({item, index}) => {
      return (
        <OneCheckList
          item={item}
          handleStatusChange={() => handleStatusChange(item)}
        />
      );
    },
    [dispatch],
  );

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      setRenderKey(prevKey => prevKey + 1);
    });
    return unsubscribe;
  }, [navigation]);

  const handleStatusChange = item => {
    console.log('handleStatusChange', item);
    if (item?.status) {
      setIsShowUpdate(true);
      setCurrentItem(item);
    } else {
      Alert.alert('Thông báo', 'Bạn có muốn cập nhật lại trạng thái?', [
        {
          text: 'Đóng',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {
          text: 'Xác nhận',
          onPress: () => {
            dispatch(
              updateChecklists({
                ...item,
                status: true,
                photo: undefined,
                note: '',
              }),
            );
            setCurrentItem(null);
          },
        },
      ]);
    }
  };

  const openCamera = useCallback(() => {
    setIsShowUpdate(false);
    if (currentItem) {
      navigate('CameraScreen', {
        title: 'Xem lại ảnh',
        description: 'Cần chụp ảnh sao cho có thể nhìn rõ',
        onCompleteCamera: _photo => {
          setCurrentItem(prev => ({...prev, photo: _photo}));
          navigate('ConformCheckinScreen');
          setIsShowUpdate(true);
        },
      });
    }
  }, [navigate, currentItem]);

  const onSave = useCallback(() => {
    if (currentItem) {
      console.log('onSave', currentItem);
      setIsShowUpdate(false);
      const data = {
        ...currentItem,
        status: !(currentItem?.note || currentItem?.photo),
      };
      dispatch(updateChecklists(data));
      setCurrentItem(null);
    }
  }, [currentItem]);

  const isFormValid = (state = false) => {
    return Boolean(currentItem?.note);
  };

  return (
    <Sys.Container hasFooter hasHeader>
      <Sys.Header title={'Quay lại'} />
      <View style={styles.container}>
        <View style={styles.innerContainer}>
          <Sys.Text style={styles.headerText}>
            Điểm bán sẵn sàng để bán hàng?
          </Sys.Text>
          <FlatList
            key={renderKey}
            data={checklists}
            renderItem={renderItem}
            keyExtractor={(item, index) => `${index}`}
          />
        </View>
        <Modal
          key={renderKey}
          animationIn={'fadeInUp'}
          animationOut={'fadeOutDown'}
          style={styles.modal}
          avoidKeyboard={true}
          isVisible={isShowUpdate}
          onSwipeComplete={() => setIsShowUpdate(false)}
          swipeDirection="down">
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Sys.Text style={styles.modalHeaderText}>Ghi chú vấn đề</Sys.Text>
              <TouchableOpacity onPress={() => setIsShowUpdate(false)}>
                <CloseIcon />
              </TouchableOpacity>
            </View>
            <View style={styles.divider} />
            <View style={styles.modalBody}>
              <Sys.Input
                onChangeText={e => setCurrentItem(prev => ({...prev, note: e}))}
                value={currentItem?.note}
                style={styles.textInput}
                multiline
                numberOfLines={3}
                placeholder="Nhập nội dung cần ghi chú"
              />
            </View>
            <View style={styles.modalFooter}>
              <TouchableOpacity onPress={openCamera}>
                {currentItem?.photo ? (
                  <Image
                    style={styles.image}
                    source={{uri: currentItem?.photo?.uri}}
                  />
                ) : (
                  <CameraIcon />
                )}
              </TouchableOpacity>
              <TouchableOpacity
                onPress={onSave}
                disabled={!isFormValid()}
                style={[
                  styles.buttonDisabled,
                  isFormValid() && styles.updateButton,
                ]}>
                <Sys.Text style={styles.updateButtonText}>Cập nhật</Sys.Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </View>
      <View style={styles.footer}>
        <TouchableOpacity onPress={handleNext} style={styles.nextButton}>
          <Sys.Text style={styles.nextButtonText}>Tiếp theo</Sys.Text>
          <View style={styles.nextButtonIcon}>
            <RightIcon color={'white'} />
          </View>
        </TouchableOpacity>
      </View>
    </Sys.Container>
  );
};

const OneCheckList = ({item, handleStatusChange}) => {
  return (
    <>
      <View style={styles.checklistItem}>
        <View>
          <Sys.Text style={styles.itemName}>{item.name}</Sys.Text>
          <Sys.Text style={styles.itemStatus}>
            Trạng thái:{' '}
            {item?.status ? (
              <Sys.Text style={styles.statusSuccess}>Đạt</Sys.Text>
            ) : (
              <Sys.Text style={styles.statusFail}>Không đạt</Sys.Text>
            )}
          </Sys.Text>
        </View>
        <TouchableOpacity
          onPress={handleStatusChange}
          style={styles.statusIcon}>
          {item?.status ? (
            <View style={styles.successIcon}>
              <SuccessIcon />
            </View>
          ) : (
            <View style={styles.failIcon}>
              <FailIcon />
            </View>
          )}
        </TouchableOpacity>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#001451',
    paddingHorizontal: 10,
    paddingTop: 20,
  },
  innerContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 30,
  },
  footer: {
    backgroundColor: '#001451',
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButton: {
    padding: 10,
    backgroundColor: '#0062FF',
    borderRadius: 50,
    paddingHorizontal: 40,
    flexDirection: 'row',
    alignItems: 'center',
  },
  nextButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  nextButtonIcon: {
    position: 'absolute',
    right: 15,
  },
  modal: {
    margin: 0,
    padding: 0,
  },
  modalContent: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    borderRadius: 20,
    overflow: 'hidden',
  },
  modalHeader: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalHeaderText: {
    fontWeight: '600',
  },
  divider: {
    height: 1,
    backgroundColor: '#F1F1F5',
  },
  modalBody: {
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderTopColor: '#F1F1F5',
    borderBottomColor: '#F1F1F5',
    padding: 20,
  },
  textInput: {
    minHeight: 100,
    borderWidth: 0,
  },
  modalFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  image: {
    height: 50,
    width: 50,
    borderRadius: 10,
  },
  updateButton: {
    backgroundColor: '#0062FF',
    borderRadius: 10,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  updateButtonText: {
    color: 'white',
  },
  checklistItem: {
    paddingVertical: 10,
    borderBottomColor: '#F1F1F5',
    borderBottomWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemName: {
    lineHeight: 20,
    fontWeight: '500',
  },
  itemStatus: {
    lineHeight: 20,
  },
  statusSuccess: {
    color: '#90BB3B',
    fontWeight: '600',
  },
  statusFail: {
    color: '#F52F2F',
    fontWeight: '600',
  },
  statusIcon: {
    position: 'absolute',
    right: 10,
  },
  successIcon: {
    width: 30,
    height: 30,
    borderRadius: 30,
    backgroundColor: '#90BB3B',
  },
  failIcon: {
    width: 30,
    height: 30,
    borderRadius: 30,
    backgroundColor: '#F52F2F',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonDisabled: {
    borderRadius: 10,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    backgroundColor: '#dad8d8',
  },
});

export default ConformCheckinScreen;
