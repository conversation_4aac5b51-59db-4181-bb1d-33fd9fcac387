import React, {useEffect} from 'react';
import {FlatList, Image, View} from 'react-native';
import Sys from '../../components/Sys';
import {useDispatch, useSelector} from 'react-redux';
import {useIsFocused} from '@react-navigation/native';
import {SupportActions} from '../message-support/services/support.slice';
import moment from 'moment';

function CommentComponent({id}) {
  const dispatch = useDispatch();
  const {comments} = useSelector(state => state.support);

  const isFocused = useIsFocused();

  useEffect(() => {
    if (isFocused) {
      dispatch(SupportActions.getComments({id}));
    }
  }, [isFocused, id]);

  return (
    <View
      style={{
        marginTop: 15,
      }}>
      <FlatList
        data={comments}
        keyExtractor={(item, index) => `${index}`}
        renderItem={({item, index}) => {
          return (
            <View
              style={{
                backgroundColor: '#0062FF05',
                marginTop: 10,
                borderRadius: 10,
                padding: 20,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    paddingBottom: 15,
                  }}>
                  <Image
                    style={{
                      width: 36,
                      height: 36,
                      borderRadius: 50,
                    }}
                    source={{
                      uri: item?.user?.avatar,
                    }}
                  />
                </View>
                <View
                  style={{
                    flex: 1,
                    marginLeft: 10,
                  }}>
                  <Sys.Text
                    style={{
                      fontWeight: '600',
                      color: '#171725',
                      marginBottom: 2,
                    }}>
                    {item?.user?.name}
                  </Sys.Text>
                  <Sys.Text
                    style={{
                      color: '#92929D',
                      fontSize: 12,
                    }}>
                    {moment(item?.created_at).format('DD-MM-YYYY HH:mm:ss')}
                  </Sys.Text>
                </View>
              </View>
              <View>
                <Sys.Text
                  style={{
                    color: '#44444f',
                    fontSize: 14,
                  }}>
                  {item?.comment}
                </Sys.Text>
              </View>
            </View>
          );
        }}
      />
    </View>
  );
}

export default CommentComponent;
