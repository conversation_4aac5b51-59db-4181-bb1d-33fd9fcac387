import React from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';

const {width} = Dimensions.get('window');

const MessageDetailSkeleton = () => {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.title} />
        <View style={styles.status} />
      </View>
      <View style={styles.body}>
        <View style={styles.date} />
        <View style={styles.bigTitle} />
        <View style={styles.textLine} />
        <View style={styles.textLine} />
        <View style={styles.textLine} width={width / 2} />
        <View style={styles.image} />
        <View style={styles.textLine} />
        <View style={styles.textLine} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 10,
    backgroundColor: '#FFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    width: '70%',
    height: 20,
    backgroundColor: '#eee',
    borderRadius: 4,
  },
  status: {
    width: '20%',
    height: 20,
    backgroundColor: '#eee',
    borderRadius: 50,
  },
  body: {
    flex: 1,
  },
  date: {
    width: '30%',
    height: 12,
    backgroundColor: '#eee',
    marginBottom: 10,
    borderRadius: 4,
  },
  bigTitle: {
    width: '60%',
    height: 24,
    backgroundColor: '#eee',
    marginBottom: 10,
    borderRadius: 4,
  },
  textLine: {
    height: 14,
    backgroundColor: '#eee',
    marginBottom: 5,
    borderRadius: 4,
    width: '100%',
  },
  image: {
    height: 85,
    width: 110,
    backgroundColor: '#eee',
    borderRadius: 15,
    marginVertical: 10,
  },
});

export default MessageDetailSkeleton;
