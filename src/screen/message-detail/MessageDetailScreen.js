import {useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useMemo, useState} from 'react';
import {
  Dimensions,
  Image,
  Linking,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import {Dropdown} from 'react-native-element-dropdown';
import RenderHTML from 'react-native-render-html';
import Toast from 'react-native-toast-message';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';

import {SupportActions} from '../message-support/services/support.slice';
import CommentComponent from './CommentComponent';
import DownIcon from './icons/DownIcon';
import FileWatchIcon from './icons/FileWatchIcon';

import SendMessageComponent from './SendMessageComponent';
import MessageDetailSkeleton from './MessageDetailSkeleton';
import SysFetch from '../../services/fetch';
import {AccountSelectors} from '../account/services/account.slice';

const {width = 0, height = 0} = Dimensions.get('window');

const MessageDetailScreen = () => {
  const route = useRoute();
  const view = useSelector(AccountSelectors.view);
  const id = route?.params?.id;
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const navigation = useNavigation();

  const [data, setData] = useState([]);
  const [statusList, setStatusList] = useState([]);
  const [statusName, setStatusName] = useState('');
  const [isLoading, setLoading] = useState(true);

  useEffect(() => {
    if (isFocused) {
      onLoadData();
    }
  }, [isFocused]);

  useEffect(() => {
    if (!id) {
      navigation.goBack();
    }
    if (isFocused) {
      onLoadData();
    }
  }, [isFocused, id]);

  const onLoadData = async () => {
    try {
      const [detail, status] = await Promise.all([
        SysFetch.get(`support/tickets/${id}`),
        SysFetch.get('support/tickets/statuses'),
      ]);
      dispatch(SupportActions.getComments({id}));

      const rs = await SysFetch.get(`support/tickets/${id}`);
      setData(detail?.data);
      if (detail?.data?.statusName) {
        setStatusName(rs?.data?.statusName);
      }
      setStatusList(
        Object.keys(status).map(x => {
          return {
            value: x,
            label: status[`${x}`],
          };
        }),
      );
      setLoading(false);
    } catch (e) {
      console.log(e);
      setLoading(false);
    }
  };

  const onUpdateStatus = status => {
    dispatch(
      SupportActions.updateStatus({
        id: route.params.id,
        status,
        onSuccess: rs => {
          Toast.show({
            type: 'success',
            text1: 'Thông báo',
            text2: 'Cập nhật trạng thái thành công',
          });
          onLoadData();
        },
      }),
    );
  };

  const renderStatus = useMemo(() => {
    return (
      <Dropdown
        disable={data?.status === 4 || data?.status === 3}
        placeholderStyle={{color: 'white', textAlign: 'center', fontSize: 12}}
        selectedTextStyle={{
          color: 'white',
          textAlign: 'center',
          fontSize: 12,
        }}
        itemContainerStyle={{
          padding: 0,
        }}
        itemTextStyle={{
          fontSize: 12,
          color: '#001451',
        }}
        style={{
          backgroundColor: '#0062FF',
          borderRadius: 50,
          paddingVertical: 10,
          paddingLeft: 20,
          paddingRight: 10,
          height: 40,
          width: 140,
        }}
        data={statusList.filter(x => x?.value != data?.status)}
        labelField="label"
        valueField="value"
        search={false}
        value={statusName}
        placeholder={statusName}
        onChange={item => {
          setStatusName(item.label);
          onUpdateStatus(item.value);
        }}
        renderRightIcon={() => (
          <View>
            <DownIcon />
          </View>
        )}
      />
    );
  }, [data, statusList]);

  const renderComments = useMemo(() => {
    return <CommentComponent id={id} />;
  }, [id]);

  if (!data) {
    return <MessageDetailSkeleton />;
  }

  return (
    <Sys.Container
      backgroundColor="white"
      hasHeader
      hasFooter
      footerColor="#E7E7E7">
      <Sys.Header title="TIN NHẮN PHẢN ÁNH CHI TIẾT" />
      {isLoading ? (
        <MessageDetailSkeleton />
      ) : (
        <>
          <View
            style={{
              padding: 10,
              backgroundColor: '#E7E7E7',
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <View
              style={{
                flex: 1,
              }}>
              <Sys.Text
                style={{
                  textTransform: 'uppercase',
                  color: '#0062FF',
                  lineHeight: 26,
                  fontWeight: '600',
                }}>
                {data?.pos?.address}/{data?.pos?.posCode}
              </Sys.Text>
            </View>
            {view === 'branch.manager' || view === 'pos.owner' ? (
              <></>
            ) : (
              <View style={{flex: 0}}>{renderStatus}</View>
            )}
          </View>
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{
              flex: 1,
            }}>
            <View
              style={{
                backgroundColor: 'white',
                padding: 10,
              }}>
              <Sys.Text
                style={{
                  color: '#696974',
                  lineHeight: 22,
                  fontSize: 12,
                }}>
                {moment(data?.created_at).format('DD/MM/YYYY hh:mm A')}
              </Sys.Text>
              <Sys.Text
                style={{
                  marginVertical: 5,
                  fontWeight: '700',
                  lineHeight: 28,
                  fontSize: 18,
                }}>
                {data?.category}
              </Sys.Text>
              <RenderHTML
                originWhitelist={['*']}
                contentWidth={width}
                tagsStyles={{
                  body: {
                    color: '#44444f',
                  },
                }}
                source={{html: data?.body || ''}}
              />
              {data?.photos?.length && (
                <View
                  style={{
                    backgroundColor: '#0062FF05',
                    paddingHorizontal: 10,
                    paddingVertical: 15,
                    borderRadius: 15,
                    marginTop: 20,
                  }}>
                  <Sys.Text
                    style={{
                      marginVertical: 5,
                      fontWeight: '700',
                      lineHeight: 28,
                      fontSize: 18,
                    }}>
                    Hình ảnh
                  </Sys.Text>
                  <View
                    style={{
                      flexDirection: 'row',
                    }}>
                    {(data?.photos || []).map((oneImage, index) => {
                      return (
                        <Image
                          key={`${index}`}
                          style={{
                            width: 110,
                            height: 85,
                            borderRadius: 15,
                            marginRight: 10,
                          }}
                          source={{
                            uri: oneImage,
                          }}
                        />
                      );
                    })}
                  </View>
                </View>
              )}
              {data?.files?.length && (
                <View
                  style={{
                    paddingHorizontal: 10,
                    paddingVertical: 15,
                  }}>
                  <Sys.Text
                    style={{
                      marginVertical: 5,
                      fontWeight: '700',
                      lineHeight: 28,
                      fontSize: 18,
                    }}>
                    File đính kèm
                  </Sys.Text>
                  <View
                    style={{
                      flexDirection: 'row',
                    }}>
                    {(data?.files || []).map((oneFile, index) => {
                      return oneFile ? (
                        <TouchableOpacity
                          onPress={() => Linking.openURL(oneFile)}
                          style={{
                            backgroundColor: '#C4DBFF15',
                            paddingHorizontal: 10,
                            paddingVertical: 15,
                            borderRadius: 15,
                            marginTop: 20,
                            marginRight: 20,
                            width: 140,
                          }}>
                          <View
                            style={{
                              backgroundColor: '#50B5FF10',
                              borderRadius: 15,
                              width: 48,
                              height: 48,
                              justifyContent: 'center',
                              alignItems: 'center',
                              marginBottom: 15,
                            }}>
                            <FileWatchIcon />
                          </View>
                          <Sys.Text
                            style={{
                              fontSize: 12,
                              lineHeight: 16,
                              color: '#171725',
                            }}>
                            {oneFile.split('/')[oneFile.split('/').length - 1]}
                          </Sys.Text>
                        </TouchableOpacity>
                      ) : (
                        <></>
                      );
                    })}
                  </View>
                </View>
              )}
              {renderComments}
            </View>
          </ScrollView>
          <SendMessageComponent id={id} onReload={onLoadData} />
        </>
      )}
    </Sys.Container>
  );
};

export default MessageDetailScreen;
