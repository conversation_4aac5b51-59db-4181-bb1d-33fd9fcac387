import {TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {pick} from 'react-native-document-picker';
// import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {AccountSelectors} from '../account/services/account.slice';
import FileIcon from './icons/FileIcon';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import SysFetch from '../../services/fetch';
import Toast from 'react-native-toast-message';
import {AppActions} from '../../app.slice';
import {SupportActions} from '../message-support/services/support.slice';

const SendMessageComponent = ({id, onReload}) => {
  const avatar = useSelector(AccountSelectors.avatar);
  const [comment, setComment] = useState('');
  const [file, setFile] = useState(undefined);
  const dispatch = useDispatch();

  const onSend = async () => {
    if (comment) {
      dispatch(AppActions.setLoading(true));

      try {
        let formData = new FormData();
        formData.append('comment', comment);
        if (file) {
          formData.append('files[]', file);
        }

        const rs = await SysFetch.post(
          `support/tickets/${id}/comment`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        );
        if (rs?.success) {
          onReload();
          setFile(undefined);
          setComment('');
          Toast.show({
            type: 'success',
            text1: 'Thông báo',
            text2: rs?.message,
          });
          dispatch(SupportActions.getComments({id}));
        }
      } catch (error) {
        if (error?.response?.data?.message) {
          Toast.show({
            type: 'error',
            text1: 'Thông báo',
            text2: error?.response?.data?.message,
          });
        }
      }
      dispatch(AppActions.setLoading(false));
    }
  };

  const onPick = async () => {
    if (file) {
      setFile(undefined);
    } else {
      try {
        const [result] = await pick({
          mode: 'open',
        });
        setFile(result);
      } catch (err) {
        // see error handling
      }
    }
  };

  return (
    <View
      style={{
        backgroundColor: '#E7E7E7',
        padding: 10,
      }}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <TouchableOpacity onPress={onPick} style={{}}>
          <FileIcon />
          {file && (
            <View
              style={{
                position: 'absolute',
                backgroundColor: 'red',
                borderRadius: 50,
                width: 14,
                height: 14,
                justifyContent: 'center',
                alignItems: 'center',
                bottom: 0,
                right: -2,
              }}>
              <Sys.Text
                style={{
                  fontSize: 10,
                  color: 'white',
                  fontWeight: '600',
                }}>
                1
              </Sys.Text>
            </View>
          )}
        </TouchableOpacity>
        <View
          style={{
            flex: 1,
            marginLeft: 10,
          }}>
          <Sys.Input
            returnKeyType="done"
            multiline
            numberOfLines={5}
            value={comment}
            onChangeText={e => {
              setComment(e);
            }}
            placeholder="Trả lời"
            style={{
              maxHeight: 50,
              borderRadius: 15,
              backgroundColor: '#FAFAFB',
              borderWidth: 1,
              borderColor: '#f1f1f5',
              paddingHorizontal: 10,
              paddingTop: 8,
              minHeight: 36,
            }}
          />
        </View>
        <View>
          <TouchableOpacity
            onPress={onSend}
            style={{
              width: 36,
              marginLeft: 10,
              height: 36,
              borderRadius: 10,
              backgroundColor: '#0062FF',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Sys.Text
              style={{
                color: 'white',
              }}>
              Gửi
            </Sys.Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default SendMessageComponent;
