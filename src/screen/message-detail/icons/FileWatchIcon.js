import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const FileWatchIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}>
    <Path
      fill="#50B5FF"
      fillRule="evenodd"
      d="M18.065 7.4 16.7 5.762V7.4h1.365ZM21.2 8.726V18.3a3.7 3.7 0 0 1-3.7 3.7H6.7A3.7 3.7 0 0 1 3 18.3V5.7A3.7 3.7 0 0 1 6.7 2h8.578a1.9 1.9 0 0 1 1.46.684l4.022 4.825a1.9 1.9 0 0 1 .44 1.217Zm-2 .674h-2.6a1.9 1.9 0 0 1-1.9-1.9V4h-8A1.7 1.7 0 0 0 5 5.7v12.6A1.7 1.7 0 0 0 6.7 20h10.8a1.7 1.7 0 0 0 1.7-1.7V9.4ZM7.103 15.6h9.994c.167 0 .303.12.303.267v1.466c0 .148-.136.267-.303.267H7.103c-.167 0-.303-.12-.303-.267v-1.466c0-.148.136-.267.303-.267Z"
      clipRule="evenodd"
    />
  </Svg>
);
export default FileWatchIcon;
