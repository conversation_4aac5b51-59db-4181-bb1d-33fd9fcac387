import {Image, View} from 'react-native';
import React from 'react';
import Sys from '../../components/Sys';
import LocationIcon from './icons/LocationIcon';
import WebIcon from './icons/WebIcon';
import MailIcon from './icons/MailIcon';
import PhoneIcon from './icons/PhoneIcon';
import {useSelector} from 'react-redux';
import {AppSelectors} from '../../app.slice';

const AppContactScreen = () => {
  const {contact} = useSelector(AppSelectors.settings);

  return (
    <Sys.Container hasHeader>
      <Sys.Header title={'liên hệ'} />
      <View
        style={{
          marginVertical: 10,
          marginHorizontal: 20,
        }}>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Image source={require('./icons/logo.png')} />
        </View>
        <View
          style={{
            marginVertical: 20,
          }}>
          <Sys.Text
            style={{
              fontSize: 18,
              lineHeight: 26,
              textAlign: 'center',
            }}>
            <PERSON><PERSON><PERSON> phản ánh hoặc góp ý
          </Sys.Text>
          <Sys.Text
            style={{
              fontSize: 18,
              lineHeight: 26,
              textAlign: 'center',
            }}>
            vui lòng liên hệ qua thông tin sau
          </Sys.Text>
        </View>
        <View
          style={{
            borderRadius: 8,
            borderWidth: 1,
            borderColor: '#d1d1d1',
            padding: 15,
            backgroundColor: 'rgba(209,209,209,0.21)',
          }}>
          <Sys.Text
            style={{
              fontSize: 16,
              fontWeight: '600',
            }}>
            Trụ sở chính
          </Sys.Text>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 10,
              alignItems: 'center',
            }}>
            <View
              style={{
                marginRight: 10,
              }}>
              <LocationIcon />
            </View>
            <View
              style={{
                flex: 1,
              }}>
              <Sys.Text>{contact?.address}</Sys.Text>
            </View>
          </View>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 10,
              alignItems: 'center',
            }}>
            <View
              style={{
                marginRight: 10,
              }}>
              <PhoneIcon />
            </View>
            <View
              style={{
                flex: 1,
              }}>
              <Sys.Text>
                Điện thoại:{' '}
                <Sys.Text
                  style={{
                    textDecorationLine: 'underline',
                  }}>
                  {contact?.phone}
                </Sys.Text>
              </Sys.Text>
            </View>
          </View>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 10,
              alignItems: 'center',
            }}>
            <View
              style={{
                marginRight: 10,
              }}>
              <MailIcon />
            </View>
            <View
              style={{
                flex: 1,
              }}>
              <Sys.Text>
                Email:{' '}
                <Sys.Text
                  style={{
                    textDecorationLine: 'underline',
                    color: '#001451',
                  }}>
                  {contact?.email}
                </Sys.Text>
              </Sys.Text>
            </View>
          </View>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 10,
              alignItems: 'center',
            }}>
            <View
              style={{
                marginRight: 10,
              }}>
              <WebIcon />
            </View>
            <View
              style={{
                flex: 1,
              }}>
              <Sys.Text>
                Website:{' '}
                <Sys.Text
                  style={{
                    textDecorationLine: 'underline',
                    color: '#001451',
                  }}>
                  https://vietlott.vn
                </Sys.Text>
              </Sys.Text>
            </View>
          </View>
        </View>
      </View>
    </Sys.Container>
  );
};

export default AppContactScreen;
