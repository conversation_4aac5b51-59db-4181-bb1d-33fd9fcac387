import {View, Text, Image} from 'react-native';
import React from 'react';
import Sys from '../../components/Sys';

const CheckoutImage = ({historyDetail}) => {
  const photos = historyDetail?.checkin_details?.data?.photos;
  const config = historyDetail?.checkin_details?.data?.config.filter(
    x => x.type === 'file',
  );

  return (
    <View>
      {(config || []).map(x => {
        return (
          <View
            style={{
              marginTop: 15,
              backgroundColor: 'white',
              marginHorizontal: 10,
              paddingHorizontal: 15,
              paddingVertical: 15,
              borderRadius: 20,
            }}>
            <Sys.Text
              style={{
                color: '#001451',
                lineHeight: 24,
                fontWeight: '600',
              }}>
              {x.description}
            </Sys.Text>
            <View
              style={{
                flexDirection: 'row',
              }}>
              {photos[`${x.key}`].map((photo, index) => {
                return (
                  <View
                    key={`${index}-photos`}
                    style={{
                      marginTop: 10,
                    }}>
                    <Image
                      style={{
                        height: 90,
                        width: 90,
                        marginRight: 15,
                      }}
                      source={{uri: photo}}
                    />
                  </View>
                );
              })}
            </View>
          </View>
        );
      })}
    </View>
  );
};

export default CheckoutImage;
