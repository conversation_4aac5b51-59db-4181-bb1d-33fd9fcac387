import React from 'react';
import {Dimensions, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import Sys from '../../components/Sys';
import {isAndroid} from '../../services/util';

const {width} = Dimensions.get('window');

const ConfirmCheckoutModal = ({
  remainingMinutes,
  setShowModal,
  handleModalConfirm,
  minMinutes,
}) => {
  const formatTime = (remainingMinutes, minMinutes) => {
    const minutesRemaining = minMinutes - remainingMinutes.asMinutes(); // Tính thời gian còn lại
    const positiveMinutes = Math.max(0, minutesRemaining); // Đảm bảo kết quả là không âm
    const totalSeconds = Math.floor(positiveMinutes * 60); // Chuyển đổi phút thành giây
    const formattedMinutes = Math.floor(totalSeconds / 60)
      .toString()
      .padStart(2, '0');
    const seconds = (totalSeconds % 60).toString().padStart(2, '0');
    return `${formattedMinutes}:${seconds}`; // Trả về chuỗi định dạng mm:ss
  };

  return (
    <Modal
      isVisible={true}
      animationIn="fadeInUp"
      animationOut="fadeOutDown"
      style={styles.modalStyle}
      onSwipeComplete={() => setShowModal(false)}
      swipeDirection={['down']}
      backdropTransitionOutTiming={0}>
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Sys.Text
            style={{color: 'white', fontWeight: isAndroid ? 'bold' : 600}}>
            XÁC NHẬN RỜI ĐBH
          </Sys.Text>
          <Sys.Text style={{color: 'white'}}>
            Thời gian tại DBH chưa đủ, thời gian còn lại
          </Sys.Text>
          <Sys.Text style={styles.timeRemaining}>
            {formatTime(remainingMinutes, minMinutes)}
          </Sys.Text>
        </View>
        <View style={styles.modalFooter}>
          <TouchableOpacity
            style={{
              backgroundColor: 'white',
              paddingVertical: 10,
              paddingHorizontal: 20,
              borderRadius: 10,
            }}
            onPress={() => setShowModal(false)}>
            <Sys.Text
              style={{
                color: '#1E75FF',
                fontWeight: isAndroid ? 'bold' : 600,
              }}>
              Ok, Tôi đợi
            </Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              backgroundColor: 'white',
              paddingVertical: 10,
              paddingHorizontal: 20,
              borderRadius: 10,
            }}
            onPress={handleModalConfirm}>
            <Sys.Text
              style={{color: '#D30B0D', fontWeight: isAndroid ? 'bold' : 600}}>
              Rời ĐBH ngay
            </Sys.Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    margin: 0,
  },
  modalContainer: {
    backgroundColor: '#D30B0D',
    padding: 20,
    borderRadius: 10,
    width: width - 40,
    alignItems: 'center',
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  timeRemaining: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
  },
});

export default ConfirmCheckoutModal;
