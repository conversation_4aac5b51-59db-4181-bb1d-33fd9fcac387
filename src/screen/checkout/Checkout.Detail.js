import React, {useCallback, useEffect, useState} from 'react';
import {FlatList, StyleSheet, View} from 'react-native';
import Sys from '../../components/Sys';
import moment from 'moment';
import {formatVND} from '../../services/util';
import 'moment-duration-format';

const CheckoutDetail = ({
  posDetail,
  m,
  historyDetail,
  allowCheckout,
  setRemainingMinutes,
}) => {
  const [data, setData] = useState([]);

  const calculateTime = useCallback(() => {
    const checkedInAt = moment(historyDetail?.checkin_details?.checked_in_at);
    const checkedOutAt = historyDetail?.checkout_details?.checked_in_at
      ? moment(historyDetail.checkout_details.checked_in_at)
      : moment();

    const currentOrCheckoutTime =
      historyDetail?.action_code === 1 && allowCheckout
        ? moment()
        : checkedOutAt;
    const duration = moment.duration(currentOrCheckoutTime.diff(checkedInAt));
    setRemainingMinutes(duration);
    const days = duration.days();
    const formatString = days > 0 ? 'd [ngày], hh:mm:ss' : 'hh:mm:ss';

    return duration.format(formatString, {trim: false});
  }, [historyDetail, allowCheckout]);

  useEffect(() => {
    const renderData = () => {
      let newData = [
        {
          field: 'Thời gian lưu tại điểm:',
          //   historyDetail?.action_code === 1
          //     ? 'Thời gian lưu tại điểm:'
          //     : 'Thời gian ghé thăm:',
          value: calculateTime(),
        },
        {
          field: 'Thời gian bắt đầu ghé thăm:',
          value: moment(historyDetail?.created_at).format('HH:mm DD/MM/YYYY'),
        },
        {
          field: 'Khoảng cách:',
          value: `${formatVND(Math.round(m))}m`,
        },
        {
          field: 'Hành động:',
          value: historyDetail?.action_label,
        },
      ];

      const warningLabels = [
        ...(historyDetail?.checkin_details?.warn_status_labels || []),
        ...(historyDetail?.checkout_details?.warn_status_labels || []),
      ];

      const info = historyDetail?.checkin_summary;

      if (info?.date) {
        newData.push({
          field: 'Ngày đi điểm theo kế hoạch:',
          value: moment(info.date, 'YYYY-MM-DD').format('DD/MM/YYYY'),
        });
      }
      newData.push({
        field: 'Được tính KPI:',
        value: info?.has_kpi ? 'Có' : 'Không',
      });

      if (warningLabels.length > 0) {
        newData.push({
          field: 'Cảnh báo:',
          value: warningLabels.join(', '),
        });
      }

      setData(newData);
    };

    renderData();

    let intervalId = null;
    if (historyDetail?.action_code === 1 && allowCheckout) {
      intervalId = setInterval(() => {
        setData(prevData => {
          const updatedData = [...prevData];
          updatedData[0] = {
            ...updatedData[0],
            value: calculateTime(),
          };
          return updatedData;
        });
      }, 1000); // Update every second if conditions are met
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      } // Cleanup the interval on component unmount
    };
  }, [posDetail, m, historyDetail, allowCheckout, calculateTime]);

  return (
    <View style={styles.container}>
      <Sys.Text style={styles.headerText}>
        Thông tin {historyDetail?.action_code === 1 ? 'ghé thăm' : 'đóng cửa'}
      </Sys.Text>
      <FlatList
        scrollEnabled={false}
        data={data}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item}) => (
          <View style={styles.itemRow}>
            <Sys.Text style={styles.fieldText}>{item.field}</Sys.Text>
            <Sys.Text style={styles.valueText}>{item.value}</Sys.Text>
          </View>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    marginHorizontal: 10,
    paddingHorizontal: 15,
    paddingVertical: 15,
    borderRadius: 20,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#171725',
    marginBottom: 15,
    marginTop: 15,
  },
  itemRow: {
    flexDirection: 'row',
    marginVertical: 5,
    justifyContent: 'space-between',
  },
  fieldText: {
    color: '#1E2022',
    lineHeight: 24,
    fontWeight: '500',
    flex: 1,
  },
  valueText: {
    textAlign: 'right',
    color: '#001451',
    lineHeight: 20,
    fontWeight: '600',
    flex: 1,
    flexWrap: 'wrap',
  },
});

export default CheckoutDetail;
