import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const MapIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={18}
    height={21}
    fill="none"
    {...props}>
    <Path
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="M8.963 5.407h5.06c1.638 0 2.977 1.3 2.977 2.89 0 1.589-1.34 2.89-2.977 2.89H3.977C2.34 11.186 1 12.486 1 14.075c0 1.59 1.34 2.89 2.977 2.89h4.911M4.87 1C3.605 1 2.637 2.011 2.637 3.167s2.233 4.19 2.233 4.19 2.232-2.962 2.232-4.19C7.102 1.94 6.135 1 4.87 1Zm8.41 12.643c-1.266 0-2.233 1.011-2.233 2.167S13.279 20 13.279 20s2.233-2.962 2.233-4.19c0-1.228-.968-2.167-2.233-2.167Z"
    />
  </Svg>
);
export default MapIcon;
