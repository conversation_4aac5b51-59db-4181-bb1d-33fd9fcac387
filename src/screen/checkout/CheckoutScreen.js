import React, {useCallback, useEffect, useState} from 'react';
import {RefreshControl, ScrollView, StyleSheet, View} from 'react-native';

import LinearGradient from 'react-native-linear-gradient';
import MapView, {Marker, PROVIDER_GOOGLE} from 'react-native-maps';
import Sys from '../../components/Sys';
import {useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import {
  getCurrentLocation,
  haversineDistance,
  stopWatchingLocation,
} from '../checkin-location/locationUtils';
import CheckoutConform from './Checkout.Conform';
import CheckoutDetail from './Checkout.Detail';
import CheckoutDeviceList from './Checkout.DeviceList';
import CheckoutFooter from './Checkout.Footer';
import CheckoutInformation from './Checkout.Infomation';
import CheckoutImage from './CheckoutImage';
import CheckoutScreenSkeleton from './CheckoutScreenSkeleton';
import SysFetch from '../../services/fetch';
import Toast from 'react-native-toast-message';
import {useDispatch, useSelector} from 'react-redux';
import {AppActions, AppSelectors} from '../../app.slice';

const CheckoutScreen = () => {
  const {navigate, ...navigation} = useNavigation();

  const {params} = useRoute();
  const {checkinId, posCode} = params;
  const [posData, setPosData] = useState(undefined);
  const [historyDetail, setHistoryDetail] = useState(undefined);
  const {location, extras} = useSelector(AppSelectors.current_location);

  const [m, setM] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [remainingMinutes, setRemainingMinutes] = useState(0);
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const historyResponse = await SysFetch.get(
        `pos/${posCode}/visit/${checkinId}`,
      );
      setHistoryDetail(historyResponse?.data);
      const posResponse = await SysFetch.get(`pos/${posCode}/show`);
      setPosData(posResponse);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Lỗi',
        text2: error?.response?.data?.message || 'Không thể tìm nạp dữ liệu',
      });
      console.error('Failed to fetch data:', error);
    }
    setLoading(false);
  }, [checkinId, posCode]);
  const isFocused = useIsFocused();

  const [highAccuracy, setHighAccuracy] = useState(true);
  const dispatch = useDispatch();

  const checkLocationAgain = useCallback(() => {
    getCurrentLocation({
      highAccuracy,
      setCallback: ({data}) => {
        if (data) {
          dispatch(AppActions.setLocation(data));
        }
      },
      watchPosition: Boolean(posData?.allowCheckout),
    });
  }, [highAccuracy]);

  useEffect(() => {
    if (isFocused) {
      fetchData();
      checkLocationAgain();
    }
    return () => stopWatchingLocation();
  }, [fetchData, isFocused]);

  useEffect(() => {
    if (posData?.data && posData?.allowCheckout) {
      const temp = haversineDistance(
        {
          lon: posData?.data?.lon,
          lat: posData?.data?.lat,
        },
        {lon: location?.longitude, lat: location?.latitude},
      );
      setM(temp);
    }
  }, [location, posData?.data]);

  useEffect(() => {
    if (historyDetail?.checkin_details?.distance) {
      setM(historyDetail.checkin_details.distance);
    }
  }, [historyDetail]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchData();
    checkLocationAgain();
    setRefreshing(false);
  }, [fetchData, checkLocationAgain]);

  const handleBack = () => {
    if (params?.resetNavigation || !navigation.canGoBack()) {
      navigation.reset({
        index: 0,
        routes: [{name: 'SaleInformationScreen', params}],
      });
    } else {
      navigation.goBack();
    }
  };

  return (
    <Sys.Container hasHeader>
      <Sys.Header
        title={`Thông tin ${
          historyDetail?.action_code === 1 ? 'Ghé thăm' : 'Đóng cửa'
        }`}
        onBack={handleBack}
        right={<View />}
      />
      {loading ? (
        <CheckoutScreenSkeleton />
      ) : (
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.container}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }>
          <LinearGradient
            colors={['#00000000', '#00000000', '#00000090']}
            style={styles.viewLinea}
          />
          <MapView
            scrollEnabled={false}
            provider={PROVIDER_GOOGLE}
            style={{width: '100%', height: 230}}
            region={{
              latitude: posData?.data?.lat || 0,
              longitude: posData?.data?.lon || 0,
              latitudeDelta: 0.03,
              longitudeDelta: 0.024,
            }}>
            <Marker
              coordinate={{
                latitude: posData?.data?.lat || 0,
                longitude: posData?.data?.lon || 0,
              }}
            />
          </MapView>
          <View style={styles.mainContainer}>
            {/*Khối avatar*/}
            <CheckoutInformation posDetail={posData?.data} />

            {/*Khối chi tiết*/}
            <CheckoutDetail
              posDetail={posData?.data}
              allowCheckout={posData?.allowCheckout}
              m={m}
              historyDetail={historyDetail}
              setRemainingMinutes={setRemainingMinutes}
            />
            <CheckoutImage historyDetail={historyDetail} />
            {historyDetail?.action_code === 1 && (
              <CheckoutDeviceList historyDetail={historyDetail} />
            )}
            {historyDetail?.action_code === 1 && (
              <CheckoutConform
                posDetail={posData?.data}
                historyDetail={historyDetail}
                checkinId={checkinId}
              />
            )}
          </View>
          <View style={{height: 100}} />
        </ScrollView>
      )}
      {posData?.allowCheckout && (
        <CheckoutFooter
          posDetail={posData?.data}
          historyDetail={historyDetail}
          remainingMinutes={remainingMinutes}
          location={location}
        />
      )}
    </Sys.Container>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    zIndex: 5,
  },
  container: {
    flex: 1,
  },
  viewLinea: {
    width: '100%',
    height: 230,
    position: 'absolute',
    zIndex: 2,
    top: 0,
  },
});

export default CheckoutScreen;
