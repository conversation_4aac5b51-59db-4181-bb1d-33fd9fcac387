import React from 'react';
import {Image, Linking, StyleSheet, TouchableOpacity, View} from 'react-native';
import Sys from '../../components/Sys';
import MapIcon from './icons/MapIcon';
import TelephoneIcon from './icons/TelephoneIcon';
import {isAndroid} from '../../services/util';

export const openGoogleMaps = (latitude, longitude) => {
  const url = `https://www.google.com/maps?q=${latitude},${longitude}&z=12`;
  Linking.openURL(url).catch(err => console.error('An error occurred', err));
};
const CheckoutInfomation = ({posDetail}) => {
  return (
    <View>
      <View style={styles.informationContainer}>
        <View style={styles.informationContainerView}>
          <View style={styles.avatarContainer}>
            <Image
              style={styles.avatar}
              source={{uri: posDetail?.owner?.avatar}}
            />
          </View>
          <View
            style={{
              flex: 1,
              height: 80,
            }}>
            <View style={{flex: 1, height: 40}}>
              <Sys.Text style={styles.address}>
                {posDetail?.address}
                {posDetail?.owner?.name ? ' -' : ''}
                <Sys.Text style={styles.phoneNumber}>
                  {posDetail?.owner?.name}
                </Sys.Text>
              </Sys.Text>
            </View>
            <View
              style={{
                height: 40,
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <TouchableOpacity
                onPress={() => {
                  openGoogleMaps(posDetail?.lat, posDetail?.lon);
                }}
                style={{
                  backgroundColor: '#001451',
                  height: 30,
                  justifyContent: 'center',
                  width: 110,
                  borderRadius: 8,
                  alignItems: 'center',
                  flexDirection: 'row',
                  marginRight: 10,
                }}>
                <MapIcon />
                <Sys.Text
                  style={{
                    marginLeft: 10,
                    color: 'white',
                    fontSize: 12,
                  }}>
                  Chỉ đường
                </Sys.Text>
              </TouchableOpacity>
              {posDetail?.owner?.phone ? (
                <TouchableOpacity
                  onPress={() => {
                    let url;
                    if (isAndroid) {
                      url = `tel:${posDetail?.owner?.phone}`;
                    } else {
                      url = `telprompt:${posDetail?.owner?.phone}`;
                    }

                    Linking.openURL(url).catch(err =>
                      console.error('Failed to open URL:', err),
                    );
                  }}
                  style={{
                    backgroundColor: '#001451',
                    height: 30,
                    justifyContent: 'center',
                    width: 110,
                    borderRadius: 50,
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}>
                  <TelephoneIcon />
                  <Sys.Text
                    style={{
                      marginLeft: 10,
                      color: 'white',
                      fontSize: 12,
                    }}>
                    Gọi điện
                  </Sys.Text>
                </TouchableOpacity>
              ) : (
                <></>
              )}
            </View>
            <View />
          </View>
        </View>
      </View>
      <View style={{height: 56}} />
    </View>
  );
};
const styles = StyleSheet.create({
  mainContainer: {
    zIndex: 5,
  },
  informationContainerView: {
    flexDirection: 'row',
    paddingHorizontal: 10,
  },
  informationContainer: {
    position: 'absolute',
    top: -40,
    width: '100%',
    zIndex: 3,
  },
  container: {
    flex: 1,
  },
  avatar: {
    width: 80,
    height: 80,
    borderWidth: 3,
    borderColor: 'white',
    borderRadius: 20,
    zIndex: 4,
  },
  viewLinea: {
    width: '100%',
    height: 230,
    position: 'absolute',
    zIndex: 2,
    top: 0,
  },
  avatarContainer: {
    flex: 0,
    marginRight: 10,
    backgroundColor: '#f1f1f1',
    borderRadius: 20,
  },
  address: {
    color: 'white',
    fontSize: 12,
    lineHeight: 16,
  },
  phoneNumber: {
    color: 'white',
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '700',
  },
});

export default CheckoutInfomation;
