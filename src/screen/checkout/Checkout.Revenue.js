import React, {useState} from 'react';
import {View, useWindowDimensions} from 'react-native';
import {
  VictoryAxi<PERSON>,
  VictoryBar,
  VictoryChart,
  VictoryTheme,
  VictoryTooltip,
} from 'victory-native';
import Sys from '../../components/Sys';

const CheckoutRevenue = () => {
  const {width} = useWindowDimensions();

  const labelList = [
    {
      name: 'H',
      value: 2,
    },
    {
      name: 'D',
      value: 3,
    },
    {
      name: 'M',
      value: 4,
    },
    {
      name: 'Q',
      value: 5,
    },
    {
      name: 'Y',
      value: 6,
    },
  ];

  const data = [
    {date: 1, money: 7000},
    {date: 2, money: 4500},
    {date: 3, money: 12250},
    {date: 4, money: 19000},
    {date: 5, money: 3250},
    {date: 6, money: 9000},
    {date: 7, money: 16500},
  ];

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'white',
        borderRadius: 20,
        marginHorizontal: 10,
        marginTop: 15,
      }}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
          padding: 15,
        }}>
        <View
          style={{
            borderRadius: 12,
            backgroundColor: '#001451',
            justifyContent: 'center',
            alignItems: 'center',
            height: 40,
            width: 110,
          }}>
          <Sys.Text
            style={{
              color: 'white',
              fontWeight: '700',
              letterSpacing: 1,
            }}>
            DOANH THU
          </Sys.Text>
        </View>
        {labelList.map(x => {
          return (
            <View
              style={{
                borderRadius: 12,
                backgroundColor: '#0062FF', //F5000D
                justifyContent: 'center',
                alignItems: 'center',
                height: 40,
                width: 40,
              }}>
              <Sys.Text
                style={{
                  color: 'white',
                  fontWeight: '700',
                  letterSpacing: 1,
                }}>
                {x.name}
              </Sys.Text>
            </View>
          );
        })}
      </View>
      <VictoryChart width={width} theme={VictoryTheme.material}>
        <VictoryAxis
          gridComponent={<></>}
          axisComponent={<></>}
          tickComponent={<></>}
        />
        <VictoryAxis
          dependentAxis
          gridComponent={<></>}
          axisComponent={<></>}
          tickComponent={<></>}
          tickLabelComponent={<></>}
        />
        <VictoryBar
          cornerRadius={{top: 10, bottom: 10}}
          style={{
            data: {
              fill: '#001451',
              borderRadius: 10,
              width: 30,
            },
            grid: {stroke: 'transparent', width: 0},
          }}
          animate={{
            duration: 500,
            onLoad: {duration: 500},
          }}
          labelComponent={
            <VictoryTooltip
              // Tuỳ chỉnh kiểu cho text trong tooltip
              style={{fontSize: 15, fill: 'black', color: 'black'}}
              // Tuỳ chỉnh kiểu cho khung tooltip
              flyoutStyle={{
                stroke: '#00000050',
                fill: 'white',
              }}
              // Tuỳ chỉnh padding cho tooltip
              flyoutPadding={{top: 10, bottom: 10, left: 15, right: 15}}
              cornerRadius={10} // Độ bo góc của tooltip
              pointerLength={5}
            />
          }
          labels={({datum}) => `Giá trị: ${datum.money}`}
          data={data}
          x="date"
          y="money"
        />
      </VictoryChart>
      <View
        style={{
          marginBottom: 20,
        }}>
        <Sys.Text
          style={{
            fontWeight: '600',
            lineHeight: 24,
          }}>
          Báo cáo doanh thu 7 ngày gần nhất
        </Sys.Text>
      </View>
    </View>
  );
};

export default CheckoutRevenue;
