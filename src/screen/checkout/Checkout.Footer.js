import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import Sys from '../../components/Sys';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {AccountSelectors} from '../account/services/account.slice';
import ConfirmCheckoutModal from './ConfirmCheckoutModal';
import SysFetch from '../../services/fetch';
import Toast from 'react-native-toast-message';
import {AppActions, AppSelectors} from '../../app.slice';

const CheckoutFooter = ({
  posDetail,
  remainingMinutes,
  historyDetail,
  location,
}) => {
  const navigation = useNavigation();
  const avatar = useSelector(AccountSelectors.avatar);
  const [showModal, setShowModal] = useState(false);
  const checkoutConfig = useSelector(AppSelectors.settings).checkout_config;
  const [minMinutes, setMinMinutes] = useState(15);

  useEffect(() => {
    const configItem = checkoutConfig.find(item => item.key === 'coordinates');
    if (configItem) {
      setMinMinutes(configItem.minimum_minutes);
    }
  }, [checkoutConfig]);

  const handleCheckoutSuccess = () => {
    Toast.show({
      type: 'success',
      text1: 'Thông báo Rời ĐBH',
      text2: 'Thành công',
    });
    dispatch(AppActions.setLoading(false));
    navigation.reset({
      index: 0,
      routes: [
        {
          name: 'SaleInformationScreen',
          params: {posCode: posDetail.posCode, resetNavigation: true},
        },
      ],
    });
  };

  const handleCheckoutFailure = error => {
    Toast.show({
      type: 'error',
      text1: 'Thông báo',
      text2: error || 'Có lỗi vui lòng thử lại',
    });
  };
  const dispatch = useDispatch();

  const requestCheckout = async () => {
    try {
      dispatch(AppActions.setLoading(true));
      const response = await SysFetch.post('plan/checkout', {
        plan_id: historyDetail?.checkin_details?.plan_id || null,
        checkin_id: historyDetail?.id,
        posCode: posDetail?.posCode,
        lon: location?.longitude,
        lat: location?.latitude,
      });

      if (response.success) {
        handleCheckoutSuccess();
      } else {
        dispatch(AppActions.setLoading(false));
        handleCheckoutFailure();
        console.error('requestCheckout', response);
      }
    } catch (error) {
      dispatch(AppActions.setLoading(false));
      handleCheckoutFailure(error.message || error?.response?.data?.error);
      console.error('requestCheckout', error);
    }
  };

  const onCheckout = () => {
    if (remainingMinutes.asMinutes() < minMinutes) {
      setShowModal(true);
    } else {
      requestCheckout();
    }
  };

  const handleModalConfirm = () => {
    setShowModal(false);
    requestCheckout();
  };

  return (
    <View style={styles.footerContainer}>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={onCheckout}
        style={styles.checkoutButton}>
        <Image style={styles.avatar} source={{uri: avatar}} />
        <View style={styles.buttonTextContainer}>
          <Sys.Text style={styles.buttonText}>Xác nhận rời ĐBH</Sys.Text>
        </View>
        <View style={styles.placeholder} />
      </TouchableOpacity>
      {showModal && (
        <ConfirmCheckoutModal
          remainingMinutes={remainingMinutes}
          setShowModal={setShowModal}
          handleModalConfirm={handleModalConfirm}
          minMinutes={minMinutes}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  footerContainer: {
    position: 'absolute',
    bottom: 20,
    width: '100%',
  },
  checkoutButton: {
    borderRadius: 50,
    backgroundColor: '#EF7721',
    padding: 7,
    flexDirection: 'row',
    alignItems: 'center',
    margin: 20,
  },
  avatar: {
    height: 40,
    width: 40,
    borderRadius: 50,
  },
  buttonTextContainer: {
    flex: 1,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    lineHeight: 26,
    textAlign: 'center',
    textTransform: 'uppercase',
  },
});

export default CheckoutFooter;
