import React, {useEffect, useState} from 'react';
import {FlatList, View} from 'react-native';
import Sys from '../../components/Sys';
import SuccessIcon from './icons/SuccessIcon';
import FailIcon from './icons/FailIcon';
import {useDispatch} from 'react-redux';
import {PosActions} from '../sale-location/services/pos.slice';
import Color from '../../components/theme/Color';

const CheckoutConform = ({posDetail, historyDetail}) => {
  const [checkList, setCheckList] = useState([]);
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(
      PosActions.getPosReadinessChecklists({
        onSuccess: rs => {
          setCheckList(rs?.data);
        },
      }),
    );
  }, []);

  return (
    <View
      style={{
        flex: 1,
        paddingHorizontal: 10,
        paddingTop: 20,
      }}>
      <View
        style={{
          backgroundColor: 'white',
          borderRadius: 20,
          padding: 20,
        }}>
        <Sys.Text
          style={{
            fontSize: 18,
            fontWeight: '600',
            marginBottom: 10,
          }}>
          <PERSON><PERSON><PERSON><PERSON> bán sẵn sàng để bán hàng?
        </Sys.Text>
        <FlatList
          data={checkList.map(x => {
            try {
              const temp = (
                historyDetail?.checkin_details?.data?.checklists || []
              ).filter(y => y.id == x.id);
              return {
                ...x,
                ...temp[0],
              };
            } catch (error) {
              return x;
            }
          })}
          renderItem={({item, index}) => {
            return (
              <View
                style={{
                  paddingVertical: 10,
                  borderBottomColor: '#F1F1F5',
                  borderBottomWidth: 1,
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <View>
                  <Sys.Text
                    style={{
                      lineHeight: 20,
                    }}>
                    {item.name}
                  </Sys.Text>
                  <Sys.Text
                    style={{
                      lineHeight: 20,
                    }}>
                    Trạng thái:{' '}
                    {item?.status ? (
                      <Sys.Text
                        style={{
                          color: '#90BB3B',
                          fontWeight: '600',
                        }}>
                        Đạt
                      </Sys.Text>
                    ) : (
                      <Sys.Text
                        style={{
                          color: Color.red,
                          fontWeight: '600',
                        }}>
                        Không đạt
                      </Sys.Text>
                    )}
                  </Sys.Text>
                </View>
                <View
                  style={{
                    position: 'absolute',
                    right: 10,
                  }}>
                  {item?.status ? (
                    <View
                      style={{
                        width: 30,
                        height: 30,
                        borderRadius: 30,
                        backgroundColor: '#90BB3B',
                      }}>
                      <SuccessIcon />
                    </View>
                  ) : (
                    <View
                      style={{
                        width: 30,
                        height: 30,
                        borderRadius: 30,
                        backgroundColor: Color.red,
                        justifyContent: 'center',
                        alignItems: 'center',
                        flexDirection: 'row',
                      }}>
                      <FailIcon />
                    </View>
                  )}
                </View>
              </View>
            );
          }}
          keyExtractor={(item, index) => `${index}`}
        />
      </View>
    </View>
  );
};

export default CheckoutConform;
