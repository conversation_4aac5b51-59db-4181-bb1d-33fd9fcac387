import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import Sys from '../../components/Sys';
import RightIcon from './icons/RightIcon';
import {setDataSectionMode} from '../device-list/services/inventory.slice';
import {useNavigation} from '@react-navigation/native';
import {useDispatch} from 'react-redux';

const CheckoutDeviceList = ({historyDetail}) => {
  const {navigate} = useNavigation();
  const dispatch = useDispatch();
  return (
    <TouchableOpacity
      onPress={() => {
        dispatch(
          setDataSectionMode({
            data: historyDetail?.checkin_details?.data?.items,
            mode: 3,
          }),
        );
        navigate('DeviceListScreen');
      }}
      style={{
        marginHorizontal: 10,
        marginTop: 15,
        borderRadius: 20,
        backgroundColor: 'white',
        paddingHorizontal: 15,
        paddingVertical: 20,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
      <View>
        <Sys.Text style={{color: '#0062FF', fontSize: 18, fontWeight: '600'}}>
          <PERSON>h sách trang thiết bị
        </Sys.Text>
      </View>
      <View
        style={{
          marginRight: 5,
        }}>
        <RightIcon />
      </View>
    </TouchableOpacity>
  );
};

export default CheckoutDeviceList;
