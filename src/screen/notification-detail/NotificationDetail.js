import React, {useEffect, useState} from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import Sys from '../../components/Sys';
import THEME from '../../components/theme/theme';
import ItemIcon from '../notification/icons/ItemIcon';
import NotificationDetailSkeleton from './NotificationDetailSkeleton';
import SysFetch from '../../services/fetch';
import moment from 'moment';

const NotificationDetail = ({route}) => {
  const {id} = route.params; // Lấy announcementId từ params
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await SysFetch.get(`me/notifications/${id}`);
        setData(response.data);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [id]);

  if (loading) {
    return <NotificationDetailSkeleton />;
  }

  return (
    <Sys.Container hasHeader>
      <Sys.Header title={'Chi Tiết thông báo'} />
      <ScrollView>
        <View style={styles.container}>
          <View style={styles.topContainer}>
            <View style={styles.iconContainer}>
              <ItemIcon />
            </View>
            <View style={styles.contentContainer}>
              <View style={styles.headerContainer}>
                <Sys.Text style={styles.noti}>Thông báo ●</Sys.Text>
                <Sys.Text style={styles.date}>
                  {moment(data?.schedule_at).format('DD/MM/YYYY')}
                </Sys.Text>
              </View>
              <Sys.Text style={styles.title}>{data.title}</Sys.Text>
              <Sys.Text
                style={styles.content}
                dangerouslySetInnerHTML={{__html: data.body}}
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </Sys.Container>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  title: {
    color: '#001451',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    lineHeight: 28,
    fontSize: 18,
  },
  content: {
    lineHeight: 24,
  },
  iconContainer: {
    marginRight: 15,
  },
  noti: {
    lineHeight: 20,
    fontSize: 12,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginRight: 5,
  },
  date: {
    color: '#848484',
    lineHeight: 20,
    fontSize: 12,
  },
  topContainer: {
    flexDirection: 'row',
  },
  contentContainer: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
  },
});

export default NotificationDetail;
