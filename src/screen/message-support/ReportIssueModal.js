import React, {useState} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Modal from 'react-native-modal';
import Sys from '../../components/Sys';
import CloseIcon from '../report/icons/CloseIcon';
import CameraIcon from '../conform-checkin/icons/CameraIcon';
import RemoveIcon from '../camera/icons/RemoveIcon';
import AttachIcon from './AttachIcon';
import {useDispatch, useSelector} from 'react-redux';
import {SupportActions} from './services/support.slice';
import SelectIcon from '../sale-location/icons/SelectIcon';
import {useNavigation} from '@react-navigation/native';
import SysFetch from '../../services/fetch';
import DocumentPicker from 'react-native-document-picker';
import Toast from 'react-native-toast-message';
import FormData from 'form-data';
import {AppActions} from '../../app.slice';
import {isIOS} from '../../services/util';

const {width, height} = Dimensions.get('window');

const ReportIssueModal = ({isVisible, onClose}) => {
  const dispatch = useDispatch();
  const {
    categories,
    relationItems,
    isCategoryModalVisible,
    isItemModalVisible,
  } = useSelector(state => state.support);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedItem, setSelectedItem] = useState('');
  const [description, setDescription] = useState('');
  const [images, setImages] = useState([]);
  const [attachments, setAttachments] = useState([]);
  const {navigate} = useNavigation();

  const isFormValid = () => {
    return (
      selectedCategory &&
      description &&
      (relationItems.length === 0 || selectedItem)
    );
  };

  const handleAddAttachment = async () => {
    try {
      const result = await DocumentPicker.pick({
        type: [
          DocumentPicker.types.images,
          DocumentPicker.types.pdf,
          DocumentPicker.types.doc,
          DocumentPicker.types.docx,
          // DocumentPicker.types.plainText,
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ],
        allowMultiSelection: true,
      });

      result.forEach(file => {
        if (file.type.includes('image')) {
          setImages(prev => [...prev, file]);
        } else {
          setAttachments(prev => [...prev, file]);
        }
      });
    } catch (error) {
      if (DocumentPicker.isCancel(error)) {
        console.log('User cancelled the picker');
      } else {
        Toast.show({
          type: 'error',
          text1: 'Error selecting file',
          text2: error.toString(),
        });
      }
    }
  };

  const handleRemoveFile = (index, type) => {
    if (type === 'image') {
      setImages(images => images.filter((_, i) => i !== index));
    } else {
      setAttachments(attachments => attachments.filter((_, i) => i !== index));
    }
  };

  const handleSendReport = async () => {
    if (!isFormValid()) {
      Toast.show({
        type: 'error',
        text1: 'Lỗi xác nhận',
        text2: 'Vui lòng điền vào tất cả các trường bắt buộc.',
      });
      return;
    }
    onClose();
    dispatch(AppActions.setLoading(true));

    try {
      const formData = new FormData();
      formData.append('category_id', selectedCategory.id);
      if (selectedItem) {
        formData.append('related_id', selectedItem.id);
      }
      formData.append('body', description);

      images.forEach((image, index) => {
        formData.append(`photos[${index}]`, {
          uri: image.uri,
          type: 'image/jpeg',
          name: image.name || `photo-${index}.jpg`,
        });
      });

      attachments.forEach((file, index) => {
        formData.append(`files[${index}]`, {
          uri: file.uri,
          type: file.type,
          name: file.name,
        });
      });

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Accept: 'application/json',
        },
      };
      console.log('formData', formData);
      const response = await SysFetch.post('support/tickets', formData, config);
      console.log('response', response);
      if (response?.success) {
        Toast.show({
          type: 'success',
          text1: 'Thông báo',
          text2: response?.message || 'Report Submitted Successfully',
        });
        setSelectedCategory('');
        setSelectedItem('');
        setDescription('');
        setImages([]);
        setAttachments([]);
        dispatch(AppActions.setLoading(false));
        dispatch(SupportActions.setRefreshNeeded(true));
      } else {
        dispatch(AppActions.setLoading(false));
        dispatch(SupportActions.setModalVisible({show: true}));
        console.log('Failed to submit the report', response);
      }
    } catch (error) {
      console.log('handleSendReport', error);
      dispatch(AppActions.setLoading(false));
      dispatch(SupportActions.setModalVisible({show: true}));
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.toString(),
      });
    }
  };

  const openCamera = () => {
    dispatch(SupportActions.setModalVisible({show: false}));
    navigate('CameraScreen', {
      onCompleteCamera: _photo => {
        setImages([...images, {..._photo, type: 'image'}]);
        navigate('MessageSupportScreen');
        dispatch(SupportActions.setModalVisible({show: true}));
      },
      title: 'Xem lại ảnh',
      description: 'Cần chụp ảnh sao cho có thể nhìn rõ',
    });
  };

  const handleCategorySelect = category => {
    setSelectedCategory(category);
    dispatch(SupportActions.setRelationItems(category.relationItems));
    setSelectedItem(undefined);
    dispatch(
      SupportActions.setIsCategoryModalVisible({
        show: false,
        nextModal: 'isModalVisible',
      }),
    );
  };

  const handleItemSelect = item => {
    setSelectedItem(item);
    dispatch(
      SupportActions.setIsItemModalVisible({
        show: false,
        nextModal: 'isModalVisible',
      }),
    );
  };

  return (
    <>
      <Modal
        isVisible={isVisible}
        onSwipeComplete={onClose}
        onBackdropPress={onClose}
        swipeDirection="down"
        avoidKeyboard={true}
        onModalHide={() => dispatch(SupportActions.handleModalHide())}
        style={styles.modalStyle}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Sys.Text style={styles.modalText}>Gửi phản ánh</Sys.Text>
            <TouchableOpacity onPress={onClose}>
              <CloseIcon />
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            onPress={() => {
              dispatch(
                SupportActions.setModalVisible({
                  show: false,
                  nextModal: 'isCategoryModalVisible',
                }),
              );
            }}
            style={styles.selector}>
            <Sys.Text style={{color: selectedCategory ? 'black' : '#D1D1D1'}}>
              {selectedCategory ? selectedCategory.name : 'Vấn đề cần hỗ trợ'}
            </Sys.Text>
            <View style={{position: 'absolute', right: 10, top: 10}}>
              <SelectIcon />
            </View>
          </TouchableOpacity>
          {relationItems.length > 0 && (
            <TouchableOpacity
              onPress={() => {
                dispatch(
                  SupportActions.setModalVisible({
                    show: false,
                    nextModal: 'isItemModalVisible',
                  }),
                );
              }}
              style={styles.selector}>
              <Sys.Text style={{color: selectedItem ? 'black' : '#D1D1D1'}}>
                {selectedItem ? selectedItem.name : 'Chọn trang thiết bị'}
              </Sys.Text>
              <View style={{position: 'absolute', right: 10, top: 10}}>
                <SelectIcon />
              </View>
            </TouchableOpacity>
          )}
          <Sys.Input
            label="Nhập nội dung cần hỗ trợ"
            value={description}
            onChangeText={setDescription}
            multiline
            style={{height: 100}}
          />
          <View style={styles.attachmentsContainer}>
            {images.map((image, index) => (
              <View key={'image-' + index} style={styles.attachment}>
                <Image source={{uri: image.uri}} style={styles.image} />
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => handleRemoveFile(index, 'image')}>
                  <RemoveIcon />
                </TouchableOpacity>
              </View>
            ))}
            {attachments.map((file, index) => (
              <View key={'file-' + index} style={styles.attachment}>
                <View style={styles.fileIconContainer}>
                  <Sys.Text style={styles.fileIconText}>
                    {file.name.split('.').pop().toUpperCase()}
                  </Sys.Text>
                </View>
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => handleRemoveFile(index, 'file')}>
                  <RemoveIcon />
                </TouchableOpacity>
              </View>
            ))}
          </View>
          <View style={styles.actions}>
            <TouchableOpacity onPress={openCamera} style={styles.iconButton}>
              <CameraIcon />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleAddAttachment}
              style={styles.iconButton}>
              <AttachIcon />
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={[styles.sendButton, !isFormValid() && styles.buttonDisabled]}
            onPress={handleSendReport}
            disabled={!isFormValid()}>
            <Sys.Text style={styles.buttonText}>Cập nhật</Sys.Text>
          </TouchableOpacity>
        </View>
      </Modal>
      <Modal
        isVisible={isCategoryModalVisible}
        onSwipeComplete={() =>
          dispatch(
            SupportActions.setIsCategoryModalVisible({
              show: false,
              nextModal: 'isModalVisible',
            }),
          )
        }
        onBackdropPress={() =>
          dispatch(
            SupportActions.setIsCategoryModalVisible({
              show: false,
              nextModal: 'isModalVisible',
            }),
          )
        }
        swipeDirection="down"
        onModalHide={() => dispatch(SupportActions.handleModalHide())}
        propagateSwipe={true}
        style={styles.modalStyle}>
        <View style={[styles.modalContent, {paddingVertical: isIOS ? 60 : 20}]}>
          <FlatList
            data={categories}
            keyExtractor={item => item.id.toString()}
            renderItem={({item}) => (
              <TouchableOpacity
                onPress={() => handleCategorySelect(item)}
                style={styles.modalItem}>
                <Sys.Text>{item.name}</Sys.Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </Modal>
      {relationItems.length > 0 && (
        <Modal
          propagateSwipe={true}
          isVisible={isItemModalVisible}
          onSwipeComplete={() =>
            dispatch(
              SupportActions.setIsItemModalVisible({
                show: false,
                nextModal: 'isModalVisible',
              }),
            )
          }
          onBackdropPress={() =>
            dispatch(
              SupportActions.setIsItemModalVisible({
                show: false,
                nextModal: 'isModalVisible',
              }),
            )
          }
          swipeDirection="down"
          onModalHide={() => dispatch(SupportActions.handleModalHide())}
          style={styles.modalStyle}>
          <View
            style={[styles.modalContent, {paddingVertical: isIOS ? 60 : 20}]}>
            <FlatList
              data={relationItems}
              keyExtractor={item => item.id.toString()}
              renderItem={({item}) => (
                <TouchableOpacity
                  onPress={() => handleItemSelect(item)}
                  style={styles.modalItem}>
                  <Sys.Text>{item.name}</Sys.Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </Modal>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  modalStyle: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  modalText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  attachmentsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 10,
  },
  attachment: {
    position: 'relative',
    marginRight: 10,
    marginBottom: 10,
  },
  image: {
    width: 50,
    height: 50,
    borderRadius: 5,
  },
  removeButton: {
    position: 'absolute',
    top: -5,
    right: -5,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  iconButton: {
    backgroundColor: '#e0e0e0',
    padding: 10,
    borderRadius: 5,
  },
  sendButton: {
    backgroundColor: '#0062FF',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#dad8d8',
  },
  selector: {
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D1D1',
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  modalItem: {
    padding: 10,
    marginBottom: 5,
    backgroundColor: '#d1d1d110',
    borderRadius: 10,
  },
  fileIconContainer: {
    height: 50,
    width: 50,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#007BFF',
  },
  fileIconText: {
    color: 'white',
    fontWeight: 'bold',
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
  },
});

export default ReportIssueModal;
