import {call, put, takeEvery} from 'redux-saga/effects';
import Toast from 'react-native-toast-message';
import SysFetch from '../../../services/fetch';
import {SupportActions} from './support.slice';
import qs from 'qs';

function* SupportSaga() {
  yield takeEvery(SupportActions.getList, getList);
  yield takeEvery(SupportActions.getDetail, getDetail);
  yield takeEvery(SupportActions.getComments, getComments);
  yield takeEvery(SupportActions.getStatus, getStatus);
  yield takeEvery(SupportActions.updateStatus, updateStatus);
  yield takeEvery(SupportActions.getCategory, getCategory);
  yield takeEvery(SupportActions.getBMListByCategory, getBMListByCategory);
}

export default SupportSaga;

function* getBMListByCategory({payload}) {
  const {onSuccess, onFail, id, body} = payload;
  try {
    let currentURI = `support/tickets/locations/branch?${qs.stringify(body, {
      encode: false,
    })}`;
    if (id != 0) {
      currentURI += '&ticket_status[]=' + id;
    }
    const response = yield SysFetch.get(`${currentURI}`);
    if (response.success) {
      onSuccess && onSuccess(response);
    }
  } catch (error) {
    if (onFail) {
      onFail(error?.response?.data);
    } else {
      Toast.show({
        type: 'error',
        text1: 'Lỗi',
        text2: error.toString(),
      });
    }
  }
}

function* getCategory({payload}) {
  const {onSuccess, onFail} = payload;
  try {
    const response = yield SysFetch.get('support/tickets/categories');
    yield put(SupportActions.getCategorySuccess(response));
    onSuccess && onSuccess(response);
  } catch (error) {
    yield put(SupportActions.getCategoryFailure());
    if (onFail) {
      onFail(error?.response?.data);
    } else {
      Toast.show({
        type: 'error',
        text1: 'Lỗi',
        text2: error.toString(),
      });
    }
  }
}

function* updateStatus({payload}) {
  const {onSuccess, onFail, id, status} = payload;
  try {
    const rs = yield SysFetch.post(`support/tickets/${id}/status`, {status});
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
    // handle error
  }
}

function* getStatus({payload}) {
  const {onSuccess, onFail} = payload;
  try {
    const rs = yield SysFetch.get('support/tickets/statuses?pageSize=-1');
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getDetail({payload}) {
  const {onSuccess, onFail, id} = payload;
  try {
    const rs = yield SysFetch.get(`support/tickets/${id}/comment?pageSize=-1`);
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
    // handle error
  }
}

function* getComments({payload}) {
  const {onSuccess, onFail, id} = payload;
  try {
    const rs = yield SysFetch.get(`support/tickets/${id}/comment`);
    yield put(SupportActions.setCommentsSuccess(rs.data));
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
    // handle error
  }
}

function* getList({payload}) {
  const {onSuccess, onFail, currentTab, branch, search} = payload;
  try {
    yield call(getCategory, {
      payload: {
        onSuccess: () => {},
        onFail: () => {},
      },
    });

    const params = {
      search,
      ...(currentTab !== 0 && {ticket_status: [currentTab]}),
      ...(branch
        ? {
            branch: [branch],
          }
        : {}),
    };

    const response = yield SysFetch.get('support/tickets?pageSize=-1', params);
    yield put(SupportActions.getListSuccess(response.data));

    if (onSuccess) {
      onSuccess(response);
    }
  } catch (error) {
    console.error('Failed to fetch list:', error);
    yield put(SupportActions.getListFailure());

    if (onFail) {
      onFail(error.response?.data);
    } else {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.toString(),
      });
    }

    if (__DEV__) {
      console.log('Error fetching tickets:', error);
    }
  }
}
