import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  categories: [],
  data: [],
  comments: [],
  relationItems: [],
  isLoading: false,
  error: null,
  refreshNeeded: false,
  nextModal: null,
  isModalVisible: false,
  isCategoryModalVisible: false,
  isItemModalVisible: false,
};

const SupportSlice = createSlice({
  name: 'support',
  initialState,
  reducers: {
    getDetail: () => {},
    getComments: () => {},
    setCommentsSuccess: (state, action) => {
      state.comments = action.payload;
    },
    getStatus: () => {},
    updateStatus: () => {},
    getCategory: state => {},
    setRefreshNeeded(state, action) {
      state.refreshNeeded = action.payload;
    },
    getCategorySuccess: (state, action) => {
      state.categories = action.payload;
    },
    getCategoryFailure: state => {
      state.isLoading = false;
    },
    getList: state => {
      state.isLoading = true;
    },
    getListSuccess: (state, action) => {
      state.isLoading = false;
      state.data = action.payload;
    },
    getListFailure: state => {
      state.isLoading = false;
    },
    getBMListByCategory: () => {},
    setModalVisible: (state, action) => {
      const {nextModal = null, show} = action.payload;
      state.nextModal = nextModal;
      state.isModalVisible = show;
    },
    setIsCategoryModalVisible: (state, action) => {
      const {nextModal = null, show} = action.payload;
      state.nextModal = nextModal;
      state.isCategoryModalVisible = show;
    },
    setIsItemModalVisible: (state, action) => {
      const {nextModal = null, show} = action.payload;
      state.nextModal = nextModal;
      state.isItemModalVisible = show;
    },
    setCurrentTab: (state, action) => {
      state.currentTab = action.payload;
    },
    setNextModal: (state, action) => {
      state.nextModal = action.payload;
    },
    handleModalHide: state => {
      if (state.nextModal) {
        state[state.nextModal] = true;
        state.nextModal = null;
      }
    },
    setRelationItems: (state, action) => {
      state.relationItems = action.payload;
    },
  },
});

const SupportReducers = SupportSlice.reducer;
export default SupportReducers;

export const SupportActions = SupportSlice.actions;
export const SupportSelectors = {};
