import {useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {
  FlatList,
  Image,
  RefreshControl,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import {SupportActions} from './services/support.slice';
import _ from 'lodash';
import ReportIssueModal from './ReportIssueModal';
import MessageSupportSkeleton from './MessageSupportSkeleton';
import PlusIcon from './PlusIcon';
import Color from '../../components/theme/Color';

const MessageSupportScreen = () => {
  const dispatch = useDispatch();
  const {isModalVisible, refreshNeeded} = useSelector(state => state.support);
  const {params} = useRoute();
  const [loading, setLoading] = useState(true);
  const isFocused = useIsFocused();
  const {view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);
  const [data, setData] = useState([]);
  const [categoryList, setCategoryList] = useState([]);
  const [currentTab, setCurrentTab] = useState(params?.currentCategoryId || 0);

  useEffect(() => {
    if (isFocused) {
      getData();
    }
  }, [isFocused, currentTab]);

  useEffect(() => {
    if (refreshNeeded) {
      getData();
      dispatch(SupportActions.setRefreshNeeded(false));
    }
  }, [refreshNeeded]);

  const getData = () => {
    dispatch(
      SupportActions.getList({
        search: params?.posCode,
        branch: params?.branchCode,
        currentTab,
        onSuccess: rs => {
          setLoading(false);
          setData(rs?.data);
        },
        onFail: () => {
          setLoading(false);
        },
      }),
    );
  };

  useEffect(() => {
    dispatch(
      SupportActions.getStatus({
        onSuccess: rs => {
          setCategoryList([
            {
              id: 0,
              name: 'Tất cả',
            },
            ...Object.keys(rs).map(x => {
              return {
                id: x,
                name: rs[`${x}`],
              };
            }),
          ]);
        },
      }),
    );
  }, []);

  return (
    <Sys.Container hasHeader backgroundColor="white">
      <Sys.Header
        hideGoBack={!params?.posCode && !params?.branchCode}
        title={'Tin nhắn phản ánh'}
        right={
          isPos ? (
            <TouchableOpacity
              onPress={() =>
                dispatch(SupportActions.setModalVisible({show: true}))
              }
              style={{
                marginTop: 5,
                marginRight: 15,
              }}>
              <PlusIcon width={30} height={30} stroke={Color.white} />
            </TouchableOpacity>
          ) : null
        }
      />
      {loading ? (
        <MessageSupportSkeleton />
      ) : (
        <>
          <View
            style={{
              padding: 10,
            }}>
            <FlatList
              horizontal
              showsHorizontalScrollIndicator={false}
              data={categoryList}
              keyExtractor={(item, index) => `${index}`}
              renderItem={({item, index}) => {
                return (
                  <View>
                    <TouchableOpacity
                      activeOpacity={0.8}
                      onPress={() => {
                        setCurrentTab(item.id);
                      }}
                      style={{
                        padding: 10,
                      }}>
                      <Sys.Text
                        style={{
                          color: currentTab === item.id ? '#0062FF' : 'black',
                          fontWeight: currentTab === item.id ? '600' : '400',
                        }}>
                        {item?.name}
                      </Sys.Text>
                    </TouchableOpacity>
                    <View
                      style={{
                        height: 3,
                        backgroundColor:
                          currentTab === item.id ? '#0062FF' : undefined,
                        borderTopEndRadius: 50,
                        borderTopStartRadius: 50,
                      }}
                    />
                  </View>
                );
              }}
            />
          </View>
          <FlatList
            refreshControl={
              <RefreshControl refreshing={false} onRefresh={getData} />
            }
            showsVerticalScrollIndicator={false}
            data={data}
            keyExtractor={(item, index) => `${index}`}
            renderItem={({item, index}) => {
              return <OneMessage item={item} index={index} />;
            }}
            ListEmptyComponent={
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginVertical: 50,
                }}>
                <Sys.Text
                  style={{
                    color: '#00000080',
                  }}>
                  Không có dữ liệu
                </Sys.Text>
              </View>
            }
            contentContainerStyle={{paddingBottom: 100}}
          />

          {isPos && (
            <>
              {/*<TouchableOpacity*/}
              {/*  onPress={() =>*/}
              {/*    dispatch(SupportActions.setModalVisible({show: true}))*/}
              {/*  }*/}
              {/*  style={{*/}
              {/*    flexDirection: 'row',*/}
              {/*    alignItems: 'center',*/}
              {/*    justifyContent: 'center',*/}
              {/*    backgroundColor: '#0062FF',*/}
              {/*    padding: 10,*/}
              {/*    marginHorizontal: 20,*/}
              {/*    borderRadius: 15,*/}
              {/*    marginBottom: 100,*/}
              {/*    marginTop: 0,*/}
              {/*  }}>*/}
              {/*  <Sys.Text*/}
              {/*    style={{*/}
              {/*      color: 'white',*/}
              {/*      fontWeight: isAndroid ? 'bold' : 700,*/}
              {/*    }}>*/}
              {/*    Gửi phản ánh*/}
              {/*  </Sys.Text>*/}
              {/*</TouchableOpacity>*/}

              <ReportIssueModal
                isVisible={isModalVisible}
                onClose={() =>
                  dispatch(SupportActions.setModalVisible({show: false}))
                }
              />
            </>
          )}
        </>
      )}
    </Sys.Container>
  );
};

export default MessageSupportScreen;

const OneMessage = ({item, index}) => {
  const {navigate} = useNavigation();
  return (
    <TouchableOpacity
      onPress={() =>
        navigate('MessageDetailScreen', {
          id: item.id,
        })
      }
      style={{
        marginHorizontal: 10,
        borderRadius: 15,
        padding: 10,
        flexDirection: 'row',
        marginBottom: 10,
        backgroundColor: index % 2 === 0 ? '#0062FF05' : '#00000005',
      }}>
      <View
        style={{
          width: 55,
          marginRight: 10,
        }}>
        <Sys.Text
          style={{
            fontSize: 10,
            color: '#696974',
            lineHeight: 22,
            textAlign: 'center',
          }}>
          {moment(item?.created_at).format('DD/MM/YYYY')}
        </Sys.Text>
        <Sys.Text
          style={{
            fontSize: 10,
            color: '#696974',
            lineHeight: 22,
            textAlign: 'center',
          }}>
          {moment(item?.created_at).format('hh:mm A')}
        </Sys.Text>
        {item?.creator?.avatar && (
          <Image
            style={{
              width: 50,
              height: 50,
              borderRadius: 50,
              marginTop: 10,
            }}
            source={{
              uri: item?.creator?.avatar,
            }}
          />
        )}
      </View>
      <View
        style={{
          flex: 1,
        }}>
        <Sys.Text
          style={{
            color: '#0062ff',
          }}>
          {item?.pos?.address}/{item?.pos?.posCode}
        </Sys.Text>
        <Sys.Text
          style={{
            color: '#171725',
            fontWeight: '600',
            lineHeight: 24,
          }}>
          {item?.category}
        </Sys.Text>
        <Sys.Text
          style={{
            color: '#92929D',
            height: 40,
          }}>
          {_.truncate(item?.body, {
            length: 80,
            omission: '...',
          })}
        </Sys.Text>
      </View>
    </TouchableOpacity>
  );
};
