import React from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';

const {width} = Dimensions.get('window');

const MessageSupportSkeleton = () => {
  return (
    <View style={styles.container}>
      <View style={styles.categoryBar}>
        {[...Array(5)].map((_, index) => (
          <View key={index} style={styles.category} />
        ))}
      </View>
      {[...Array(10)].map((_, index) => (
        <View key={index} style={styles.messageItem}>
          <View style={styles.avatar} />
          <View style={styles.content}>
            <View style={styles.title} />
            <View style={styles.textLine} />
            <View style={styles.textLineShort} />
          </View>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 10,
    backgroundColor: '#FFF',
  },
  categoryBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 10,
  },
  category: {
    backgroundColor: '#eee',
    height: 20,
    borderRadius: 10,
    width: width * 0.2,
  },
  messageItem: {
    flexDirection: 'row',
    padding: 10,
    marginBottom: 10,
    backgroundColor: '#F0F0F0',
    borderRadius: 10,
  },
  avatar: {
    width: 55,
    height: 55,
    borderRadius: 27.5,
    backgroundColor: '#ddd',
  },
  content: {
    flex: 1,
    paddingHorizontal: 10,
  },
  title: {
    height: 16,
    backgroundColor: '#eee',
    borderRadius: 4,
    marginBottom: 5,
  },
  textLine: {
    height: 14,
    backgroundColor: '#eee',
    marginBottom: 5,
    borderRadius: 4,
    width: '80%',
  },
  textLineShort: {
    height: 14,
    backgroundColor: '#eee',
    borderRadius: 4,
    width: '40%',
  },
});

export default MessageSupportSkeleton;
