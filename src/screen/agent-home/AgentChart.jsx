import {View} from 'react-native';
import React, {useEffect} from 'react';
import {useDispatch} from 'react-redux';
import {ReportActions} from '../report/services/report.slice';
import Sys from '../../components/Sys';

const AgentChart = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    getData();
  }, []);

  const getData = () => {
    dispatch(
      ReportActions.getAgentHomeChart({
        onSuccess: rs => {
          console.log(17, {data: rs.data});
        },
      }),
    );
  };
  return (
    <View>
      <Sys.Text>AgentChart</Sys.Text>
    </View>
  );
};

export default AgentChart;
