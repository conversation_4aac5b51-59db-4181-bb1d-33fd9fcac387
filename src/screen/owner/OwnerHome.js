import {ScrollView, View} from 'react-native';
import React from 'react';
import OwnerHomeRevenue from './OwnerHomeRevenue';
import OnwerHomeList from './OnwerHomeList';
import {useSelector} from 'react-redux';

const OwnerHome = () => {
  const {isPos, isStaff, isAgent, isBranchOwner, isBranchManager} = useSelector(
    state => state.account,
  );
  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={{
        marginTop: 10,
        flex: 1,
      }}>
      <OwnerHomeRevenue />
      {!isAgent && <OnwerHomeList />}
      <View style={{height: 130}} />
    </ScrollView>
  );
};

export default OwnerHome;
