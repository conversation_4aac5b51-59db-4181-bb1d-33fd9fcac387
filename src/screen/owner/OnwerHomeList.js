import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useCallback, useEffect, useState} from 'react';
import {
  Animated,
  FlatList,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Sys from '../../components/Sys';
import THEME from '../../components/theme/theme';
import {
  changeMode,
  fetchInitPosDetailStart,
} from '../device-list/services/inventory.slice';
import {AccountSelectors} from '../account/services/account.slice';
import SysFetch from '../../services/fetch';

const OnwerHomeList = () => {
  const {navigate} = useNavigation();
  const dispatch = useDispatch();
  const profile = useSelector(AccountSelectors.profile);
  const {isPos, isStaff, isAgent, isBranchOwner, isBranchManager} = useSelector(
    state => state.account,
  );

  // Animation states
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  let posCode;
  if (isPos) {
    posCode = profile.username;
  } else if (isStaff || isBranchOwner || isBranchManager) {
    posCode = profile?.owner_pos?.posCode;
  }
  const {sectionsFull} = useSelector(state => state.inventory);
  const [data, setData] = useState({
    surveys: {},
    marketing: {},
    tickets: {},
    audits: {},
  });
  const isFocused = useIsFocused();

  useEffect(() => {
    if (posCode && isFocused) {
      setIsLoading(true);
      getData(posCode);
    }
  }, [posCode, isFocused]);

  // Handle animations when component becomes focused
  useEffect(() => {
    if (isFocused) {
      // Reset and start animations
      fadeAnim.setValue(0);
      slideAnim.setValue(30);

      const timer = setTimeout(() => {
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 400,
            useNativeDriver: true,
          }),
        ]).start();
      }, 50);

      return () => clearTimeout(timer);
    }
  }, [isFocused, fadeAnim, slideAnim]);

  const getData = useCallback(
    async posCode => {
      dispatch(fetchInitPosDetailStart({posCode, mode: 2}));
      try {
        const [
          surveyResponse,
          marketingResponse,
          ticketsResponse,
          auditsResponse,
        ] = await Promise.all([
          SysFetch.get('survey/getActive', {pos: posCode}),
          SysFetch.get('marketing/getMarketing', {pos: posCode}),
          SysFetch.get('support/tickets', {pos: posCode}),
          SysFetch.get('inventory-plan/getActive', {pos: posCode}),
        ]);
        setData({
          surveys: surveyResponse,
          marketing: marketingResponse,
          tickets: ticketsResponse,
          audits: auditsResponse,
        });
      } catch (error) {
        console.error('Failed to fetch data:', error);
      } finally {
        setIsLoading(false);
        setIsRefreshing(false);
      }
    },
    [dispatch],
  );

  const onRefresh = useCallback(() => {
    if (posCode) {
      setIsRefreshing(true);
      getData(posCode);
    }
  }, [posCode, getData]);
  const menuList = [
    {
      label: 'Danh sách trang thiết bị',
      subtitle: 'Quản lý thiết bị và vật tư',
      icon: 'devices',
      gradient: ['#6366F1', '#4F46E5'],
      onPress: () => {
        dispatch(changeMode(2));
        navigate('DeviceListScreen');
      },
      count: sectionsFull.reduce(
        (acc, section) => acc + section.data.length,
        0,
      ),
    },
    {
      label: 'Vật phẩm marketing',
      subtitle: 'Tài liệu và vật phẩm quảng cáo',
      icon: 'bullhorn',
      gradient: ['#10B981', '#059669'],
      onPress: () => {
        navigate('MarketingScreen', {
          pos_id: posCode,
        });
      },
      count: data.marketing?.not_completed || 0,
    },
    {
      label: 'Khảo sát',
      subtitle: 'Khảo sát và đánh giá',
      icon: 'clipboard-list',
      gradient: ['#F59E0B', '#D97706'],
      onPress: () => {
        navigate('SurveyScreen', {
          pos_id: posCode,
        });
      },
      count: data.surveys?.not_completed || 0,
    },
  ];

  // Modern List Item Component
  const ModernListItem = ({item, index}) => {
    const [itemScale] = useState(new Animated.Value(1));

    const handlePressIn = () => {
      Animated.spring(itemScale, {
        toValue: 0.98,
        useNativeDriver: true,
      }).start();
    };

    const handlePressOut = () => {
      Animated.spring(itemScale, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    };

    return (
      <Animated.View
        style={[
          modernStyles.listItem,
          {
            transform: [{scale: itemScale}],
          },
        ]}>
        <TouchableOpacity
          onPress={item?.onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.9}
          style={modernStyles.listItemTouchable}>
          <LinearGradient
            colors={['#ffffff', '#f8f9fa']}
            style={modernStyles.listItemGradient}>
            {/* Icon Container */}
            <View style={modernStyles.iconContainer}>
              <LinearGradient
                colors={item.gradient}
                style={modernStyles.iconGradient}
                start={{x: 0, y: 0}}
                end={{x: 1, y: 1}}>
                <MaterialCommunityIcons
                  name={item.icon}
                  size={24}
                  color="white"
                />
              </LinearGradient>
            </View>

            {/* Content */}
            <View style={modernStyles.contentContainer}>
              <Sys.Text style={modernStyles.labelText}>{item.label}</Sys.Text>
              <Sys.Text style={modernStyles.subtitleText}>
                {item.subtitle}
              </Sys.Text>
            </View>

            {/* Count Badge */}
            <View style={modernStyles.badgeContainer}>
              <LinearGradient
                colors={item.gradient}
                style={modernStyles.countBadge}
                start={{x: 0, y: 0}}
                end={{x: 1, y: 1}}>
                <Sys.Text style={modernStyles.countText}>{item.count}</Sys.Text>
              </LinearGradient>
            </View>

            {/* Arrow Icon */}
            <View style={modernStyles.arrowContainer}>
              <MaterialCommunityIcons
                name="chevron-right"
                size={24}
                color={THEME.Color.gray}
              />
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  // Don't render if not focused to prevent navigation issues
  if (!isFocused) {
    return null;
  }

  if (isLoading && !isRefreshing) {
    return (
      <View style={modernStyles.loadingContainer}>
        <MaterialCommunityIcons
          name="loading"
          size={48}
          color={THEME.Color.primary}
        />
        <Sys.Text style={modernStyles.loadingText}>
          Đang tải dữ liệu...
        </Sys.Text>
      </View>
    );
  }

  return (
    <View style={modernStyles.container}>
      {/* Header */}
      <View style={modernStyles.header}>
        <MaterialCommunityIcons
          name="format-list-bulleted"
          size={24}
          color={THEME.Color.primary}
        />
        <Sys.Text style={modernStyles.headerText}>Danh mục quản lý</Sys.Text>
      </View>

      <Animated.View
        style={[
          modernStyles.listWrapper,
          {
            opacity: fadeAnim,
            transform: [{translateY: slideAnim}],
          },
        ]}>
        <FlatList
          data={menuList}
          keyExtractor={(item, index) => `modern-item-${index}`}
          renderItem={({item, index}) => (
            <ModernListItem item={item} index={index} />
          )}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={onRefresh}
              colors={[THEME.Color.primary]}
              tintColor={THEME.Color.primary}
            />
          }
          contentContainerStyle={modernStyles.listContainer}
        />
      </Animated.View>
    </View>
  );
};

// Modern Styles
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    fontSize: 16,
    color: THEME.Color.gray,
    marginTop: 12,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  headerText: {
    fontSize: 18,
    fontWeight: '700',
    color: THEME.Color.text,
    marginLeft: 12,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    letterSpacing: 0.5,
  },
  listWrapper: {
    flex: 1,
  },
  listContainer: {
    paddingVertical: 8,
  },
  listItem: {
    marginHorizontal: 16,
    marginVertical: 6,
  },
  listItemTouchable: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  listItemGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 18,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  iconContainer: {
    marginRight: 16,
  },
  iconGradient: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  contentContainer: {
    flex: 1,
    marginRight: 12,
  },
  labelText: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  subtitleText: {
    fontSize: 13,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    lineHeight: 18,
  },
  badgeContainer: {
    marginRight: 12,
  },
  countBadge: {
    minWidth: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  countText: {
    fontSize: 14,
    fontWeight: '700',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  arrowContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default OnwerHomeList;
