import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useCallback, useEffect, useState} from 'react';
import {FlatList, TouchableOpacity, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import {
  changeMode,
  fetchInitPosDetailStart,
} from '../device-list/services/inventory.slice';
import RightIcon from './icons/RightIcon';
import {AccountSelectors} from '../account/services/account.slice';
import SysFetch from '../../services/fetch';

const OnwerHomeList = () => {
  const {navigate} = useNavigation();
  const dispatch = useDispatch();
  const profile = useSelector(AccountSelectors.profile);
  const {isPos, isStaff, isAgent, isBranchOwner, isBranchManager} = useSelector(
    state => state.account,
  );
  let posCode;
  if (isPos) {
    posCode = profile.username;
  } else if (isStaff || isBranchOwner || isBranchManager) {
    posCode = profile?.owner_pos?.posCode;
  }
  const {sectionsFull} = useSelector(state => state.inventory);
  const [data, setData] = useState({
    surveys: {},
    marketing: {},
    tickets: {},
    audits: {},
  });
  const isFocused = useIsFocused();

  useEffect(() => {
    if (posCode) {
      getData(posCode);
    }
  }, [posCode, isFocused]);

  const getData = useCallback(
    async posCode => {
      dispatch(fetchInitPosDetailStart({posCode, mode: 2}));
      try {
        const [
          surveyResponse,
          marketingResponse,
          ticketsResponse,
          auditsResponse,
        ] = await Promise.all([
          SysFetch.get('survey/getActive', {pos: posCode}),
          SysFetch.get('marketing/getMarketing', {pos: posCode}),
          SysFetch.get('support/tickets', {pos: posCode}),
          SysFetch.get('inventory-plan/getActive', {pos: posCode}),
        ]);
        setData({
          surveys: surveyResponse,
          marketing: marketingResponse,
          tickets: ticketsResponse,
          audits: auditsResponse,
        });
      } catch (error) {
        console.error('Failed to fetch data:', error);
      }
    },
    [posCode, isFocused],
  );
  const menuList = [
    {
      label: 'Danh sách trang thiết bị',
      onPress: () => {
        dispatch(changeMode(2));
        navigate('DeviceListScreen');
      },
      count: sectionsFull.reduce(
        (acc, section) => acc + section.data.length,
        0,
      ),
    },
    {
      label: 'Vật phẩm marketing',
      onPress: () => {
        navigate('MarketingScreen', {
          pos_id: posCode,
        });
      },
      count: data.marketing?.not_completed,
    },
    {
      label: 'Khảo sát',
      onPress: () => {
        navigate('SurveyScreen', {
          pos_id: posCode,
        });
      },
      count: data.surveys?.not_completed,
    },
  ];

  return (
    <View>
      <FlatList
        data={menuList}
        keyExtractor={(item, index) => `${index}`}
        renderItem={({item, index}) => {
          return (
            <TouchableOpacity
              onPress={item?.onPress}
              style={{
                backgroundColor: 'white',
                marginTop: 10,
                marginHorizontal: 15,
                borderRadius: 20,
                paddingHorizontal: 15,
                paddingVertical: 15,
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <View
                style={{
                  flex: 1,
                }}>
                <Sys.Text
                  style={{
                    color: '#0062FF',
                    fontSize: 18,
                    fontWeight: '600',
                  }}>
                  {item.label}
                </Sys.Text>
              </View>
              <View
                style={{
                  marginRight: 20,
                  width: 32,
                  height: 32,
                  backgroundColor: '#EF7721',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: 50,
                }}>
                <Sys.Text
                  style={{
                    color: 'white',
                    fontWeight: '600',
                    fontSize: 16,
                  }}>
                  {item.count}
                </Sys.Text>
              </View>
              <RightIcon />
            </TouchableOpacity>
          );
        }}
      />
    </View>
  );
};

export default OnwerHomeList;
