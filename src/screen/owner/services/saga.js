import {takeLatest} from 'redux-saga/effects';
import {OwnerActions} from './slice';
import moment from 'moment';
import qs from 'qs';
import SysFetch from '../../../services/fetch';

function* OwnerSaga() {
  yield takeLatest(OwnerActions.getOwnerCurrentDay, getOwnerCurrentDay);
  yield takeLatest(OwnerActions.getOwnerCurrentMonth, getOwnerCurrentMonth);
  yield takeLatest(OwnerActions.getOwnerChart, getOwnerChart);
  yield takeLatest(OwnerActions.getReportChart, getReportChart);
  yield takeLatest(OwnerActions.getOwnerDateList, getOwnerDateList);
  yield takeLatest(OwnerActions.getOwnerGameByDateList, getOwnerGameByDateList);
}

export default OwnerSaga;

function* getOwnerGameByDateList({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    delete body.stacked_by;
    delete body.end_time;
    const rs = yield SysFetch.get('report/stats/daily/game', body);
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getOwnerDateList({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    delete body.stacked_by;
    const rs = yield SysFetch.get(
      `report/stats/daily/days?${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getReportChart({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/daily/days?${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getOwnerChart({payload}) {
  const {onSuccess, onFail} = payload;
  try {
    const event_time = moment().subtract('days', 7).format('YYYY-MM-DD');
    const end_time = moment().subtract('days', 1).format('YYYY-MM-DD');
    const rs = yield SysFetch.get(
      `report/stats/daily/days?${qs.stringify(
        {
          event_time,
          end_time,
          sort_by: 'code',
          stacked_by: 'game',
          sort_order: 'desc',
        },
        {
          encode: false,
        },
      )}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getOwnerCurrentMonth({payload}) {
  const {onSuccess, onFail} = payload;
  try {
    const event_time = moment().format('YYYY-MM');
    const rs = yield SysFetch.get('report/stats/monthly/months', {
      event_time,
      sort_by: 'total_revenue',
      sort_order: 'desc',
    });
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getOwnerCurrentDay({payload}) {
  const {onSuccess, onFail} = payload;
  try {
    const event_time = moment().subtract('days', 1).format('YYYY-MM-DD');
    const rs = yield SysFetch.get(
      `report/stats/daily/days?${qs.stringify(
        {event_time},
        {
          encode: false,
        },
      )}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}
