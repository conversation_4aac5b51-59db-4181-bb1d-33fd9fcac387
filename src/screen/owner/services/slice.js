import {createSlice} from '@reduxjs/toolkit';

const initialState = {};
const OwnerSlice = createSlice({
  name: 'owner',
  initialState,
  reducers: {
    getOwnerCurrentDay: () => {},
    getOwnerCurrentMonth: () => {},
    getOwnerChart: () => {},
    getReportChart: () => {},
    getOwnerDateList: () => {},
    getOwnerGameByDateList: () => {},
  },
});

const OwnerReducer = OwnerSlice.reducer;
export default OwnerReducer;

export const OwnerActions = OwnerSlice.actions;
