import {View, Text, TouchableOpacity} from 'react-native';
import React, {useMemo, useState} from 'react';
import Sys from '../../../components/Sys';
import * as Ant from 'react-native-animatable';
import OwnerReportRevenue from './OwnerReport.Revenue';
const OwnerReport = ({setBody, body}) => {
  const [currentTab, setCurrentTab] = useState(0);
  const tabList = [
    {
      name: `<PERSON>anh thu`,
      content: <OwnerReportRevenue body={body} setBody={setBody} />,
    },
    {
      name: `VP Marketing`,
      content: <OwnerReportRevenue body={body} setBody={setBody} />,
    },
  ];

  const renderTab = useMemo(() => {
    return tabList.map((x, index) => {
      return (
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            borderBottomWidth: 1,
            borderBottomColor: '#F1F1F5',
            marginBottom: 5,
          }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => {
              setCurrentTab(index);
            }}
            key={x.name}>
            <Sys.Text
              style={{
                padding: 10,
                textAlign: 'center',
                fontWeight: '600',
                lineHeight: 24,
                color: index === currentTab ? '#0062FF' : 'black',
              }}>
              {x.name}
            </Sys.Text>
            {index === currentTab ? (
              <Ant.View
                currentTab
                duration={300}
                animation={'zoomIn'}
                style={{
                  height: 4,
                  backgroundColor: '#0062FF',
                  borderTopEndRadius: 20,
                  borderTopStartRadius: 20,
                }}></Ant.View>
            ) : (
              <Ant.View
                currentTab
                duration={300}
                animation={'zoomIn'}
                style={{
                  height: 4,
                  backgroundColor: 'white',
                  borderTopEndRadius: 20,
                  borderTopStartRadius: 20,
                }}></Ant.View>
            )}
          </TouchableOpacity>
        </View>
      );
    });
  }, [currentTab]);

  return (
    <View style={{flex: 1}}>
      <View
        style={{
          backgroundColor: 'white',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {renderTab}
      </View>
      {tabList[currentTab].content}
    </View>
  );
};

export default OwnerReport;
