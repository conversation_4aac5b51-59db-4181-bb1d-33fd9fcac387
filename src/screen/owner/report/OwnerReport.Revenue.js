import moment from 'moment';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {FlatList, ScrollView, TouchableOpacity, View} from 'react-native';
import {useDispatch} from 'react-redux';
import Sys from '../../../components/Sys';
import {formatVND} from '../../../services/util';
import RevenueChart from '../../../components/charts/RevenueChart';
import DownIcon from '../../report/icons/DownIcon';
import DownsIcon from '../../report/icons/DownsIcon';
import RightCityIcon from '../../report/icons/RightCityIcon';
import SortIcon from '../../report/icons/SortIcon';
import UpIcon from '../../report/icons/UpIcon';
import {OwnerActions} from '../services/slice';

const OwnerReportRevenue = ({body, setBody}) => {
  const dispatch = useDispatch();
  const [dateList, setDateList] = useState([]);

  const loadDateList = useCallback(() => {
    dispatch(
      OwnerActions.getOwnerDateList({
        onSuccess: rs => {
          setDateList(rs?.data?.items || []);
        },
        onFail: rs => {},
        body: {
          ...body,
        },
      }),
    );
  }, [body, dispatch]);

  // Memoize onDataLoaded callback to prevent recreation
  const handleDataLoaded = useCallback(() => {
    loadDateList();
  }, [loadDateList]);

  // Memoize chart configuration to prevent unnecessary re-renders
  const chartConfig = useMemo(
    () => ({
      title: 'Báo cáo doanh thu theo thời gian',
      apiAction: 'report',
      apiParams: {
        ...body,
        sort_order: 'desc',
      },
      showDateRange: true,
      onDataLoaded: handleDataLoaded,
    }),
    [body, handleDataLoaded],
  );

  useEffect(() => {
    loadDateList();
  }, [loadDateList]);

  return (
    <ScrollView
      style={{
        backgroundColor: 'white',
        flex: 1,
      }}
      showsVerticalScrollIndicator={false}>
      <RevenueChart config={chartConfig} />
      <View
        style={{
          marginHorizontal: 10,
        }}>
        <View
          style={{
            backgroundColor: '#376DF7',
            flexDirection: 'row',
            padding: 10,
          }}>
          <Sys.Text
            style={{
              color: 'white',
              flex: 1,
              fontWeight: '500',
            }}>
            Ngày
          </Sys.Text>
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              width: 120,
            }}
            onPress={() => {
              setBody({
                ...body,
                sort_order: body.sort_order === 'desc' ? 'asc' : 'desc',
              });
            }}>
            <Sys.Text
              style={{
                color: 'white',
                marginRight: 6,
                fontWeight: '500',
              }}>
              Doanh thu
            </Sys.Text>
            <SortIcon />
          </TouchableOpacity>
          <Sys.Text
            style={{
              color: 'white',
              width: 60,
              textAlign: 'right',
              fontWeight: '500',
            }}>
            Thay đổi
          </Sys.Text>
        </View>
        <FlatList
          data={dateList}
          keyExtractor={(item, index) => `${index}`}
          renderItem={({item, index}) => {
            return <OneItem item={item} body={body} />;
          }}
        />
      </View>
      <View style={{height: 100}} />
    </ScrollView>
  );
};

export default OwnerReportRevenue;

const OneItem = ({item, body}) => {
  const dispatch = useDispatch();
  const [isShow, setIsShow] = useState(false);
  const [game, setGame] = useState([]);
  useEffect(() => {
    setIsShow(false);
  }, [body]);

  const getData = () => {
    dispatch(
      OwnerActions.getOwnerGameByDateList({
        onSuccess: rs => {
          setGame(rs?.data?.items);
        },
        onFail: rs => {},
        body: {
          ...body,
          event_time: item?.code,
        },
      }),
    );
  };

  return (
    <View
      style={{
        marginBottom: 2,
      }}>
      <TouchableOpacity
        onPress={() => {
          setIsShow(!isShow);
          if (!isShow) {
            getData();
          }
        }}
        style={{
          flexDirection: 'row',
          padding: 10,
          backgroundColor: !isShow ? '#F1F1F1' : '#001451',
          alignItems: 'center',
        }}>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          {!isShow ? <RightCityIcon /> : <DownIcon />}
          <Sys.Text
            style={{
              color: !isShow ? '#001451' : 'white',
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {moment(item?.code, 'YYYY-MM-DD').format('DD/MM/YYYY')}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 120,
          }}>
          <Sys.Text
            style={{
              textAlign: 'center',
              color: !isShow ? '#001451' : 'white',
              fontWeight: '600',
            }}>
            {formatVND(item?.total_revenue)}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 60,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          {item?.percentage_change == 0 ? (
            <></>
          ) : item?.percentage_change > 0 ? (
            <UpIcon />
          ) : (
            <DownsIcon />
          )}
          <Sys.Text
            style={{
              textAlign: 'right',
              color: !isShow ? '#001451' : 'white',
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.percentage_change}%
          </Sys.Text>
        </View>
      </TouchableOpacity>
      {isShow ? (
        <View
          style={{
            backgroundColor: '#F5F6FC',
          }}>
          <FlatList
            data={game}
            keyExtractor={(item, index) => `${index}`}
            renderItem={itemProps => {
              return <OneGame item={itemProps.item} />;
            }}
          />
        </View>
      ) : (
        <></>
      )}
    </View>
  );
};

const OneGame = ({item}) => {
  return (
    <View>
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          padding: 10,
          alignItems: 'center',
          marginBottom: 2,
        }}>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.name}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 120,
          }}>
          <Sys.Text
            style={{
              textAlign: 'center',
              fontWeight: '600',
            }}>
            {formatVND(item?.total_revenue)}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 60,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          {item?.percentage_change == 0 ? (
            <></>
          ) : item?.percentage_change > 0 ? (
            <UpIcon />
          ) : (
            <DownsIcon />
          )}
          <Sys.Text
            style={{
              textAlign: 'right',
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.percentage_change}%
          </Sys.Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};
