import React from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';

const {width} = Dimensions.get('window');

const SaleInformationSkeleton = () => {
  return (
    <View style={styles.container}>
      <ContentLoader
        speed={2}
        width={width * 0.9}
        height={1000}
        viewBox={`0 0 ${width * 0.95} 1000`}
        backgroundColor="#f3f3f3"
        foregroundColor="#ecebeb">
        {/* Map Placeholder */}
        <Rect x="0" y="0" rx="10" ry="10" width="100%" height="230" />

        {/* Information Section Placeholder */}
        <Rect x="15" y="250" rx="8" ry="8" width="80%" height="20" />
        <Rect x="15" y="280" rx="8" ry="8" width="60%" height="20" />
        <Rect x="15" y="320" rx="8" ry="8" width="90%" height="20" />
        <Rect x="15" y="350" rx="8" ry="8" width="70%" height="20" />

        {/* Features Section Placeholder */}
        <Rect x="15" y="400" rx="8" ry="8" width="80%" height="20" />
        <Rect x="15" y="430" rx="8" ry="8" width="70%" height="20" />

        {/* Detail Section Placeholder */}
        <Rect x="15" y="480" rx="8" ry="8" width="80%" height="20" />
        <Rect x="15" y="510" rx="8" ry="8" width="70%" height="20" />
        <Rect x="15" y="540" rx="8" ry="8" width="90%" height="20" />

        {/* Revenue Section Placeholder */}
        <Rect x="15" y="600" rx="8" ry="8" width="80%" height="20" />
        <Rect x="15" y="630" rx="8" ry="8" width="70%" height="20" />

        {/* Chart Ticket Section Placeholder */}
        <Rect x="15" y="690" rx="8" ry="8" width="80%" height="20" />
        <Rect x="15" y="720" rx="8" ry="8" width="70%" height="20" />

        {/* Device List Section Placeholder */}
        <Rect x="15" y="780" rx="8" ry="8" width="80%" height="20" />
        <Rect x="15" y="810" rx="8" ry="8" width="70%" height="20" />

        {/* History Section Placeholder */}
        <Rect x="15" y="870" rx="8" ry="8" width="80%" height="20" />
        <Rect x="15" y="900" rx="8" ry="8" width="70%" height="20" />
      </ContentLoader>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 0,
    backgroundColor: 'white',
  },
});

export default SaleInformationSkeleton;
