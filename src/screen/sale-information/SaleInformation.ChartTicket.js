import {useIsFocused} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useMemo, useState} from 'react';
import {useWindowDimensions, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {
  <PERSON>A<PERSON>s,
  VictoryBar,
  VictoryChart,
  VictoryTheme,
} from 'victory-native';
import {AppActions} from '../../app.slice';
import Sys from '../../components/Sys';
import {AccountSelectors} from '../account/services/account.slice';

moment.locale('vi');
const SaleInformationChartTicket = ({posDetail}) => {
  const dispatch = useDispatch();
  const {width} = useWindowDimensions();
  const profile = useSelector(AccountSelectors.profile);
  const isFocused = useIsFocused();
  const [data, setData] = useState([]);

  useEffect(() => {
    if (isFocused && posDetail?.posCode) {
      const posId = posDetail?.posCode;
      const startDate = moment().subtract(7, 'days').format('YYYY-MM-DD');
      const endDate = moment().format('YYYY-MM-DD');
      dispatch(
        AppActions.getChart({
          posId,
          startDate,
          endDate,
          onFail: rs => {},
          onSuccess: rs => {
            const chartData = rs?.data.map((x, index) => {
              let open = x?.open || 6; // Default open time is 6:00 if missing
              let close = x?.close || 22; // Default close time is 22:00 if missing

              // Add a small difference when open and close are the same
              if (open === close) {
                close += 0.1; // Add a slight difference to make the bar visible
              }

              return {
                x: x.event_date, // Use event_date for x-axis
                y: open, // Opening time (y)
                y0: close, // Closing time (y0), adjusted if necessary
              };
            });
            setData(chartData);
          },
        }),
      );
    }
  }, [profile, posDetail, isFocused]);

  const renderChart = useMemo(() => {
    // If no data is present, return an empty view instead of a chart
    if (data.length === 0) {
      return (
        <View>
          <Sys.Text
            style={{
              color: '#001451',
              marginTop: 15,
              textAlign: 'center',
            }}>
            Không có dữ liệu
          </Sys.Text>
        </View>
      );
    }

    return (
      <VictoryChart
        domain={{y: [6, 22]}} // y-axis range from 6 AM to 10 PM
        domainPadding={{x: 30}}
        theme={VictoryTheme.material}
        width={width - 20}>
        <VictoryAxis
          dependentAxis
          tickValues={[6, 8, 10, 12, 14, 16, 18, 20, 22]} // Custom tick values for every 2 hours
          tickFormat={t => `${t}:00`} // Format y-axis as hours (6:00, 8:00, etc.)
          style={{
            tickLabels: {fontSize: 10, padding: 5}, // Adjust tick label font and padding
          }}
        />
        <VictoryAxis
          tickFormat={t => moment(t).format('DD/MM')} // Format x-axis as event_date (e.g., 05-10)
          style={{
            tickLabels: {fontSize: 10, padding: 5, angle: -45}, // Adjust tick label font and padding
          }}
        />
        <VictoryBar
          style={{
            data: {
              fill: '#0062FF', // Bar color
              width: 20, // Bar width
            },
          }}
          data={data}
        />
      </VictoryChart>
    );
  }, [data]);

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'white',
        borderRadius: 20,
        marginHorizontal: 10,
      }}>
      <View>
        <Sys.Text
          style={{
            color: '#001451',
            marginTop: 15,
          }}>
          Thời gian bán vé đầu tiên và vé cuối theo ngày
        </Sys.Text>
      </View>
      {renderChart}
    </View>
  );
};

export default SaleInformationChartTicket;
