import React from 'react';
import {Image, TouchableOpacity, View} from 'react-native';
import Sys from '../../components/Sys';
import FooterRightIcon from './icons/FooterRightIcon';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {AccountSelectors} from '../account/services/account.slice';
import {changeMode} from '../device-list/services/inventory.slice';

const SaleInformationFooter = ({posDetail, allowCheckout}) => {
  const {navigate} = useNavigation();
  const avatar = useSelector(AccountSelectors.avatar);
  const view = useSelector(AccountSelectors.view);
  const dispatch = useDispatch();

  return ['branch.manager', 'branch.owner'].indexOf(view) > -1 ? (
    <></>
  ) : (
    <View
      style={{
        padding: 10,
      }}>
      <TouchableOpacity
        onPress={() => {
          if (allowCheckout) {
            navigate('CheckoutScreen', {
              checkinId: posDetail?.latest_visit?.id,
              posCode: posDetail.posCode,
            });
          } else {
            dispatch(changeMode(1));
            navigate('CheckInLocationScreen', {
              ...posDetail,
            });
          }
        }}
        style={{
          borderRadius: 50,
          backgroundColor: allowCheckout ? '#EF7721' : '#0062FF',
          padding: 7,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <Image
          style={{
            height: 40,
            width: 40,
            borderRadius: 50,
          }}
          source={{uri: avatar}}
        />

        <View
          style={{
            flex: 1,
          }}>
          <Sys.Text
            style={{
              fontSize: 16,
              color: 'white',
              fontWeight: '600',
              lineHeight: 26,
              textAlign: 'center',
            }}>
            Ghé thăm ĐBH
            {allowCheckout
              ? ' - Yêu cầu rời ĐBH'
              : posDetail?.plan_current_approved
              ? posDetail?.checked_in_planned
                ? ' - Đã ghé thăm'
                : ' - Chưa ghé thăm'
              : ' - Ngoài kế hoạch'}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 20,
          }}>
          <FooterRightIcon />
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default SaleInformationFooter;
