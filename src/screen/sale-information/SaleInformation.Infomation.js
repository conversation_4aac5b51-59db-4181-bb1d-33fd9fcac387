import React, {useState} from 'react';
import {
  Image,
  Linking,
  Platform,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import Sys from '../../components/Sys';
import PlusIcon from './icons/PlusIcon';
import TelephoneIcon from './icons/TelephoneIcon';
import ReactNativeModal from 'react-native-modal';
import {useDispatch, useSelector} from 'react-redux';
import {PosActions, PosSelectors} from '../sale-location/services/pos.slice';
import Toast from 'react-native-toast-message';
import {Calendar} from 'react-native-calendars';
import moment from 'moment';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const SaleInformationInfomation = ({posDetail, onReload}) => {
  const insets = useSafeAreaInsets();
  const [isShow, setIsShow] = useState(false);
  const posList = useSelector(PosSelectors.posList);
  const {width} = useWindowDimensions();
  const dispatch = useDispatch();
  const onCreatePlan = date => {
    dispatch(
      PosActions.planCreate({
        onSuccess: rs => {
          Toast.show({
            type: 'success',
            text1: 'Thông báo',
            text2: 'Thêm kế hoạch thành công',
          });
          setIsShow(false);
          onReload();
          dispatch(
            PosActions.setPos(
              posList.map(x => {
                if (x.posCode === posDetail?.data?.posCode) {
                  return {
                    ...x,
                    allowAddPlan: false,
                  };
                } else {
                  return x;
                }
              }),
            ),
          );
        },
        onFail: rs => {
          setIsShow(false);
          Toast.show({
            type: 'success',
            text1: 'Thông báo',
            text2: rs?.error?.message,
          });
        },
        posCode: posDetail?.posCode,
        date: moment(date, 'YYYY-MM-DD'),
      }),
    );
  };
  const styles = StyleSheet.create({
    mainContainer: {
      zIndex: 5,
    },
    informationContainerView: {
      flexDirection: 'row',
      paddingHorizontal: 10,
    },
    informationContainer: {
      position: 'absolute',
      top: -40,
      width: '100%',
      zIndex: 3,
    },
    container: {
      flex: 1,
    },
    avatar: {
      width: 80,
      height: 80,
      borderWidth: 3,
      borderColor: 'white',
      borderRadius: 20,
      backgroundColor: 'white',
      zIndex: 4,
    },
    viewLinea: {
      width: '100%',
      height: 230,
      position: 'absolute',
      zIndex: 2,
      top: 0,
    },
    avatarContainer: {
      flex: 0,
      marginRight: 10,
    },
    address: {
      color: 'white',
      fontSize: 12,
      lineHeight: 16,
    },
    phoneNumber: {
      color: 'white',
      fontSize: 12,
      lineHeight: 16,
      fontWeight: '700',
    },
  });

  return (
    <View>
      <ReactNativeModal
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={{
          padding: 0,
          margin: 0,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onBackdropPress={() => setIsShow(false)}
        isVisible={isShow}
        onSwipeComplete={() => {
          setIsShow(false);
        }}
        swipeDirection="down">
        <View
          style={{
            flex: 1,
          }}
        />
        <View
          style={{
            backgroundColor: 'white',
            width: width,
            borderRadius: 20,
            padding: 20,
            paddingBottom: insets.bottom + 20,
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
            }}>
            Thêm vào kế hoạch
          </Sys.Text>
          <Calendar
            hideArrows
            initialDate={moment()
              .add('M', 1)
              .startOf('month')
              .format('YYYY-MM-DD')}
            minDate={moment().add('M', 1).startOf('month').format('YYYY-MM-DD')}
            maxDate={moment().add('M', 1).endOf('month').format('YYYY-MM-DD')}
            onDayPress={day => {
              onCreatePlan(day?.dateString);
            }}
          />
        </View>
      </ReactNativeModal>
      <View style={styles.informationContainer}>
        <View style={styles.informationContainerView}>
          <View style={styles.avatarContainer}>
            <Image
              style={styles.avatar}
              source={{uri: posDetail?.owner?.avatar}}
            />
          </View>
          <View
            style={{
              flex: 1,
              height: 80,
            }}>
            <View style={{height: 40}}>
              <Sys.Text style={styles.address}>
                {posDetail?.address}
                {posDetail?.posCode ? ' - ' : ''}
                <Sys.Text style={styles.phoneNumber}>
                  {posDetail?.posCode}
                </Sys.Text>
              </Sys.Text>
            </View>
            <View
              style={{
                justifyContent: 'space-between',
                height: 40,
                flexDirection: 'row',
                alignItems: 'flex-end',
              }}>
              {posDetail?.owner?.phone ? (
                <TouchableOpacity
                  onPress={() => {
                    let url;
                    if (Platform.OS === 'android') {
                      url = `tel:${posDetail?.owner?.phone}`;
                    } else {
                      url = `telprompt:${posDetail?.owner?.phone}`;
                    }

                    Linking.openURL(url).catch(err =>
                      console.error('Failed to open URL:', err),
                    );
                  }}
                  style={{
                    backgroundColor: '#D30B0D',
                    height: 30,
                    justifyContent: 'center',
                    width: 110,
                    borderRadius: 50,
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}>
                  <TelephoneIcon />
                  <Sys.Text
                    style={{
                      marginLeft: 10,
                      color: 'white',
                      fontSize: 12,
                    }}>
                    Gọi điện
                  </Sys.Text>
                </TouchableOpacity>
              ) : (
                <View
                  style={{
                    height: 30,
                    justifyContent: 'center',
                    width: 110,
                    borderRadius: 50,
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}
                />
              )}
              {posDetail?.allowAddPlan ? (
                <TouchableOpacity
                  onPress={() => setIsShow(true)}
                  style={{
                    backgroundColor: '#90BB3B',
                    height: 30,
                    justifyContent: 'center',
                    width: 110,
                    borderRadius: 50,
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}>
                  <PlusIcon />
                  <Sys.Text
                    style={{
                      marginLeft: 10,
                      color: 'white',
                      fontSize: 12,
                    }}>
                    Kế hoạch
                  </Sys.Text>
                </TouchableOpacity>
              ) : (
                <></>
              )}
            </View>
            <View />
          </View>
        </View>
      </View>
      <View style={{height: 56}} />
    </View>
  );
};

export default SaleInformationInfomation;
