import {useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import React, {useCallback, useEffect} from 'react';
import {
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MapView, {Marker, PROVIDER_GOOGLE} from 'react-native-maps';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import SaleInformationChartTicket from './SaleInformation.ChartTicket';
import SaleInformationDetail from './SaleInformation.Detail';
import SaleInformationDeviceList from './SaleInformation.DeviceList';
import SaleInformationFeatures from './SaleInformation.Features';
import SaleInformationFooter from './SaleInformation.Footer';
import SaleInformationHistory from './SaleInformation.History';
import SaleInformationInfomation from './SaleInformation.Infomation';
import SaleInformationRevenue from './SaleInformation.Revenue';
import GoMapIcon from './icons/GoMapIcon';
import {openGoogleMaps} from '../checkout/Checkout.Infomation';
import SaleInformationSkeleton from './SaleInformationSkeleton';
import {fetchInitPosDetailStart} from '../device-list/services/inventory.slice';

const SaleInformationScreen = props => {
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const {navigate, ...navigation} = useNavigation();

  const {
    isLoading,
    pos: posDetail,
    allowCheckout,
    total,
  } = useSelector(state => state.inventory);
  const {width} = useWindowDimensions();
  const {params} = useRoute();
  useEffect(() => {
    if (params?.posCode && isFocused) {
      getData(params?.posCode);
    }
  }, [params?.posCode, isFocused]);

  const getData = useCallback(
    posCode => {
      dispatch(fetchInitPosDetailStart({posCode}));
    },
    [params?.posCode],
  );
  const styles = StyleSheet.create({
    mainContainer: {
      zIndex: 5,
    },

    container: {
      flex: 1,
    },

    viewLinea: {
      width: '100%',
      height: 230,
      position: 'absolute',
      zIndex: 2,
      top: 0,
    },
  });

  const handleBack = () => {
    if (params?.resetNavigation || !navigation.canGoBack()) {
      navigation.reset({
        index: 0,
        routes: [{name: 'DashboardScreen'}],
      });
    } else {
      navigation.goBack();
    }
  };

  const onReload = () => {
    getData(params?.posCode);
  };

  return (
    <Sys.Container hasHeader>
      <Sys.Header
        onBack={handleBack}
        title={'Thông tin ĐBH'}
        right={
          <View style={{marginRight: 10}}>
            <TouchableOpacity
              onPress={() => {
                openGoogleMaps(posDetail?.lat, posDetail?.lon);
              }}
              style={{
                backgroundColor: 'white',
                borderRadius: 8,
                flexDirection: 'row',
                paddingVertical: 4,
                paddingHorizontal: 10,
                alignItems: 'center',
              }}>
              <GoMapIcon />
              <Sys.Text
                style={{
                  fontSize: 12,
                  color: '#0062FF',
                  paddingLeft: 5,
                }}>
                Chỉ đường
              </Sys.Text>
            </TouchableOpacity>
          </View>
        }
      />
      {isLoading ? (
        <SaleInformationSkeleton />
      ) : (
        <View style={styles.container}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={styles.container}>
            <LinearGradient
              colors={['#00000000', '#00000000', '#00000090']}
              style={styles.viewLinea}
            />
            {posDetail?.lat && posDetail?.lon ? (
              <MapView
                scrollEnabled={false}
                provider={PROVIDER_GOOGLE}
                style={{
                  width,
                  height: 230,
                }}
                region={{
                  latitude: posDetail?.lat,
                  longitude: posDetail?.lon,
                  latitudeDelta: 0.03,
                  longitudeDelta: 0.024,
                }}>
                {posDetail?.lat && posDetail?.lon ? (
                  <Marker
                    coordinate={{
                      latitude: posDetail?.lat,
                      longitude: posDetail?.lon,
                    }}
                  />
                ) : undefined}
              </MapView>
            ) : undefined}
            <View style={styles.mainContainer}>
              <SaleInformationInfomation
                onReload={onReload}
                posDetail={posDetail}
                preScreen={params}
              />
              <SaleInformationFeatures posCode={posDetail?.posCode} />
              <SaleInformationDetail posDetail={posDetail} preScreen={params} />
              <SaleInformationRevenue posDetail={posDetail} />
              <SaleInformationChartTicket posDetail={posDetail} />
              <SaleInformationDeviceList total={total} />
              <SaleInformationHistory posDetail={posDetail} />
            </View>
          </ScrollView>
          <SaleInformationFooter
            posDetail={posDetail}
            allowCheckout={allowCheckout}
          />
        </View>
      )}
    </Sys.Container>
  );
};

export default SaleInformationScreen;
