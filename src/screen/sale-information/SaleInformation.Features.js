import {TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import Sys from '../../components/Sys';
import VPMIcon from './icons/VPMIcon';
import BrokenIcon from './icons/BrokenIcon';
import TaskIcon from './icons/TaskIcon';
import MenuIcon from './icons/MenuIcon';
import {useNavigation} from '@react-navigation/native';
import SysFetch from '../../services/fetch';

const SaleInformationFeatures = ({posCode}) => {
  const {navigate} = useNavigation();
  const [data, setData] = useState({
    surveys: {},
    marketing: {},
    tickets: {},
    audits: {},
  });

  useEffect(() => {
    if (posCode) {
      fetchData();
    }
  }, [posCode]);

  const fetchData = async () => {
    try {
      const [
        surveyResponse,
        marketingResponse,
        ticketsResponse,
        auditsResponse,
      ] = await Promise.all([
        SysFetch.get('survey/getActive', {pos: posCode}),
        SysFetch.get('marketing/getMarketing', {pos: posCode}),
        SysFetch.get('support/tickets', {pos: posCode}),
        SysFetch.get('inventory-plan/getActive', {pos: posCode}),
      ]);
      setData({
        surveys: surveyResponse,
        marketing: marketingResponse,
        tickets: ticketsResponse,
        audits: auditsResponse,
      });
    } catch (error) {
      console.error('Failed to fetch data:', error);
    }
  };
  const menuList = [
    {
      name: 'Kiểm kê TTB',
      icon: <MenuIcon />,
      badge: data.audits?.not_completed,
      onPress: () => {
        navigate('EIPScreen');
      },
    },
    {
      name: 'Khảo sát',
      icon: <TaskIcon />,
      badge: data.surveys?.not_completed,
      onPress: () => {
        navigate('SurveyScreen', {
          pos_id: posCode,
        });
      },
    },
    {
      name: 'VP Marketing',
      icon: <BrokenIcon />,
      badge: data.marketing?.not_completed,
      onPress: () => {
        navigate('MarketingScreen', {
          pos_id: posCode,
        });
      },
    },
    {
      name: 'Phản ánh',
      icon: <VPMIcon />,
      badge: data.tickets?.meta?.total,
      onPress: () => {
        navigate('MessageSupportScreen', {
          posCode,
        });
      },
    },
  ];

  return (
    <View
      style={{
        padding: 10,
        backgroundColor: 'white',
        marginHorizontal: 10,
        borderRadius: 12,
      }}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-around',
        }}>
        {menuList.map((item, index) => {
          return (
            <View key={index} style={{flex: 1, alignItems: 'center'}}>
              <TouchableOpacity
                onPress={item?.onPress}
                style={{
                  height: 44,
                  width: 44,
                  backgroundColor: '#0062FF',
                  borderRadius: 50,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                {Number(item.badge) > 0 ? (
                  <View
                    style={{
                      backgroundColor: '#F5000D',
                      height: 20,
                      width: 20,
                      position: 'absolute',
                      right: -5,
                      top: -5,
                      borderRadius: 50,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <Sys.Text style={{color: 'white', fontWeight: '500'}}>
                      {item.badge}
                    </Sys.Text>
                  </View>
                ) : (
                  <></>
                )}
                {item.icon}
              </TouchableOpacity>
              <View
                style={{
                  marginTop: 4,
                }}>
                <Sys.Text
                  style={{
                    color: '#0062FF',
                    fontSize: 12,
                    fontWeight: '500',
                    lineHeight: 20,
                  }}>
                  {item.name}
                </Sys.Text>
              </View>
            </View>
          );
        })}
      </View>
    </View>
  );
};

export default SaleInformationFeatures;
