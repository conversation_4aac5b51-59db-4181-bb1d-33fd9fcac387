import React, {useEffect, useState} from 'react';
import {FlatList, StyleSheet, View} from 'react-native';
import Sys from '../../components/Sys';
import SysFetch from '../../services/fetch';
import OneItemHistory from './OneItem.History';

const CompleteHistoryScreen = ({route}) => {
  const {posDetail} = route.params;
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    const fetchHistory = async () => {
      if (posDetail?.posCode) {
        setLoadingMore(true);
        try {
          const rs = await SysFetch.get(
            `pos/${posDetail.posCode}/visit-history`,
            {
              sort_order: 'desc',
              sort_by: 'created_at',
              page: currentPage,
            },
          );
          setData(prevData => [...prevData, ...rs.data]);
          setTotalPages(rs.meta.last_page);
        } catch (error) {
          console.error('Failed to fetch history:', error);
        } finally {
          setLoadingMore(false);
        }
      }
    };

    fetchHistory();
  }, [posDetail, currentPage]);

  const loadMoreItems = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const renderItem = ({item}) => (
    <OneItemHistory item={item} posDetail={posDetail} />
  );

  return (
    <Sys.Container hasHeader>
      <Sys.Header title={'Lịch sử ghé thăm'} />
      <View style={styles.container}>
        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={(item, index) => index.toString()}
          initialNumToRender={10}
          onEndReached={loadMoreItems}
          onEndReachedThreshold={0.5}
          ListFooterComponent={() =>
            loadingMore && <Sys.Text>Loading...</Sys.Text>
          }
        />
      </View>
    </Sys.Container>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 20,
    paddingVertical: 15,
    marginHorizontal: 10,
    marginTop: 15,
    paddingHorizontal: 10,
  },
  textHeader: {
    fontSize: 18,
    fontWeight: 'bold',
    lineHeight: 28,
    marginBottom: 10,
  },
});

export default CompleteHistoryScreen;
