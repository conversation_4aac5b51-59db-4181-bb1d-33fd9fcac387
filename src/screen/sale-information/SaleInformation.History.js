import {useNavigation} from '@react-navigation/native';
import React, {useCallback, useEffect, useState} from 'react';
import {FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import {useDispatch} from 'react-redux';
import Sys from '../../components/Sys';
import {isAndroid} from '../../services/util';
import SysFetch from '../../services/fetch';
import OneItemHistory from './OneItem.History';

const SaleInformationHistory = ({posDetail}) => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const [data, setData] = useState([]);
  const [totalPages, setTotalPages] = useState(1);
  const pageSize = 3;
  useEffect(() => {
    const fetchHistory = async () => {
      if (posDetail?.posCode) {
        try {
          const rs = await SysFetch.get(
            `pos/${posDetail.posCode}/visit-history`,
            {
              sort_order: 'desc',
              sort_by: 'created_at',
              page: 1,
              pageSize,
            },
          );
          setData(prevData => [...prevData, ...rs.data]);
          // console.log(pageSize, rs.meta);
          setTotalPages(rs.meta.last_page);
        } catch (error) {
          console.error('Failed to fetch history:', error);
        } finally {
        }
      }
    };

    fetchHistory();
  }, [posDetail, dispatch]);

  const renderItem = useCallback(
    ({item}) => <OneItemHistory item={item} posDetail={posDetail} />,
    [posDetail],
  );

  const keyExtractor = useCallback((item, index) => index.toString(), []);

  const handleViewMore = () => {
    navigation.navigate('CompleteHistoryScreen', {posDetail});
  };

  return (
    <View style={styles.container}>
      <Sys.Text style={styles.textHeader}>Lịch sử ghé thăm điểm bán</Sys.Text>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        initialNumToRender={10}
        ListFooterComponent={() =>
          totalPages > 1 && (
            <TouchableOpacity
              onPress={handleViewMore}
              style={styles.loadMoreButton}>
              <Sys.Text
                style={{
                  fontSize: 16,
                  textTransform: 'uppercase',
                  color: '#0062FF',
                }}>
                Xem thêm
              </Sys.Text>
            </TouchableOpacity>
          )
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 20,
    paddingVertical: 15,
    marginHorizontal: 10,
    marginTop: 15,
    paddingHorizontal: 10,
  },
  textHeader: {
    fontSize: 18,
    fontWeight: isAndroid ? 'bold' : '600',
    lineHeight: 28,
    marginBottom: 10,
  },
  loadMoreButton: {
    padding: 10,
    alignItems: 'center',
    borderRadius: 5,
  },
});

export default SaleInformationHistory;
