import {useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import Sys from '../../components/Sys';
import {formatDistance} from '../../services/util';

const OneItemHistory = ({item, posDetail}) => {
  const [coord, setCoord] = useState(undefined);
  const {navigate} = useNavigation();

  useEffect(() => {
    try {
      const coordinates = item?.checkin_details?.data?.config.filter(
        x => x.type === 'coordinates',
      )[0];
      setCoord(coordinates);
    } catch (error) {}
  }, [item]);

  return (
    <TouchableOpacity
      onPress={() => {
        navigate('CheckoutScreen', {
          posDetail,
          posCode: posDetail?.posCode,
          checkinId: item.id,
        });
      }}
      style={styles.itemContainer}>
      <View style={styles.itemRow}>
        <View>
          <Image style={styles.image} source={{uri: item?.banner}} />
        </View>
        <View style={styles.itemContent}>
          <View style={styles.itemLine}>
            <Sys.Text style={styles.staffName}>
              {item?.staff?.name || null}
            </Sys.Text>
            <View style={styles.actionLabelContainer}>
              <View
                style={[
                  styles.actionLabel,
                  item.action_code === 1
                    ? {backgroundColor: '#3DD59810'}
                    : {backgroundColor: 'rgba(190,131,88,0.4)'},
                ]}>
                <Sys.Text
                  style={[
                    styles.actionLabelText,
                    item.action_code === 1
                      ? {color: '#3DD598'}
                      : {color: '#EF7721'},
                  ]}>
                  {item?.action_label}
                </Sys.Text>
              </View>
            </View>
          </View>
          <View style={styles.itemLine}>
            <Sys.Text style={styles.timestamp}>
              {moment(item?.created_at).format('DD-MM-YYYY HH:mm')}
            </Sys.Text>
            <Sys.Text
              style={{
                color:
                  coord?.minimum_distance < item?.checkin_details?.distance
                    ? '#F5000D'
                    : 'black',
              }}>
              Cách ĐBH {formatDistance(item?.checkin_details?.distance)}
            </Sys.Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    marginBottom: 5,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContent: {
    flex: 1,
  },
  itemLine: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  staffName: {
    fontWeight: '500',
    lineHeight: 24,
    color: '#171725',
  },
  actionLabelContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 10,
  },
  actionLabel: {
    backgroundColor: '#3DD59810',
    borderRadius: 5,
    paddingHorizontal: 15,
    paddingVertical: 5,
  },
  actionLabelText: {
    color: '#3DD598',
  },
  timestamp: {
    color: '#696974',
  },
  image: {
    height: 90,
    width: 90,
    borderRadius: 10,
    marginRight: 15,
  },
});

export default React.memo(OneItemHistory);
