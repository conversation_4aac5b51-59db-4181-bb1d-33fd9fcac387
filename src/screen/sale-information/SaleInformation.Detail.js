import {FlatList, View} from 'react-native';
import React from 'react';
import Sys from '../../components/Sys';
import {formatDate, formatVND} from '../../services/util';

const SaleInformationDetail = ({posDetail, preScreen}) => {
  const data = [
    {
      field: 'Tên chủ điếm:',
      value: posDetail?.owner?.name,
    },
    {
      field: 'Điện thoại:',
      value: posDetail?.owner?.phone,
    },
    {
      field: 'Trạng thái:',
      value: posDetail?.posStatusName,
    },
    {
      field: '<PERSON><PERSON><PERSON> bắt đầu hoạt động:',
      value: formatDate(posDetail?.start_date),
    },
    {
      field: 'Thời điểm ghé thăm gần nhất:',
      value: formatDate(posDetail?.last_checkin, true),
    },
    {
      field: '<PERSON>ạn mức đầu ngày:',
      value: `${formatVND(posDetail?.start_threshold_amount)} VNĐ`,
    },
    {
      field: '<PERSON><PERSON><PERSON> điểm bán:',
      value: posDetail?.posType,
    },
    {
      field: '<PERSON>yên viên phụ trách:',
      value: posDetail?.staff?.name || null,
    },
    {
      field: 'Tỉ lệ hoa hồng:',
      value: posDetail?.commission_rate ? `${posDetail?.commission_rate}%` : '',
    },
    {
      field: 'Đại lý:',
      value: preScreen?.agency?.name,
    },
  ];

  return (
    <View
      style={{
        marginTop: 15,
        backgroundColor: 'white',
        marginHorizontal: 10,
        paddingHorizontal: 10,
        paddingVertical: 10,
        borderRadius: 20,
      }}>
      <FlatList
        scrollEnabled={false}
        data={data}
        keyExtractor={(item, index) => index}
        renderItem={({item, index}) => {
          return item?.value ? (
            <View
              style={{
                flexDirection: 'row',
                marginVertical: 5,
                justifyContent: 'space-between',
              }}>
              <View style={{flex: 1}}>
                <Sys.Text
                  style={{color: '#1E2022', lineHeight: 24, fontWeight: '500'}}>
                  {item.field}
                </Sys.Text>
              </View>
              <View style={{flex: 1}}>
                <Sys.Text
                  style={{
                    textAlign: 'right',
                    color: '#001451',
                    lineHeight: 20,
                    fontWeight: '600',
                  }}>
                  {item.value}
                </Sys.Text>
              </View>
            </View>
          ) : (
            <></>
          );
        }}
      />
    </View>
  );
};

export default SaleInformationDetail;
