import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import Sys from '../../components/Sys';
import RightIcon from './icons/RightIcon';
import {useNavigation} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {changeMode} from '../device-list/services/inventory.slice';
import {isAndroid} from '../../services/util';

const SaleInformationDeviceList = ({total}) => {
  const {navigate, ...navigation} = useNavigation();
  const dispatch = useDispatch();

  return (
    <TouchableOpacity
      onPress={() => {
        dispatch(changeMode(2));
        navigate('DeviceListScreen');
      }}
      style={{
        backgroundColor: 'white',
        marginTop: 10,
        marginHorizontal: 15,
        borderRadius: 20,
        paddingHorizontal: 15,
        paddingVertical: 15,
        flexDirection: 'row',
        alignItems: 'center',
      }}>
      <View
        style={{
          flex: 1,
        }}>
        <Sys.Text
          style={{
            color: '#0062FF',
            fontSize: 18,
            fontWeight: isAndroid ? 'bold' : 600,
          }}>
          <PERSON>h sách trang thiết bị
        </Sys.Text>
      </View>
      <View
        style={{
          marginRight: 20,
          width: 32,
          height: 32,
          backgroundColor: '#EF7721',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: 50,
        }}>
        <Sys.Text
          style={{
            color: 'white',
            fontWeight: '600',
            fontSize: 16,
          }}>
          {total}
        </Sys.Text>
      </View>
      <RightIcon />
    </TouchableOpacity>
  );
};

export default SaleInformationDeviceList;
