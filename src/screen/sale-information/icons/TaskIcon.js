import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const TaskIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}>
    <Path
      fill="#fff"
      fillRule="evenodd"
      d="M6 2h12a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V6a4 4 0 0 1 4-4Zm0 2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H6Zm1.707 2.293L8 6.586l1.293-1.293a1 1 0 0 1 1.414 1.414l-2 2a1 1 0 0 1-1.414 0l-1-1a1 1 0 0 1 1.414-1.414ZM13 8A1 1 0 0 1 13 6l3.982.005a1 1 0 0 1-.003 2L13 8Zm0 5A1 1 0 0 1 13 11l3.982.005a1 1 0 0 1-.003 2L13 13Zm0 5A1 1 0 0 1 13 16l3.982.005a1 1 0 0 1-.003 2L13 18Zm-5.292-6.707.293.293 1.293-1.293a1 1 0 1 1 1.414 1.414l-2 2a1 1 0 0 1-1.414 0l-1-1a1 1 0 1 1 1.414-1.414Zm0 5 .293.293 1.293-1.293a1 1 0 0 1 1.414 1.414l-2 2a1 1 0 0 1-1.414 0l-1-1a1 1 0 1 1 1.414-1.414Z"
      clipRule="evenodd"
    />
  </Svg>
);
export default TaskIcon;
