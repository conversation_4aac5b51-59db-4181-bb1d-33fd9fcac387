import {useNavigation, useRoute} from '@react-navigation/native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  Alert,
  Image,
  Linking,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import * as ANT from 'react-native-animatable';
import Modal from 'react-native-modal';
import {check, PERMISSIONS, request, RESULTS} from 'react-native-permissions';
import {
  Camera,
  useCameraDevice,
  useCameraFormat,
} from 'react-native-vision-camera';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import CameraButtonIcon from './icons/CameraButtonIcon';
import ChangeCameraIcon from './icons/ChangeCameraIcon';
import RemoveIcon from './icons/RemoveIcon';
import RightIcon from './icons/RightIcon';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import moment from 'moment';
import {
  formatVND,
  getCameraHeight,
  isIOS,
  resizeAndCompressImage,
} from '../../services/util';
import {PosActions} from '../sale-location/services/pos.slice';
import {Dropdown} from 'react-native-element-dropdown';
import SelectIcon from '../sale-location/icons/SelectIcon';
import SysFetch from '../../services/fetch';
import {AppActions, AppSelectors} from '../../app.slice';
import Toast from 'react-native-toast-message';
import {setCheckinAt} from '../device-list/services/inventory.slice';
import {useSharedValue} from 'react-native-reanimated';
import Color from '../../components/theme/Color';

const CheckCloseScreen = () => {
  const dispatch = useDispatch();
  const {params} = useRoute();
  const insets = useSafeAreaInsets();
  const {store_close_config, lat, lon, pos} = params;
  const [cameraPosition, setCameraPosition] = useState('back');
  const [isShow, setIsShow] = useState(true);
  const [isShowConform, setIsShowConform] = useState(false);
  const [action, setAction] = useState('fadeInRight');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [reasonList, setReasonList] = useState([]);
  const [currentReason, setCurrentReason] = useState(undefined);
  const [isActive, setIsActive] = useState(true);
  const [images, setImages] = useState(
    store_close_config
      .filter(x => x.type === 'file')
      .map(x => ({
        key: x.key,
        max: x.max_photos,
        size: x.rules.max,
        max_width: x.rules.max_width,
        max_height: x.rules.max_height,
        photo: [],
      })),
  );
  const camera = useRef(null);
  const {navigate, goBack, ...navigation} = useNavigation();

  const zoom = useSharedValue(device?.minZoom || 1);

  const device = useCameraDevice(cameraPosition, {
    physicalDevices: [
      'ultra-wide-angle-camera',
      'wide-angle-camera',
      'telephoto-camera',
    ],
  });

  const {image_rules} = useSelector(AppSelectors.settings);
  const {checkin_time_at, plan_id} = useSelector(state => state.inventory);

  const resolutionSettings = Platform.select({
    ios: {
      videoResolution: {
        width: image_rules?.max_width || 1920,
        height: image_rules?.max_height || 1080,
      },
    },
    android: {
      photoResolution: {
        width: image_rules?.max_width || 1920,
        height: image_rules?.max_height || 1080,
      },
    },
  });

  const cameraProps = Platform.select({
    ios: {video: true},
    android: {photo: true},
  });

  const format = useCameraFormat(device, [resolutionSettings]);

  useEffect(() => {
    const checkin_time_at = moment().format('YYYY-MM-DD HH:mm:ss');
    dispatch(setCheckinAt(checkin_time_at));

    checkPermissionCamera();
    getReason();
  }, []);

  useEffect(() => {
    if (isShow) {
      setIsShow(false);
    }
  }, [currentIndex]);

  useEffect(() => {
    if (!isShow) {
      setTimeout(() => {
        setIsShow(true);
      }, 300);
    }
  }, [isShow]);

  const getReason = () => {
    dispatch(
      PosActions.getReason({
        onSuccess: rs => {
          if (rs?.data?.length > 0) {
            setCurrentReason(rs?.data[0]);
            setReasonList(rs.data);
          }
        },
      }),
    );
  };

  const checkPermissionCamera = async () => {
    const cmrPer = await check(PERMISSIONS.IOS.CAMERA);
    if (cmrPer !== RESULTS.GRANTED) {
      const reqPer = await request(PERMISSIONS.IOS.CAMERA);
      if (reqPer === RESULTS.BLOCKED) {
        Alert.alert('Thông báo', 'Hãy cấp quyền camera cho ứng dụng', [
          {
            text: 'Đóng',
            onPress: goBack,
          },
          {
            text: 'Tới cài đặt',
            onPress: () => {
              Linking.openSettings();
              goBack();
            },
          },
        ]);
      }
    }
  };

  const onTakePhoto = useCallback(async () => {
    try {
      const photo = await camera.current.takeSnapshot({
        quality: image_rules.quality || 60,
      });
      if (photo && photo.path) {
        const uri = `file://${photo.path}`;
        const currentImageConfig = images[currentIndex];
        let result = {uri};
        const response = await fetch(uri);
        const blob = await response.blob();
        const fileSizeBytes = blob.size;
        const fileSizeKilobytes = fileSizeBytes / 1024;

        console.log(`Original file size: ${fileSizeKilobytes.toFixed(2)} KB`);

        if (
          (currentImageConfig && currentImageConfig.size < fileSizeKilobytes) ||
          image_rules.isWebp ||
          isIOS
        ) {
          result = await resizeAndCompressImage(
            uri,
            currentImageConfig?.max_width || 1920,
            currentImageConfig?.max_height || 1080,
            currentImageConfig.quality || 60,
            image_rules.isWebp,
            photo.orientation,
          );

          const newUri = result.uri;
          const newResponse = await fetch(newUri);
          const newBlob = await newResponse.blob();
          const newFileSizeBytes = newBlob.size;
          const newFileSizeKilobytes = newFileSizeBytes / 1024;

          console.log(
            `Resized file size: ${newFileSizeKilobytes.toFixed(2)} KB`,
          );
        }
        if (currentImageConfig.photo.length < currentImageConfig.max) {
          currentImageConfig.photo.push(result);
          setImages(
            images.map((x, index) =>
              index === currentIndex ? currentImageConfig : x,
            ),
          );
        }
      }
    } catch (error) {
      console.error('Failed to take photo', error);
    }
  }, [currentIndex, images]);

  const onNext = useCallback(() => {
    if (images[currentIndex].photo.length > 0) {
      if (currentIndex + 1 < images.length) {
        setAction('fadeInRight');
        setCurrentIndex(currentIndex + 1);
      } else {
        setIsShowConform(true);
      }
    }
  }, [currentIndex, images]);

  const getDataFromObject = fieldName => {
    try {
      return store_close_config.filter(x => x.type === 'file')[currentIndex][
        fieldName
      ];
    } catch (error) {
      return '';
    }
  };

  const onRequestClose = async () => {
    try {
      dispatch(AppActions.setLoading(true));
      let formData = new FormData();

      images.map(({photo, key}, index) => {
        photo.forEach(item => {
          if (item && item.uri) {
            formData.append(`${key}[]`, {
              uri: item.uri,
              type: 'image/jpeg',
              name: `${key}-${index + 1}.jpg`,
            });
          }
        });
      });
      formData.append('plan_id', plan_id || '');

      formData.append('lat', lat);
      formData.append('lon', lon);
      formData.append('posCode', pos.posCode);
      formData.append('reason_id', currentReason?.id);
      formData.append('checkin_time_at', checkin_time_at);

      console.log(formData);
      const rs = await SysFetch.post('plan/store-close', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      if (rs.success) {
        Toast.show({
          type: 'success',
          text1: 'Thông báo',
          text2: 'Báo cáo đóng cửa thành công',
        });
        navigation.reset({
          index: 0,
          routes: [
            {
              name: 'SaleInformationScreen',
              params: {posCode: pos.posCode, resetNavigation: true},
            },
          ],
        });
      }
      dispatch(AppActions.setLoading(false));
    } catch (error) {
      console.log(error);
      console.log(error.response.data);
      dispatch(AppActions.setLoading(false));
    }
  };

  return (
    <Sys.Container
      hasHeader
      headerColor={'#848484'}
      footerColor={'#848484'}
      hasFooter
      backgroundColor={'#848484'}>
      <Modal
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={styles.modal}
        onBackdropPress={() => setIsShowConform(false)}
        isVisible={isShowConform}
        onSwipeComplete={() => setIsShow(false)}
        swipeDirection="down">
        <View style={{flex: 1}} />
        <View
          style={[
            styles.confirmModalContent,
            {paddingBottom: insets.bottom + 20},
          ]}>
          <Sys.Text style={styles.confirmTitle}>
            Xác nhận thông tin ghé thăm
          </Sys.Text>
          <View style={styles.confirmInfo}>
            <Sys.Text style={styles.confirmInfoText}>
              Mã điểm bán: {pos.posCode}
            </Sys.Text>
            <Sys.Text style={styles.confirmInfoText}>
              Thời gian: {moment().format('DD/MM/YYYY HH:mm:ss')}
            </Sys.Text>
            <Sys.Text style={styles.confirmInfoText}>
              Khoảng cách:{' '}
              <Sys.Text style={{fontWeight: '600'}}>
                {formatVND(params?.m)}m
              </Sys.Text>
            </Sys.Text>
            <Sys.Text style={styles.confirmInfoText}>
              Địa chỉ: {pos.address}
            </Sys.Text>
          </View>
          <Sys.Text style={styles.reasonLabel}>
            Lý do đóng cửa
            <Sys.Text style={{color: 'red'}}>(*):</Sys.Text>
          </Sys.Text>
          <Dropdown
            selectedTextStyle={styles.dropdownSelectedText}
            styles={styles.dropdownSelectedText}
            itemTextStyle={{
              color: Color.text,
            }}
            data={reasonList}
            maxHeight={100}
            mode="auto"
            labelField="name"
            valueField="id"
            search={false}
            value={currentReason?.id}
            onChange={item => setCurrentReason(item)}
            renderRightIcon={() => <SelectIcon />}
          />
          <View style={styles.divider} />
          <View style={styles.confirmButtonContainer}>
            <TouchableOpacity
              onPress={() => {
                setIsShowConform(false);
                onRequestClose();
              }}
              style={styles.confirmButton}>
              <Sys.Text style={styles.confirmButtonText}>XÁC NHẬN</Sys.Text>
              <RightIcon />
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <Sys.Header
        onBack={() => {
          if (currentIndex > 0) {
            setAction('fadeInLeft');
            setCurrentIndex(currentIndex - 1);
          } else {
            goBack();
          }
        }}
        color={'#00000000'}
        title={getDataFromObject('name')}
      />
      {isShow ? (
        <ANT.View
          animation={action}
          style={{flex: 1, backgroundColor: '#848484'}}>
          <View style={{flex: 1, paddingHorizontal: 20}}>
            <View style={styles.descriptionContainer}>
              <View style={styles.descriptionTextContainer}>
                <Sys.Text style={styles.descriptionText}>
                  {getDataFromObject('description')}
                </Sys.Text>
              </View>
              <Image
                source={{uri: getDataFromObject('illustration')}}
                style={styles.illustrationImage}
              />
            </View>
            <View style={styles.cameraContainer}>
              <Camera
                style={styles.camera}
                device={device}
                format={format}
                isActive={isActive}
                ref={camera}
                photoQualityBalance="speed"
                {...cameraProps}
                zoom={zoom.value}
                enableZoomGesture={true}
              />
            </View>
            <View style={styles.imagesContainer}>
              {images[currentIndex].photo.map((x, index) => (
                <OnePic
                  key={index}
                  item={x}
                  removeImage={() => {
                    setImages(
                      images.map((img, idx) =>
                        idx === currentIndex
                          ? {
                              ...img,
                              photo: img.photo.filter(p => p.uri !== x.uri),
                            }
                          : img,
                      ),
                    );
                  }}
                />
              ))}
            </View>
          </View>
          <View style={styles.controlsContainer}>
            <TouchableOpacity
              onPress={() =>
                setCameraPosition(cameraPosition === 'back' ? 'front' : 'back')
              }
              style={styles.changeCameraButton}>
              <ChangeCameraIcon />
            </TouchableOpacity>
            <TouchableOpacity onPress={onTakePhoto}>
              <CameraButtonIcon />
            </TouchableOpacity>
            <TouchableOpacity onPress={onNext} style={styles.nextButton}>
              <Sys.Text style={styles.nextButtonText}>Tiếp</Sys.Text>
              <RightIcon />
            </TouchableOpacity>
          </View>
        </ANT.View>
      ) : (
        <View style={{flex: 1, paddingHorizontal: 20}} />
      )}
    </Sys.Container>
  );
};

export default CheckCloseScreen;

const OnePic = ({item, removeImage}) => {
  const [isShow, setIsShow] = useState(false);

  return (
    <>
      <Modal
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={styles.modal}
        onBackdropPress={() => setIsShow(false)}
        isVisible={isShow}
        onSwipeComplete={() => setIsShow(false)}
        swipeDirection="down">
        <View style={styles.modalImageContainer}>
          <Image style={styles.modalImage} source={{uri: item?.uri}} />
        </View>
      </Modal>
      <TouchableOpacity
        onPress={() => setIsShow(true)}
        activeOpacity={0.8}
        style={styles.imageWrapper}>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={removeImage}
          style={styles.removeIconWrapper}>
          <RemoveIcon />
        </TouchableOpacity>
        <Image style={styles.image} source={{uri: item?.uri}} />
      </TouchableOpacity>
    </>
  );
};

const styles = StyleSheet.create({
  modal: {
    padding: 0,
    margin: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmModalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 20,
    width: '100%',
  },
  confirmTitle: {
    color: '#001451',
    fontSize: 18,
    fontWeight: '600',
  },
  confirmInfo: {
    marginVertical: 15,
  },
  confirmInfoText: {
    color: '#001451',
    fontWeight: '600',
    marginBottom: 5,
  },
  reasonLabel: {
    fontSize: 12,
    color: '#001451',
  },
  dropdownSelectedText: {
    fontWeight: '600',
    color: '#001451',
  },
  divider: {
    height: 1,
    marginBottom: 30,
    backgroundColor: '#E2E2EA',
  },
  confirmButtonContainer: {
    flexDirection: 'row',
    marginTop: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmButton: {
    height: 50,
    backgroundColor: '#F5000D',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  confirmButtonText: {
    color: 'white',
    fontWeight: '700',
    fontSize: 18,
    marginHorizontal: 40,
  },
  descriptionContainer: {
    marginTop: 5,
    backgroundColor: 'white',
    paddingHorizontal: 10,
    paddingVertical: 10,
    borderRadius: 20,
    flexDirection: 'row',
  },
  descriptionTextContainer: {
    flex: 1,
  },
  descriptionText: {
    lineHeight: 20,
    marginTop: 10,
    marginBottom: 10,
    color: '#001451',
  },
  illustrationImage: {
    flex: 0,
    borderWidth: 1,
    borderRadius: 10,
    borderColor: '#ccc',
    width: 60,
    height: 60,
  },
  cameraContainer: {
    marginTop: 15,
    alignItems: 'center',
    backgroundColor: 'black',
  },
  camera: {
    width: '100%',
    height: getCameraHeight(),
  },
  imagesContainer: {
    flexDirection: 'row',
    marginTop: 10,
    height: 100,
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
  },
  changeCameraButton: {
    width: 110,
  },
  nextButton: {
    flexDirection: 'row',
    backgroundColor: '#0062FF',
    height: 50,
    alignItems: 'center',
    borderRadius: 50,
    width: 110,
    justifyContent: 'center',
  },
  nextButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 20,
    lineHeight: 28,
    marginRight: 10,
  },
  modalImageContainer: {
    zIndex: 99,
  },
  modalImage: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  },
  imageWrapper: {
    marginRight: 20,
  },
  removeIconWrapper: {
    position: 'absolute',
    right: -10,
    top: -10,
    zIndex: 9,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 10,
  },
});
