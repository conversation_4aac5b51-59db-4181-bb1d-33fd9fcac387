import * as React from 'react';
import Svg, {Circle, Path} from 'react-native-svg';
const ChangeCameraIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={48}
    height={48}
    fill="none"
    {...props}>
    <Circle cx={24} cy={24} r={24} fill="#000" fillOpacity={0.35} />
    <Path
      fill="#fff"
      d="M35.56 28.86c-.781 2.459-2.633 4.554-5.19 5.872-2.556 1.317-5.63 1.762-8.617 1.246-2.986-.517-5.666-1.957-7.51-4.035-1.844-2.079-2.718-4.645-2.45-7.192l1.325.13c-.239 2.268.54 4.553 2.18 6.403 1.642 1.85 4.028 3.133 6.686 3.592 2.658.46 5.396.064 7.672-1.11 2.276-1.172 3.924-3.037 4.62-5.226l1.284.32Z"
    />
    <Path
      fill="#fff"
      d="m12.746 21.14 2.237 3.93-5.09-.421 2.853-3.51ZM12.115 19.14c.781-2.459 2.633-4.554 5.19-5.872 2.556-1.317 5.63-1.762 8.617-1.246 2.986.517 5.665 1.957 7.51 4.035 1.843 2.079 2.718 4.645 2.45 7.192l-1.325-.13c.239-2.268-.54-4.553-2.18-6.403-1.643-1.85-4.028-3.133-6.686-3.592-2.659-.46-5.396-.064-7.672 1.11-2.276 1.172-3.924 3.037-4.62 5.226l-1.284-.32Z"
    />
    <Path fill="#fff" d="m34.928 26.86-2.237-3.93 5.09.421-2.853 3.51Z" />
  </Svg>
);
export default ChangeCameraIcon;
