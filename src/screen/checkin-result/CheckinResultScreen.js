import React, {useEffect, useState} from 'react';
import {useWindowDimensions, View} from 'react-native';
import * as Progress from 'react-native-progress';
import Sys from '../../components/Sys';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import FormData from 'form-data';
import SysFetch from '../../services/fetch';
import {resetCheckin} from '../device-list/services/inventory.slice';
import Toast from 'react-native-toast-message';
import Color from '../../components/theme/Color';

const CheckinResultScreen = () => {
  const {navigate, ...navigation} = useNavigation();
  const {width} = useWindowDimensions();

  const [status, setStatus] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const dispatch = useDispatch();

  const {
    checklists,
    images,
    sections,
    pos,
    plan_id,
    lat,
    lon,
    mode,
    checkin_time_at,
  } = useSelector(state => state.inventory);

  useEffect(() => {
    requestCheckin();
  }, []);

  const requestCheckin = async () => {
    if (!loading) {
      setLoading(true);
      try {
        let formData = new FormData();

        // Add basic information to formData
        formData.append('plan_id', plan_id || '');
        formData.append('posCode', pos.posCode || '');
        formData.append('lat', lat);
        formData.append('lon', lon);
        formData.append('checkin_time_at', checkin_time_at);
        console.log('checkin_time_at end', checkin_time_at);
        // Add images to formData
        if (mode === 1) {
          images.forEach(({photo, key}, index) => {
            photo.forEach(item => {
              if (item && item.uri) {
                formData.append(`${key}[]`, {
                  uri: item.uri,
                  type: 'image/jpeg',
                  name: `${key}-${index + 1}.jpg`,
                });
              }
            });
          });

          // Add checklists to formData
          checklists.forEach((item, index) => {
            const itemCopy = {...item};
            delete itemCopy.photo;
            formData.append(
              `checklist[${itemCopy.id}]`,
              JSON.stringify(itemCopy),
            );

            if (item.photo && item.photo.uri) {
              formData.append(`checklist[${item.id}][photo]`, {
                uri: item.photo.uri,
                type: 'image/jpeg',
                name: `checklist_photo${item.id}.jpg`,
              });
            }
          });
        }

        // Add sections items to formData
        sections.forEach(section => {
          section.data.forEach(group => {
            group.items.forEach(item => {
              const itemCopy = {...item};

              delete itemCopy.photo;
              if (itemCopy.statusData) {
                delete itemCopy.statusData.photo;
              }

              formData.append(`items[${item.id}]`, JSON.stringify(itemCopy));

              if (item.photo && item.photo.uri) {
                formData.append(`items[${item.id}][photo]`, {
                  uri: item.photo.uri,
                  type: 'image/jpeg',
                  name: `item_photo${item.id}.jpg`,
                });
              }

              if (
                item.statusData &&
                item.statusData.photo &&
                item.statusData.photo.uri
              ) {
                formData.append(`items[${item.id}][statusData][photo]`, {
                  uri: item.statusData.photo.uri,
                  type: 'image/jpeg',
                  name: `item_status_photo${item.id}.jpg`,
                });
              }
            });
          });
        });

        const config = {
          headers: {
            'Content-Type': 'multipart/form-data',
            Accept: 'application/json',
          },
        };
        console.log('formData', {formData});
        const url = mode === 1 ? 'plan/checkin' : 'plan/checkin-inventory';
        const rs = await SysFetch.post(url, formData, config);
        if (rs?.success) {
          Toast.show({
            type: 'success',
            text1: mode === 1 ? 'Ghé thăm ĐBH' : 'Kiểm kê TTB',
            text2: 'Thành công',
          });
          navigation.reset({
            index: 0,
            routes: [
              {
                name: mode === 1 ? 'CheckoutScreen' : 'SaleInformationScreen',
                params: {
                  checkinId: rs?.record_id,
                  posCode: pos.posCode,
                  resetNavigation: true,
                },
              },
            ],
          });
          setStatus(true);
          dispatch(resetCheckin);
        } else {
          setErrorMessage(rs?.message || 'Unknown error occurred');
          setStatus(false);
        }
      } catch (error) {
        console.error(error);
        setErrorMessage(
          error.response?.data?.message ||
            error.message ||
            'Unknown error occurred',
        );
        setStatus(false);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <View style={{backgroundColor: 'white', overflow: 'hidden', flex: 1}}>
      <View
        style={{
          borderRadius: 1000,
          backgroundColor: '#EBF0F0',
          position: 'absolute',
          width: width * 2,
          height: width * 2,
          top: (-width * 4) / 3,
          left: -width / 2,
        }}
      />
      <View style={{alignItems: 'center'}}>
        <View
          style={{
            borderRadius: 130,
            width: 130,
            height: 130,
            backgroundColor: 'white',
            top: width / 2,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          {!loading ? (
            <View style={{position: 'absolute'}}>
              <Sys.Text>{status ? 'Thành công' : 'Thất bại'}</Sys.Text>
            </View>
          ) : (
            <Progress.Circle
              strokeCap="round"
              size={80}
              indeterminate={true}
              endAngle={0.2}
            />
          )}
        </View>
      </View>
      <View style={{top: width / 2}}>
        {loading ? (
          <View
            style={{
              marginTop: 30,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Sys.Text style={{fontSize: 24, fontWeight: '600', lineHeight: 34}}>
              Đang lưu
            </Sys.Text>
            <Sys.Text style={{fontWeight: '500', lineHeight: 24}}>
              Dữ liệu đang được xử lý vui lòng đợi
            </Sys.Text>
          </View>
        ) : (
          <>
            {status ? (
              <View style={{margin: 20}}>
                <Sys.Button onPress={() => navigate('CheckoutScreen')}>
                  Checkout
                </Sys.Button>
              </View>
            ) : (
              <>
                <Sys.Text style={{textAlign: 'center', marginBottom: 20}}>
                  {errorMessage}
                </Sys.Text>
                <View
                  style={{
                    margin: 20,
                    flex: 1,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <Sys.Button
                    style={{width: 150, height: 50}}
                    onPress={() =>
                      navigation.reset({
                        index: 0,
                        routes: [
                          {
                            name: 'CheckinResultScreen',
                          },
                        ],
                      })
                    }>
                    Thử lại
                  </Sys.Button>
                  <Sys.Button
                    style={{
                      width: 150,
                      height: 50,
                      backgroundColor: Color.gray,
                    }}
                    onPress={() =>
                      navigation.reset({
                        index: 0,
                        routes: [
                          {
                            name: 'SaleInformationScreen',
                            params: {
                              posCode: pos.posCode,
                              resetNavigation: true,
                            },
                          },
                        ],
                      })
                    }>
                    Hủy và quay lại
                  </Sys.Button>
                </View>
              </>
            )}
          </>
        )}
      </View>
    </View>
  );
};

export default CheckinResultScreen;
