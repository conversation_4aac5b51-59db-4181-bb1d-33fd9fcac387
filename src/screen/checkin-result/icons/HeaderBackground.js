import * as React from 'react';
import {useWindowDimensions} from 'react-native';
import Svg, {Path} from 'react-native-svg';
const HeaderBackground = props => {
  const {width, height} = useWindowDimensions();

  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={262}
      fill="none"
      {...props}>
      <Path
        fill="#EBF0F0"
        d="M0 0h390v231.202S271.719 262.122 120 262c-76.95-.121-120-30.798-120-30.798V0Z"
      />
    </Svg>
  );
};
export default HeaderBackground;
