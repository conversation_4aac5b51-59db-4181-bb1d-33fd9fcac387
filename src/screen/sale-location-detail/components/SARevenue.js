import {StyleSheet, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import THEME from '../../../components/theme/theme';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Sys from '../../../components/Sys';

const SARevenue = () => {
  const [isExpand, setIsExpand] = useState(false);

  const styles = StyleSheet.create({
    container: {
      borderWidth: 1,
      borderColor: '#d1d1d1',
      borderRadius: 8,
      marginBottom: 20,
      paddingVertical: 5,
    },
    title: {
      fontSize: 16,
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
      lineHeight: 26,
    },
    titleContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 10,
      paddingLeft: 15,
      paddingRight: 10,
    },
    contentView: {
      flexDirection: 'row',
      paddingHorizontal: 3,
    },
    oneView: {
      borderRadius: 8,
      alignContent: 'center',
      justifyContent: 'center',
      flex: 1,
      backgroundColor: '#F1F1F1',
      paddingVertical: 20,
      flexDirection: 'row',
      margin: 3,
    },
    label: {color: '#848484', lineHeight: 24, textAlign: 'center'},
    value: {
      color: '#001451',
      fontSize: 16,
      lineHeight: 26,
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
      textAlign: 'center',
    },
  });

  const onToggle = () => setIsExpand(!isExpand);

  return (
    <View style={styles.container}>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={onToggle}
        style={styles.titleContainer}>
        <Sys.Text style={styles.title}>Doanh thu</Sys.Text>
        <MaterialCommunityIcons
          name={isExpand ? 'chevron-down' : 'chevron-right'}
          size={20}
          color="#000000"
        />
      </TouchableOpacity>
      {isExpand && (
        <View>
          <View style={styles.contentView}>
            <View style={styles.oneView}>
              <View>
                <Sys.Text style={styles.label}>Theo giờ</Sys.Text>
                <Sys.Text style={styles.value}>500.000</Sys.Text>
              </View>
            </View>
            <View style={styles.oneView}>
              <View>
                <Sys.Text style={styles.label}>Ngày hôm qua</Sys.Text>
                <Sys.Text style={styles.value}>500.000</Sys.Text>
              </View>
            </View>
          </View>
          <View style={styles.contentView}>
            <View style={styles.oneView}>
              <View>
                <Sys.Text style={styles.label}>Lũy kế</Sys.Text>
                <Sys.Text style={styles.value}>500.000</Sys.Text>
              </View>
            </View>
            <View style={styles.oneView}>
              <View>
                <Sys.Text style={styles.label}>Quý</Sys.Text>
                <Sys.Text style={styles.value}>500.000</Sys.Text>
              </View>
            </View>
          </View>
          <View style={styles.contentView}>
            <View style={styles.oneView}>
              <View>
                <Sys.Text style={styles.label}>Năm</Sys.Text>
                <Sys.Text style={styles.value}>500.000</Sys.Text>
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

export default SARevenue;
