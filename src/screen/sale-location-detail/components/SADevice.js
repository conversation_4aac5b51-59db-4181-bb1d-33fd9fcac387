import {FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import THEME from '../../../components/theme/theme';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Sys from '../../../components/Sys';

const SADevice = () => {
  const [isExpand, setIsExpand] = useState(false);

  const styles = StyleSheet.create({
    container: {
      borderWidth: 1,
      borderColor: '#d1d1d1',
      borderRadius: 8,
      marginBottom: 20,
      paddingVertical: 5,
    },
    title: {
      fontSize: 16,
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
      lineHeight: 26,
    },
    titleContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 10,
      paddingLeft: 15,
      paddingRight: 10,
    },
  });

  const onToggle = () => setIsExpand(!isExpand);

  return (
    <View style={styles.container}>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={onToggle}
        style={styles.titleContainer}>
        <Sys.Text style={styles.title}>Danh sách trang thiết bị</Sys.Text>
        <MaterialCommunityIcons
          name={isExpand ? 'chevron-down' : 'chevron-right'}
          size={20}
          color="#000000"
        />
      </TouchableOpacity>
      {isExpand && (
        <View>
          <FlatList
            scrollEnabled={false}
            data={[1, 2, 3]}
            renderItem={({item, index}) => <OneItem item={item} />}
          />
        </View>
      )}
    </View>
  );
};

export default SADevice;

const OneItem = ({item, index}) => {
  const styles = StyleSheet.create({
    container: {
      margin: 5,
      backgroundColor: '#F1F1F1',
      borderRadius: 8,
      paddingVertical: 10,
      paddingHorizontal: 15,
    },
    name: {
      lineHeight: 24,
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
    },
    code: {lineHeight: 24},
    keyName: {lineHeight: 24, color: '#848484'},
    keyValue: {lineHeight: 24},
    flex: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    right: {},
    left: {},
  });
  return (
    <View style={styles.container}>
      <View style={styles.flex}>
        <View>
          <Sys.Text style={styles.name}>Máy A</Sys.Text>
        </View>
        <View>
          <Sys.Text style={styles.code}>100</Sys.Text>
        </View>
      </View>
      <View style={styles.flex}>
        <View>
          <Sys.Text style={styles.keyName}>Đơn vị:</Sys.Text>
        </View>
        <View>
          <Sys.Text style={styles.keyValue}>Hà Nội</Sys.Text>
        </View>
      </View>
      <View style={styles.flex}>
        <View>
          <Sys.Text style={styles.keyName}>Loại trang thiết bị:</Sys.Text>
        </View>
        <View>
          <Sys.Text style={styles.keyValue}>Máy tính</Sys.Text>
        </View>
      </View>
    </View>
  );
};
