import React from 'react';
import {FlatList, StyleSheet, View} from 'react-native';
import Sys from '../../../components/Sys';
const data = [
  {
    name: '<PERSON><PERSON> điểm bán:',
    value: '01234',
  },
  {
    name: '<PERSON><PERSON><PERSON> điểm bán:',
    value: '<PERSON><PERSON>ể<PERSON> bán Đống Đa',
  },
  {
    name: 'Số lần ghé thăm gần nhất:',
    value: '20',
  },
  {
    name: '<PERSON><PERSON><PERSON> nh<PERSON> trực thuộc:',
    value: '01234',
  },
  {
    name: '<PERSON><PERSON> điện thoại chủ điểm:',
    value: '0123456789',
  },
  {
    name: '<PERSON><PERSON><PERSON> mức đầu ngàys:',
    value: '50.000.000 đ',
  },
];
const SABasic = () => {
  return (
    <View>
      <FlatList
        scrollEnabled={false}
        data={data}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item, index}) => <OneItem item={item} />}
      />
    </View>
  );
};

export default SABasic;

const OneItem = ({item}) => {
  const styles = StyleSheet.create({
    container: {
      marginBottom: 15,
      flexDirection: 'row',
    },
    left: {
      flex: 1,
    },
    right: {
      flex: 1,
    },
    name: {
      color: '#848484',
      lineHeight: 24,
    },
    value: {
      lineHeight: 24,
      textAlign: 'right',
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.left}>
        <Sys.Text style={styles.name}>{item.name}</Sys.Text>
      </View>
      <View style={styles.right}>
        <Sys.Text style={styles.value}>{item.value}</Sys.Text>
      </View>
    </View>
  );
};
