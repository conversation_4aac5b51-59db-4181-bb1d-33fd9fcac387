import {useNavigation, useRoute} from '@react-navigation/native';
import moment from 'moment';
import React, {useCallback, useEffect, useState} from 'react';
import {
  FlatList,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {Calendar} from 'react-native-calendars';
import ReactNativeModal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useDispatch, useSelector} from 'react-redux';
import {AppSelectors} from '../../app.slice';
import Sys from '../../components/Sys';
import {formatVND, isAndroid, isIOS} from '../../services/util';
import CloseIcon from './icons/CloseIcon';
import SearchIcon from './icons/SearchIcon';
import SelectIcon from './icons/SelectIcon';
import {PosActions, PosSelectors} from './services/pos.slice';

const SaleLocationFilterScreen = () => {
  const {width, height} = useWindowDimensions();
  const insets = useSafeAreaInsets();
  const provinceList = useSelector(PosSelectors.provinceList);
  const districtList = useSelector(PosSelectors.districtList);
  const posTypeList = useSelector(PosSelectors.posTypeList);
  const settings = useSelector(AppSelectors.settings);
  const no_revenue = settings?.pos_warn?.no_revenue;
  const threshold_amount = settings?.pos_warn?.threshold_amount;
  const {goBack} = useNavigation();
  const {params} = useRoute();
  const dispatch = useDispatch();
  const body = useSelector(PosSelectors.body);
  const timeList = [
    {id: 1, name: `ĐBH hạn mức thấp dưới ${formatVND(threshold_amount)}đ`},
    {id: 2, name: `ĐBH ${no_revenue} ngày liên tục không phát sinh DT`},
  ];

  const [posCode, setPosCode] = useState(body?.search || '');
  const [fromDate, setFromDate] = useState({
    isShow: false,
    date: body?.from_date_checkin || undefined,
  });
  const [toDate, setToDate] = useState({
    isShow: false,
    date: body?.to_date_checkin || undefined,
  });
  const [province, setProvince] = useState({isShow: false, item: undefined});
  const [district, setDistrict] = useState({isShow: false, item: undefined});
  const [agencyList, setAgencyList] = useState([]);
  const [agency, setAgency] = useState({isShow: false, item: undefined});
  const [posType, setPosType] = useState([]);
  const [status, setStatus] = useState([]);
  const visitTimeList = [
    {name: 'Nhỏ hơn 90 Ngày', value: 1},
    {name: 'Từ 90 Ngày đến 120 Ngày', value: 2},
    {name: 'Lớn hơn 120 Ngày', value: 3},
  ];
  const [visitTime, setVisitTime] = useState({isShow: false, date: undefined});

  useEffect(() => {
    if (provinceList.length > 0) {
      const tempProvince = provinceList.filter(x => x.id === body?.province);
      if (tempProvince.length > 0) {
        setProvince({isShow: false, item: tempProvince[0]});
      }
    }
  }, [provinceList]);

  useEffect(() => {
    if (districtList.length > 0 && body?.district?.length > 0) {
      const firstDistrictId = body.district[0];
      const matchingDistrict = districtList.find(
        district => district.id === firstDistrictId,
      );
      if (matchingDistrict) {
        setDistrict({isShow: false, item: matchingDistrict});
      } else {
        setDistrict({isShow: false, item: undefined});
      }
    }
  }, [districtList, body.district]);

  useEffect(() => {
    if (agencyList.length > 0 && body?.agency?.length > 0) {
      const firstAgencyId = body.agency[0];
      const matchingAgency = agencyList.find(
        agency => agency.id === firstAgencyId,
      );

      setAgency({
        isShow: false,
        item: matchingAgency || undefined,
      });
    } else {
      setAgency({
        isShow: false,
        item: undefined,
      });
    }
  }, [agencyList, body.agency]);

  useEffect(() => {
    if (posTypeList.length > 0 && body?.posType) {
      setPosType(body.posType);
    }
  }, [posTypeList, body.posType]);

  useEffect(() => {
    console.log('SaleLocationFilterScreen checkin_days', body);
    if (body?.checkin_days_gte && body?.checkin_days_lte) {
      setVisitTime({
        isShow: false,
        item: {name: 'Từ 90 Ngày đến 120 Ngày', value: 2},
      });
    } else if (body?.checkin_days_lte) {
      setVisitTime({
        isShow: false,
        item: {name: 'Nhỏ hơn 90 Ngày', value: 1},
      });
    } else if (body?.checkin_days_gte) {
      setVisitTime({
        isShow: false,
        item: {name: 'Lớn hơn 120 Ngày', value: 3},
      });
    } else {
      setVisitTime({
        isShow: false,
        item: undefined,
      });
    }
  }, [body?.checkin_days_gte, body?.checkin_days_lte]);

  useEffect(() => {
    const tempStatus = [];

    if (
      body?.sort_by === 'start_threshold_amount' &&
      body?.sort_order === 'desc' &&
      body?.start_threshold_amount_lte === threshold_amount
    ) {
      tempStatus.push(1);
    }

    if (
      body?.sort_by === 'recent_revenue' &&
      body?.sort_order === 'desc' &&
      body?.recent_revenue_lte === 0
    ) {
      tempStatus.push(2);
    }

    setStatus(tempStatus);
  }, [
    body?.sort_by,
    body?.sort_order,
    body?.start_threshold_amount_lte,
    body?.recent_revenue_lte,
    threshold_amount,
  ]);

  useEffect(() => {
    dispatch(PosActions.getDistricts({province: province?.item?.id}));
    setDistrict({isShow: false, item: undefined});
  }, [province?.item]);

  useEffect(() => {
    dispatch(PosActions.getProvinces());
    dispatch(PosActions.getPosType());
    dispatch(
      PosActions.getAgency({
        onSuccess: rs => {
          setAgencyList(rs);
        },
      }),
    );
  }, []);

  const onReset = () => {
    setPosCode('');
    setStatus([]);
    setDistrict({isShow: false, item: undefined});
    setFromDate({isShow: false, date: undefined});
    setToDate({isShow: false, date: undefined});
    setProvince({isShow: false, item: undefined});
    setAgency({isShow: false, item: undefined});
    setPosType([]);
    setVisitTime({isShow: false, item: undefined});
  };

  const renderConditions = useCallback(
    tempBody => {
      if (visitTime?.item?.value) {
        if (visitTime?.item?.value === 2) {
          return {...tempBody, checkin_days_gte: 90, checkin_days_lte: 120};
        }
        if (visitTime?.item?.value === 1) {
          return {
            ...tempBody,
            checkin_days_lte: 89,
            checkin_days_gte: undefined,
          };
        }
        if (visitTime?.item?.value === 3) {
          return {
            ...tempBody,
            checkin_days_gte: 121,
            checkin_days_lte: undefined,
          };
        }
      } else {
        return {
          ...tempBody,
          checkin_days_gte: undefined,
          checkin_days_lte: undefined,
        };
      }
      return tempBody;
    },
    [visitTime],
  );

  const renderStatus = useCallback(
    temp => {
      try {
        if (status.indexOf(1) > -1) {
          temp.sort_by = 'start_threshold_amount';
          temp.sort_order = 'desc';
          temp.start_threshold_amount_lte = threshold_amount;
        } else {
          delete temp?.start_threshold_amount_lte;
        }
        if (status.indexOf(2) > -1) {
          temp.sort_by = 'recent_revenue';
          temp.sort_order = 'desc';
          temp.recent_revenue_lte = 0;
        } else {
          delete temp?.recent_revenue_lte;
        }
        return temp;
      } catch (error) {
        return temp;
      }
    },
    [status],
  );

  const onAccept = () => {
    params.onAccept(
      renderStatus(
        renderConditions({
          ...body,
          search: posCode || undefined,
          from_date_checkin: fromDate.date || undefined,
          to_date_checkin: toDate.date || undefined,
          province: province?.item?.id || undefined,
          district: district?.item ? [district?.item?.id] : [],
          agency: agency?.item ? [agency?.item?.id] : [],
          posType: posType.length > 0 ? posType : undefined,
          page: 1,
        }),
      ),
      'filter',
    );
    goBack();
  };

  const renderModal = (state, setState, content, title) => (
    <ReactNativeModal
      propagateSwipe
      animationIn="fadeInUp"
      animationOut="fadeOutDown"
      style={styles.modalStyle}
      onBackdropPress={() => setState({...state, isShow: false})}
      isVisible={state.isShow}>
      <View style={{flex: 1}} />
      <View
        style={[
          styles.modalContent,
          {width, paddingBottom: insets.bottom + 20},
        ]}>
        <Sys.Text
          style={[styles.modalTitle, {paddingBottom: insets.bottom + 20}]}>
          {title}
        </Sys.Text>
        {content}
      </View>
    </ReactNativeModal>
  );

  return (
    <Sys.Container
      headerColor="white"
      hasHeader
      hasFooter
      backgroundColor="white"
      footerColor="white">
      {renderModal(
        visitTime,
        setVisitTime,
        <FlatList
          showsVerticalScrollIndicator={false}
          data={visitTimeList}
          keyExtractor={(item, index) => `${index}-prov`}
          renderItem={({item}) => (
            <TouchableOpacity
              onPress={() => setVisitTime({isShow: false, item})}
              style={styles.listItem}>
              <Sys.Text>{item.name}</Sys.Text>
            </TouchableOpacity>
          )}
        />,
        'Chọn thời gian ghé thăm',
      )}
      {renderModal(
        fromDate,
        setFromDate,
        <Calendar
          markingType="custom"
          markedDates={{
            [`${fromDate.date}`]: {
              customStyles: {
                container: {backgroundColor: 'blue'},
                text: {color: 'white'},
              },
            },
          }}
          onDayPress={day => setFromDate({isShow: false, date: day.dateString})}
        />,
        'Chọn ngày bắt đầu',
      )}
      {renderModal(
        toDate,
        setToDate,
        <Calendar
          markingType="custom"
          markedDates={{
            [`${toDate.date}`]: {
              customStyles: {
                container: {backgroundColor: 'blue'},
                text: {color: 'white'},
              },
            },
          }}
          onDayPress={day => setToDate({isShow: false, date: day.dateString})}
        />,
        'Chọn ngày kết thúc',
      )}
      {renderModal(
        province,
        setProvince,
        <FlatList
          showsVerticalScrollIndicator={false}
          data={provinceList}
          keyExtractor={(item, index) => `${index}-prov`}
          renderItem={({item}) => (
            <TouchableOpacity
              onPress={() => setProvince({isShow: false, item})}
              style={styles.listItem}>
              <Sys.Text>{item.text}</Sys.Text>
            </TouchableOpacity>
          )}
        />,
        'Chọn Tỉnh/Thành phố',
      )}
      {renderModal(
        district,
        setDistrict,
        <FlatList
          showsVerticalScrollIndicator={false}
          data={districtList}
          keyExtractor={(item, index) => `${index}-prov`}
          renderItem={({item}) => (
            <TouchableOpacity
              onPress={() => setDistrict({isShow: false, item})}
              style={styles.listItem}>
              <Sys.Text>{item.text}</Sys.Text>
            </TouchableOpacity>
          )}
        />,
        'Chọn Quận/Huyện',
      )}
      {renderModal(
        agency,
        setAgency,
        <FlatList
          showsVerticalScrollIndicator={false}
          data={agencyList}
          keyExtractor={(item, index) => `${index}-prov`}
          renderItem={({item}) => (
            <TouchableOpacity
              onPress={() => setAgency({isShow: false, item})}
              style={styles.listItem}>
              <Sys.Text>{item.text}</Sys.Text>
            </TouchableOpacity>
          )}
        />,
        'Chọn Phường/Xã',
      )}

      <View style={styles.header}>
        <TouchableOpacity
          onPress={goBack}
          activeOpacity={0.8}
          style={styles.closeButton}>
          <CloseIcon />
        </TouchableOpacity>
        <Sys.Text style={styles.headerTitle}>Lọc địa điểm</Sys.Text>
        <View style={styles.headerSpacer} />
      </View>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.inputContainer}>
          <Sys.Input
            style={styles.input}
            value={posCode}
            onChangeText={setPosCode}
            placeholder="Nhập mã điểm bán"
            left={
              <View style={[styles.inputIcon, {marginTop: isIOS ? 12 : 6}]}>
                <SearchIcon />
              </View>
            }
          />
        </View>
        <View style={styles.dateContainer}>
          <View style={[styles.inputContainer, styles.dateInput]}>
            <Sys.Text style={styles.label}>Từ ngày</Sys.Text>
            <TouchableOpacity
              onPress={() => setFromDate({...fromDate, isShow: true})}
              activeOpacity={0.8}
              style={styles.selectButton}>
              <Sys.Text style={{color: fromDate.date ? 'black' : '#D1D1D1'}}>
                {fromDate.date
                  ? moment(fromDate.date, 'YYYY-MM-DD').format('DD/MM/YYYY')
                  : 'Chọn ngày bắt đầu'}
              </Sys.Text>
              <SelectIcon style={styles.selectIcon} />
            </TouchableOpacity>
          </View>
          <View style={[styles.inputContainer, styles.dateInput]}>
            <Sys.Text style={styles.label}>Đến ngày</Sys.Text>
            <TouchableOpacity
              onPress={() => setToDate({...toDate, isShow: true})}
              activeOpacity={0.8}
              style={styles.selectButton}>
              <Sys.Text style={{color: toDate.date ? 'black' : '#D1D1D1'}}>
                {toDate.date
                  ? moment(toDate.date, 'YYYY-MM-DD').format('DD/MM/YYYY')
                  : 'Chọn ngày kết thúc'}
              </Sys.Text>
              <SelectIcon style={styles.selectIcon} />
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.inputContainer}>
          <Sys.Text style={styles.label}>Tỉnh/Thành phố</Sys.Text>
          <TouchableOpacity
            onPress={() => setProvince({...province, isShow: true})}
            activeOpacity={0.8}
            style={styles.selectButton}>
            <Sys.Text style={{color: province.item ? 'black' : '#D1D1D1'}}>
              {province?.item ? province?.item?.text : 'Chọn Tỉnh/Thành Phố'}
            </Sys.Text>
            <SelectIcon style={styles.selectIcon} />
          </TouchableOpacity>
        </View>
        <View style={styles.inputContainer}>
          <Sys.Text style={styles.label}>Quận/Huyện</Sys.Text>
          <TouchableOpacity
            onPress={() => {
              if (districtList.length !== 0) {
                setDistrict({...district, isShow: true});
              }
            }}
            activeOpacity={0.8}
            style={styles.selectButton}>
            <Sys.Text style={{color: district.item ? 'black' : '#D1D1D1'}}>
              {district?.item ? district?.item?.text : 'Chọn Quận/Huyện'}
            </Sys.Text>
            <SelectIcon style={styles.selectIcon} />
          </TouchableOpacity>
        </View>
        <View style={styles.inputContainer}>
          <Sys.Text style={styles.label}>Đại lý</Sys.Text>
          <TouchableOpacity
            onPress={() => {
              if (agencyList.length !== 0) {
                setAgency({...agency, isShow: true});
              }
            }}
            activeOpacity={0.8}
            style={styles.selectButton}>
            <Sys.Text style={{color: agency.item ? 'black' : '#D1D1D1'}}>
              {agency?.item ? agency?.item?.text : 'Chọn đại lý'}
            </Sys.Text>
            <SelectIcon style={styles.selectIcon} />
          </TouchableOpacity>
        </View>
        <View style={styles.inputContainer}>
          <Sys.Text style={styles.label}>Thời gian ghé thăm</Sys.Text>
          <TouchableOpacity
            onPress={() => setVisitTime({...visitTime, isShow: true})}
            activeOpacity={0.8}
            style={styles.selectButton}>
            <Sys.Text
              style={{color: visitTime?.item?.name ? 'black' : '#D1D1D1'}}>
              {visitTime?.item?.name || 'Chọn thời gian ghé thăm'}
            </Sys.Text>
            <SelectIcon style={styles.selectIcon} />
          </TouchableOpacity>
        </View>
        <View style={styles.inputContainer}>
          <Sys.Text style={styles.label}>Loại điểm bán</Sys.Text>
          <FlatList
            keyExtractor={item => item.name}
            data={posTypeList}
            renderItem={({item}) => (
              <OnePosType
                item={item}
                posType={posType}
                setPosType={setPosType}
              />
            )}
          />
        </View>
        <View style={styles.inputContainer}>
          <Sys.Text style={styles.label}>Tình trạng</Sys.Text>
          <FlatList
            keyExtractor={item => item.name}
            data={timeList}
            renderItem={({item}) => (
              <OneStatus item={item} status={status} setStatus={setStatus} />
            )}
          />
        </View>
        <View style={{height: 50}} />
      </ScrollView>
      <View style={styles.footer}>
        <TouchableOpacity onPress={onReset} style={styles.resetButton}>
          <Sys.Text style={styles.resetButtonText}>Thiết lập lại</Sys.Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={onAccept} style={styles.applyButton}>
          <Sys.Text style={styles.applyButtonText}>Áp dụng</Sys.Text>
        </TouchableOpacity>
      </View>
    </Sys.Container>
  );
};

const OneStatus = ({item, status, setStatus}) => (
  <View style={styles.posTypeContainer}>
    <Sys.Checkbox
      checked={status.indexOf(item?.id) > -1}
      onPress={() => {
        if (status.indexOf(item?.id) > -1) {
          setStatus(status.filter(x => x !== item.id));
        } else {
          setStatus([item?.id]);
        }
      }}
    />
    <Sys.Text style={styles.posTypeText}>{item?.name}</Sys.Text>
  </View>
);

const OnePosType = ({item, posType, setPosType}) => (
  <View style={styles.posTypeContainer}>
    <Sys.Checkbox
      checked={posType.indexOf(item?.id) > -1}
      onPress={() => {
        if (posType.indexOf(item?.id) > -1) {
          setPosType(posType.filter(x => x !== item.id));
        } else {
          setPosType([...posType, item?.id]);
        }
      }}
    />
    <Sys.Text style={styles.posTypeText}>{item?.name}</Sys.Text>
  </View>
);

export default SaleLocationFilterScreen;

const styles = StyleSheet.create({
  modalStyle: {
    padding: 0,
    margin: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
  },
  modalTitle: {
    fontWeight: isAndroid ? 'bold' : '600',
  },
  listItem: {
    padding: 10,
    marginBottom: 5,
    backgroundColor: '#d1d1d110',
    borderRadius: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  closeButton: {
    padding: 20,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#0062FF',
    fontSize: 18,
    fontWeight: '600',
  },
  headerSpacer: {
    width: 54,
  },
  scrollView: {
    paddingTop: 10,
    paddingBottom: 20,
    paddingHorizontal: 20,
    flex: 1,
  },
  inputContainer: {
    marginBottom: 10,
    marginRight: 5,
  },
  input: {
    paddingLeft: 40,
  },
  inputIcon: {
    marginTop: 6,
    marginLeft: 10,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dateInput: {
    flex: 1,
  },
  label: {
    marginBottom: 5,
    textTransform: 'uppercase',
    fontWeight: isAndroid ? 'bold' : '600',
    color: '#44444F',
  },
  selectButton: {
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D1D1',
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  selectIcon: {
    position: 'absolute',
    right: 10,
    top: 10,
  },
  footer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: 'white',
    flexDirection: 'row',
  },
  resetButton: {
    marginRight: 10,
    flex: 1,
    borderWidth: 1,
    borderRadius: 50,
    padding: 10,
    borderColor: '#0062FF',
    alignItems: 'center',
  },
  resetButtonText: {
    color: '#0062FF',
    fontWeight: '600',
  },
  applyButton: {
    marginLeft: 10,
    flex: 1,
    borderWidth: 1,
    borderRadius: 50,
    padding: 10,
    borderColor: '#0062FF',
    alignItems: 'center',
    backgroundColor: '#0062FF',
  },
  applyButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  posTypeContainer: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'center',
  },
  posTypeText: {
    marginLeft: 5,
  },
});
