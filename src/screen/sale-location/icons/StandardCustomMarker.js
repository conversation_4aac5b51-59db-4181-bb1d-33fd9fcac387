import React, {memo} from 'react';
import {Marker} from 'react-native-maps';
import {StyleSheet, View} from 'react-native';
import Svg, {Circle, Path} from 'react-native-svg';

const StandardCustomMarker = memo(
  ({coordinate, onPress, pinColor}) => {
    return (
      <Marker
        coordinate={coordinate}
        onPress={onPress}
        pinColor={pinColor?.color}>
        <View style={styles.marker}>
          <Svg width="24" height="40" viewBox="0 0 24 40" fill="none">
            <Path
              d="M12 0C18.6274 0 24 5.37258 24 12C24 21 12 40 12 40C12 40 0 21 0 12C0 5.37258 5.37258 0 12 0Z"
              fill={pinColor?.color || '#FF0000'}
            />
            <Circle cx="12" cy="12" r="5" fill="white" />
          </Svg>
        </View>
      </Marker>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.coordinate === nextProps.coordinate &&
      prevProps.pinColor === nextProps.pinColor
    );
  },
);

const styles = StyleSheet.create({
  marker: {
    alignItems: 'center',
  },
});

export default StandardCustomMarker;
