import React, {memo} from 'react';
import {Marker} from 'react-native-maps';
import {StyleSheet, View} from 'react-native';
import Svg, {Path, Rect, Text as SvgText} from 'react-native-svg';

const CustomMarker = memo(
  ({amount, coordinate, onPress, pinColor}) => {
    return (
      <Marker coordinate={coordinate} onPress={onPress} pinColor={pinColor}>
        <View style={styles.marker}>
          <Svg width="100" height="60" viewBox="0 0 100 60">
            <Rect
              x="5"
              y="5"
              width="90"
              height="40"
              rx="6"
              fill={pinColor || '#00AEEF'}
            />
            <Path d="M50 55L45 45H55L50 55Z" fill={pinColor || '#00AEEF'} />
            <SvgText
              x="50"
              y="25"
              textAnchor="middle"
              dy="0.3em"
              fontSize={amount === 'chưa ghé thăm' ? 10 : 14}
              fontWeight="bold"
              fill="white">
              {amount}
            </SvgText>
          </Svg>
        </View>
      </Marker>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.amount === nextProps.amount &&
      prevProps.coordinate === nextProps.coordinate &&
      prevProps.pinColor === nextProps.pinColor
    );
  },
);

const styles = StyleSheet.create({
  marker: {
    alignItems: 'center',
  },
});

export default CustomMarker;
