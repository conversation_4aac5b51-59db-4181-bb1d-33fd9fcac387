import React, {memo} from 'react';
import {Image, StyleSheet, View} from 'react-native';

const MyMarker = memo(
  ({avatarUri}) => {
    return (
      <View style={styles.container}>
        {/* <Svg height="80" width="160" style={styles.svg}>
          <Rect x="0" y="0" width="160" height="40" fill={Color.second} />
          <Path d="M80 70 L75 40 L85 40 Z" fill={Color.second} />
          <SvgText
            x="80"
            y="25"
            fill="white"
            fontSize="16"
            fontWeight="bold"
            textAnchor="middle">
            Bạn đang ở đây
          </SvgText>
        </Svg>*/}
        <Image style={styles.image} source={{uri: avatarUri}} />
      </View>
    );
  },
  (prevProps, nextProps) => {
    return prevProps.avatarUri === nextProps.avatarUri;
  },
);

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  svg: {
    marginBottom: -15,
  },
  image: {
    height: 40,
    width: 40,
    borderRadius: 30,
  },
});

export default MyMarker;
