import * as React from 'react';
import Svg, {Circle, Path} from 'react-native-svg';
const AddPlanIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={23}
    height={23}
    fill="none"
    {...props}>
    <Circle cx={11.5} cy={11.5} r={11} fill="#0062FF" stroke="#fff" />
    <Path
      fill="#fff"
      fillRule="evenodd"
      d="M12.5 10.73h6.192a.885.885 0 0 1 0 1.77H12.5v6.192a.885.885 0 0 1-1.77 0V12.5H4.539a.885.885 0 0 1 0-1.77h6.193V4.539a.885.885 0 0 1 1.769 0v6.193Z"
      clipRule="evenodd"
    />
  </Svg>
);
export default AddPlanIcon;
