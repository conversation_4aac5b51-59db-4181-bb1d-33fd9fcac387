import React from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';

const {width} = Dimensions.get('window');

const SaleLocationSkeleton = () => {
  return (
    <View style={styles.container}>
      <ContentLoader
        speed={2}
        width={width - 20}
        height={120}
        viewBox={`0 0 ${width - 20} 120`}
        backgroundColor="#f3f3f3"
        foregroundColor="#ecebeb">
        <Rect x="10" y="15" rx="10" ry="10" width="48" height="48" />
        <Rect x="70" y="15" rx="4" ry="4" width="200" height="20" />
        <Rect x="70" y="45" rx="4" ry="4" width="150" height="15" />
        <Rect x="10" y="75" rx="10" ry="10" width={width - 40} height="10" />
        <Rect x="10" y="90" rx="10" ry="10" width={width - 40} height="10" />
      </ContentLoader>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    paddingVertical: 0,
    backgroundColor: 'white',
    borderRadius: 10,
    marginHorizontal: 10,
  },
});

export default SaleLocationSkeleton;
