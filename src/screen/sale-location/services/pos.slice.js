import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  body: {
    page: 1,
    pageSize: 50,
    sort_order: 'asc',
    sort_by: 'last_checkin',
  },
  posList: [],
  branches: [],
  isLoadingBranch: false,
  provinceList: [],
  districtList: [],
  wardList: [],
  posTypeList: [],
  total: 0,
  lastPage: 1,
};

const PosSlice = createSlice({
  name: 'pos',
  initialState,
  reducers: {
    setTotal: (state, {payload}) => {
      state.total = payload;
    },
    setLastPage: (state, {payload}) => {
      state.lastPage = payload;
    },
    setBody: (state, {payload}) => {
      // Reset body to only contain the new payload, keeping only essential fields
      const essentialFields = {
        with: state.body.with || [],
        page: 1,
        pageSize: state.body.pageSize || 50,
      };
      state.body = {...essentialFields, ...payload};
    },

    resetBody: state => {
      state.body = initialState.body;
    },
    getPos: () => {},
    getPosByBody: (state, {payload}) => {},
    getPosByURL: (state, {payload}) => {},
    setPos: (state, {payload}) => {
      state.posList = payload;
    },
    getProvinces: () => {},
    setProvinces: (state, {payload}) => {
      state.provinceList = payload;
    },
    getDistricts: () => {},
    setDistricts: (state, {payload}) => {
      state.districtList = payload;
    },
    getWards: () => {},
    setWards: (state, {payload}) => {
      state.wardList = payload;
    },
    getPosType: () => {},
    setPosType: (state, {payload}) => {
      state.posTypeList = payload;
    },
    getPostDetail: () => {},
    getPosListByPage: (state, {payload}) => {},
    getPosDetail: (state, {payload}) => {},
    getHistory: (state, {payload}) => {},
    planCreate: (state, {payload}) => {},
    getPlanCurrentMonth: (state, {payload}) => {},
    getPlanNextMonth: (state, {payload}) => {},
    getReason: (state, {payload}) => {},
    getPosReadinessChecklists: (state, {payload}) => {},
    deletePlan: (state, {payload}) => {},
    requestPlan: (state, {payload}) => {},
    getDeviceList: (state, {payload}) => {},
    proposeLatLon: (state, {payload}) => {},
    updatePending: (state, {payload}) => {},
    getDatePlan: (state, {payload}) => {},
    getHistoryDetail: (state, {payload}) => {},
    getAgency: (state, {payload}) => {},
    getPosListByPageCount: (state, {payload}) => {},
    getBranch: (state, {payload}) => {
      state.isLoadingBranch = true;
    },
    setBranch: (state, {payload}) => {
      state.branches = payload;
      state.isLoadingBranch = false;
    },
  },
});

const PosReducers = PosSlice.reducer;
export default PosReducers;

export const PosActions = PosSlice.actions;
export const PosSelectors = {
  branches: state => state.pos.branches,
  body: state => state.pos.body,
  total: state => state.pos.total,
  posList: state => state.pos.posList,
  provinceList: state => state.pos.provinceList,
  districtList: state => state.pos.districtList,
  wardList: state => state.pos.wardList,
  posTypeList: state => state.pos.posTypeList,
};
