import qs from 'qs';
import {delay, put, select, takeEvery, takeLatest} from 'redux-saga/effects';
import SysFetch from '../../../services/fetch';
import {AccountSelectors} from '../../account/services/account.slice';
import {PosActions, PosSelectors} from './pos.slice';

function* PosSaga() {
  yield takeEvery(PosActions.getPos, getPos);
  yield takeLatest(PosActions.getBranch, getBranch);
  yield takeEvery(PosActions.getPosByBody, getPosByBody);
  yield takeEvery(PosActions.getPosByURL, getPosByURL);
  yield takeEvery(PosActions.getProvinces, getProvinces);
  yield takeEvery(PosActions.getDistricts, getDistricts);
  yield takeEvery(PosActions.getWards, getWards);
  yield takeEvery(PosActions.getPosType, getPosType);
  yield takeEvery(PosActions.getPostDetail, getPostDetail);
  yield takeLatest(PosActions.getPosListByPage, getPosListByPage);
  yield takeEvery(PosActions.getPosDetail, getPosDetail);
  yield takeEvery(PosActions.getHistory, getHistory);
  yield takeEvery(PosActions.planCreate, planCreate);
  yield takeEvery(PosActions.getPlanCurrentMonth, getPlanCurrentMonth);
  yield takeEvery(PosActions.getPlanNextMonth, getPlanNextMonth);
  yield takeEvery(PosActions.getReason, getReason);
  yield takeEvery(PosActions.deletePlan, deletePlan);
  yield takeEvery(PosActions.requestPlan, requestPlan);
  yield takeEvery(PosActions.getDeviceList, getDeviceList);
  yield takeEvery(PosActions.proposeLatLon, proposeLatLon);
  yield takeEvery(PosActions.updatePending, updatePending);
  yield takeEvery(PosActions.getDatePlan, getDatePlan);
  yield takeEvery(PosActions.getHistoryDetail, getHistoryDetail);
  yield takeEvery(PosActions.getAgency, getAgency);
  yield takeEvery(PosActions.getPosListByPageCount, getPosListByPageCount);
  yield takeEvery(
    PosActions.getPosReadinessChecklists,
    getPosReadinessChecklists,
  );
}

export default PosSaga;

function* getBranch({payload = {}}) {
  try {
    const {onSuccess, onFail} = payload;
    const rs = yield SysFetch.get('branches-search?pageSize=-1');
    yield put(PosActions.setBranch(rs?.items || []));
    if (onSuccess) {
      onSuccess(rs);
    }
  } catch (error) {
    console.log({error});
    yield put(PosActions.setBranch([]));
  }
}

function* getHistoryDetail({payload}) {
  const {onSuccess, posCode, onFail, id} = payload;
  try {
    const rs = yield SysFetch.get(`pos/${posCode}/visit/${id}`);
    onSuccess && onSuccess(rs);
  } catch (error) {}
}

function* getDatePlan({payload}) {
  const {onSuccess, onFail, params} = payload;
  try {
    const rs = yield SysFetch.get('pos', params);
    onSuccess && onSuccess(rs);
  } catch (error) {
    // console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* updatePending({payload}) {
  const {onSuccess, posCode, onFail} = payload;
  try {
    const rs = yield SysFetch.get(`pos/pos-updates/pending/${posCode}`);
    onSuccess && onSuccess(rs);
  } catch (error) {
    // console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* proposeLatLon({payload}) {
  const {onSuccess, body, onFail} = payload;
  try {
    const rs = yield SysFetch.post('pos/propose-lat-lon', body);
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error?.response?.data);
    if (onFail) {
      onFail(error?.response?.data?.error || 'Có lỗi vui lòng thử lại');
    }
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* getDeviceList({payload}) {
  const {onSuccess, posCode, onFail} = payload;
  try {
    const rs = yield SysFetch.get(`inventory/item/pos/${posCode}`);
    onSuccess && onSuccess(rs);
  } catch (error) {
    // console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* requestPlan({payload}) {
  const {onSuccess, planId, onFail} = payload;
  try {
    const rs = yield SysFetch.put(`plan/proposePlan/${planId}`);
    onSuccess && onSuccess(rs);
  } catch (error) {
    // console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* deletePlan({payload}) {
  const {onSuccess, body, onFail} = payload;
  try {
    const rs = yield SysFetch.put(
      `plan/deletePosFromPlan/${body?.planId}?pos_ids[]=${body.posId}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    // console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* getPosReadinessChecklists({payload}) {
  const {onSuccess} = payload;
  try {
    const rs = yield SysFetch.get('pos/pos-readiness-checklists');
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
    // if (onFail) {
    //   onFail(error);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* getReason({payload}) {
  const {onSuccess} = payload;
  try {
    const rs = yield SysFetch.get('pos/store-closure-reasons');
    onSuccess && onSuccess(rs);
  } catch (error) {
    // console.log(error);
    // if (onFail) {
    //   onFail(error);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* getPlanNextMonth({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get('pos', {
      with: ['nextMonthPlan', 'plans', 'owner', 'agency', 'staff'],
      ...body,
    });
    onSuccess && onSuccess(rs);
  } catch (error) {
    onFail && onFail(error);
  }
}

function* getPlanCurrentMonth({payload}) {
  const {onSuccess, onFail, body} = payload;

  try {
    const rs = yield SysFetch.get('pos', {
      with: ['nextMonthPlan', 'plans', 'owner', 'agency', 'staff'],
      ...body,
    });
    onSuccess && onSuccess(rs);
  } catch (error) {
    onFail && onFail(error);
  }
}

function* planCreate({payload}) {
  const {onSuccess, posCode, date, onFail} = payload;
  try {
    const rs = yield SysFetch.post('plan/create', {
      pos_ids: [posCode],
      date,
    });

    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error?.response?.data);
    onFail && onFail(error?.response?.data);

    // if (error?.response?.data) {
    //   onFail && onFail(error?.response?.data);
    // } else {
    //   // Toast.show({
    //   //   type: 'error',
    //   //   text1: 'Lỗi',
    //   //   text2: error.toString(),
    //   // });
    // }
  }
}

function* getHistory({payload}) {
  try {
    const {onSuccess, posCode} = payload;

    const rs = yield SysFetch.get(
      `pos/${posCode}/visit-history?sort_order=desc&sort_by=created_at`,
    );

    onSuccess && onSuccess(rs);
  } catch (error) {
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}

function* getPosDetail({payload}) {
  const {onSuccess, posCode, onFail} = payload;

  try {
    const rs = yield SysFetch.get(`pos/${posCode}/show`);
    onSuccess && onSuccess(rs);
  } catch (error) {
    // onFail(error);
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}

function* getPosListByPage({payload}) {
  const {onSuccess, onFail, body} = payload;
  yield delay(300);
  try {
    const rs = yield SysFetch.get('pos', {
      with: ['nextMonthPlan', 'plans', 'owner', 'agency', 'staff'],
      ...body,
    });
    onSuccess && onSuccess(rs);
  } catch (error) {
    onFail && onFail(error);
  }
}

function* getPostDetail({payload}) {
  try {
    const {posCode, setPosDetail} = payload;
    const rs = yield SysFetch.get(`pos/${posCode}/show`);
    setPosDetail(rs.data);
  } catch (error) {
    // console.log(error);
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}

function* getPosType() {
  try {
    yield put(PosActions.setPosType([]));

    const rs = yield SysFetch.get('pos/pos-types');
    yield put(PosActions.setPosType(rs.data));
  } catch (error) {
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}

function* getProvinces() {
  try {
    yield put(PosActions.setProvinces([]));

    const rs = yield SysFetch.get('province-search?pageSize=-1');
    yield put(PosActions.setProvinces(rs.results));
  } catch (error) {
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}

function* getPosByURL({payload}) {
  try {
    const {onSuccess, url} = payload;
    const rs = yield SysFetch.get(`${url}`);
    onSuccess && onSuccess(rs.data);
  } catch (error) {
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}

function* getPosByBody({payload}) {
  try {
    const {onSuccess} = payload;
    const bodyParams = payload.body;

    yield put(PosActions.setPos([]));
    const profile = yield select(AccountSelectors.profile);

    if (!profile?.id) {
      return;
    }
    const rs = yield SysFetch.get('pos', {
      ...bodyParams,
      staff_id: profile?.id,
    });
    yield put(PosActions.setPos(rs.data));

    onSuccess && onSuccess(rs.data);
  } catch (error) {
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}

function* getPos({payload}) {
  try {
    const {onSuccess} = payload;

    yield put(PosActions.setPos([]));
    const body = yield select(PosSelectors.body);
    const profile = yield select(AccountSelectors.profile);

    const rs = yield SysFetch.get(
      `pos?${qs.stringify({...body, staff_id: profile?.id})}`,
    );
    yield put(PosActions.setPos(rs.data));

    onSuccess && onSuccess(rs.data);
  } catch (error) {
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}

function* getDistricts({payload}) {
  try {
    if (payload.province) {
      yield put(PosActions.setDistricts([]));

      const rs = yield SysFetch.get(
        `district-search?pageSize=-1&province=${payload.province}`,
      );
      yield put(PosActions.setDistricts(rs.results));
    }
  } catch (error) {
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}

function* getAgency({payload}) {
  const {onSuccess} = payload;
  try {
    const rs = yield SysFetch.get('agencies-search?pageSize=-1');
    onSuccess && onSuccess(rs?.results);
  } catch (error) {
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}

function* getWards({payload}) {
  try {
    yield put(PosActions.setWards([]));

    const rs = yield SysFetch.get(
      `ward-search?pageSize=-1&district=${payload.district}`,
    );
    yield put(PosActions.setWards(rs.results));
  } catch (error) {
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}

function* getPosListByPageCount({payload}) {
  const {onSuccess, onFail, body} = payload;
  yield delay(300);
  try {
    const url = `pos?with[]=nextMonthPlan&with[]=plans&with[]=owner&with[]=staff&with[]=agency&${qs.stringify(
      body,
      {
        encode: false,
      },
    )}`;
    const rs = yield SysFetch.get(url);
    onSuccess && onSuccess(rs.data, rs);
  } catch (error) {
    // onFail && onFail(error);
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}
