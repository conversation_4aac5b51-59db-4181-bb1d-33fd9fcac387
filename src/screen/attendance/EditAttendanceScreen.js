import React, {useState} from 'react';
import {Button, StyleSheet, TextInput, View} from 'react-native';
import SysFetch from '../../services/fetch';
import Sys from '../../components/Sys';

const EditAttendanceScreen = ({route, navigation}) => {
  const {attendanceId, startTime, endTime} = route.params;
  const [newStartTime, setNewStartTime] = useState(startTime);
  const [newEndTime, setNewEndTime] = useState(endTime);

  const handleSave = async () => {
    try {
      await SysFetch.put(`/attendance/${attendanceId}`, {
        check_in: newStartTime,
        check_out: newEndTime,
      });
      navigation.goBack();
    } catch (error) {
      console.error('Error updating attendance:', error);
    }
  };

  return (
    <View style={styles.container}>
      <Sys.Text style={styles.label}><PERSON><PERSON><PERSON> đến</Sys.Text>
      <TextInput
        style={styles.input}
        value={newStartTime}
        onChangeText={setNewStartTime}
      />
      <Sys.Text style={styles.label}>Giờ về</Sys.Text>
      <Sys.Input
        style={styles.input}
        value={newEndTime}
        onChangeText={setNewEndTime}
      />
      <Button title="Lưu" onPress={handleSave} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#000',
  },
  label: {
    fontSize: 16,
    color: '#FFF',
    marginBottom: 5,
  },
  input: {
    height: 40,
    borderColor: '#333',
    borderWidth: 1,
    marginBottom: 20,
    color: '#FFF',
    paddingHorizontal: 10,
  },
});

export default EditAttendanceScreen;
