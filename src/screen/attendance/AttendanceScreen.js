import React, {useEffect, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import SysFetch from '../../services/fetch';
import Sys from '../../components/Sys';
import Icons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useNavigation} from '@react-navigation/native';
import moment from 'moment';

let timer;

const AttendanceScreen = () => {
  const [status, setStatus] = useState('check-in');
  const [workTime, setWorkTime] = useState({hours: 0, minutes: 0, seconds: 0});
  const navigation = useNavigation();

  useEffect(() => {
    const fetchStatus = async () => {
      try {
        const response = await SysFetch.get('/attendance/status');
        setStatus(response.status);

        if (response.status === 'check-out' && response.check_in) {
          const start = moment(response.check_in);
          startTimer(start);
        }
      } catch (error) {
        console.error('Error fetching status:', error);
      }
    };

    fetchStatus();

    return () => {
      // Dọn dẹp khi component unmount (ngừng bộ đếm)
      clearInterval(timer);
    };
  }, []);

  const startTimer = start => {
    timer = setInterval(() => {
      const now = new Date();
      const diff = Math.floor((now - start) / 1000);
      const hours = Math.floor(diff / 3600);
      const minutes = Math.floor((diff % 3600) / 60);
      const seconds = diff % 60;
      setWorkTime({hours, minutes, seconds});
    }, 1000);
  };

  const handleCheckInOut = async () => {
    try {
      if (status === 'check-in') {
        const response = await SysFetch.post('/attendance/check-in');
        setStatus('check-out');
        const start = moment.utc(response.data.check_in).local().toDate();
        startTimer(start);
      } else {
        await SysFetch.post('/attendance/check-out');
        setStatus('check-in');
        clearInterval(timer);
        setWorkTime({hours: 0, minutes: 0, seconds: 0});
      }
    } catch (error) {
      console.error('Error during check-in/out:', error);
    }
  };

  return (
    <Sys.Container hasHeader hasFooter>
      <Sys.Header
        style={styles.header}
        title="Chấm công"
        right={
          <TouchableOpacity
            style={{marginRight: 5}}
            onPress={() => navigation.navigate('AttendanceListScreen')}>
            <Icons name="playlist-play" size={24} color="#FFF" />
          </TouchableOpacity>
        }
      />
      <View style={styles.container}>
        <Sys.Text
          style={
            styles.timeDisplay
          }>{`${workTime.hours}giờ ${workTime.minutes}phút ${workTime.seconds}giây`}</Sys.Text>
        <TouchableOpacity
          style={status === 'check-in' ? styles.buttonStart : styles.buttonStop}
          onPress={handleCheckInOut}>
          <Sys.Text style={styles.buttonText}>
            {status === 'check-in' ? 'Bắt đầu Ca' : 'Kết thúc Ca'}
          </Sys.Text>
        </TouchableOpacity>
      </View>
    </Sys.Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  timeDisplay: {
    color: '#FFF',
    fontSize: 20,
    marginBottom: 20,
  },
  buttonStart: {
    backgroundColor: '#3E82FC',
    padding: 15,
    borderRadius: 10,
  },
  buttonStop: {
    backgroundColor: '#E53935',
    padding: 15,
    borderRadius: 10,
  },
  buttonText: {
    color: '#FFF',
    fontSize: 16,
  },
});

export default AttendanceScreen;
