import React, {useEffect, useState} from 'react';
import {ActivityIndicator, FlatList, StyleSheet, View} from 'react-native';
import SysFetch from '../../services/fetch';
import Sys from '../../components/Sys';
import {useNavigation} from '@react-navigation/native';
import {formatDate} from '../../services/util';
import Color from '../../components/theme/Color';

const AttendanceListScreen = () => {
  const [attendanceRecords, setAttendanceRecords] = useState([]);
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const navigation = useNavigation();

  useEffect(() => {
    fetchAttendanceRecords();
  }, [page]);

  const fetchAttendanceRecords = async () => {
    if (isLoading || !hasMore) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await SysFetch.get('/attendance', {
        page,
      });
      const newRecords = response.data;
      const currentPage = response.meta.current_page;
      const lastPage = response.meta.last_page;

      setAttendanceRecords(prevRecords => [...prevRecords, ...newRecords]);
      setHasMore(currentPage < lastPage);
    } catch (error) {
      console.error('Error fetching attendance records:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadMoreRecords = () => {
    if (hasMore) {
      setPage(prevPage => prevPage + 1);
    }
  };

  const renderItem = ({item}) => {
    let evaluationColor = '#FFF';
    if (item.evaluation === 'Đạt') {
      evaluationColor = Color.green;
    } else if (item.evaluation === 'Không đạt') {
      evaluationColor = Color.red;
    } else {
      evaluationColor = Color.orange;
    }

    return (
      <View style={styles.record}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 5,
          }}>
          <Sys.Text style={styles.date}>{`${formatDate(item.date)}`}</Sys.Text>
          <Sys.Text
            style={
              styles.duration
            }>{`${item.total_hours}giờ ${item.total_minutes}phút`}</Sys.Text>
        </View>
        <Sys.Text style={styles.time}>{`${formatDate(item.check_in, true)} - ${
          formatDate(item.check_out, true) || 'Chưa kết thúc ca'
        }`}</Sys.Text>
        <Sys.Text style={[styles.evaluation, {color: evaluationColor}]}>
          {item.evaluation}
        </Sys.Text>
      </View>
    );
  };
  const renderFooter = () => {
    if (!isLoading) {
      return null;
    }
    return <ActivityIndicator size="large" color="#FFF" />;
  };

  return (
    <Sys.Container hasHeader hasFooter>
      <Sys.Header style={styles.header} title="Danh sách chấm công" />
      <FlatList
        data={attendanceRecords}
        keyExtractor={item => item.id.toString()}
        renderItem={renderItem}
        onEndReached={loadMoreRecords}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
      />
    </Sys.Container>
  );
};

const styles = StyleSheet.create({
  record: {
    padding: 15,
    marginVertical: 8,
    marginHorizontal: 16,
    backgroundColor: Color.white90,
    borderRadius: 5,
    marginBottom: 5,
  },
  date: {
    fontSize: 16,
    color: Color.black,
    fontWeight: 'bold',
  },
  time: {
    fontSize: 14,
    color: Color.black,
  },
  evaluation: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  duration: {
    fontSize: 14,
    color: Color.black,
    fontWeight: 'bold',
  },
});

export default AttendanceListScreen;
