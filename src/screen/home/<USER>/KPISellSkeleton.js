import React from 'react';
import {StyleSheet, View} from 'react-native';
import ContentLoader, {Circle, Rect} from 'react-content-loader/native';

const KPISellSkeleton = () => {
  return (
    <View style={styles.container}>
      <ContentLoader
        speed={2}
        width={'100%'}
        height={100} // Adjusted height
        viewBox="0 0 400 100" // Updated viewBox to reflect new height
        backgroundColor="#f3f3f3"
        foregroundColor="#ecebeb">
        <Rect x="20" y="10" rx="5" ry="5" width="90" height="10" />
        <Rect x="20" y="30" rx="5" ry="5" width="90" height="10" />
        <Rect x="20" y="50" rx="5" ry="5" width="90" height="10" />
        <Rect x="20" y="70" rx="5" ry="5" width="90" height="10" />
        <Circle cx="300" cy="50" r="40" />
      </ContentLoader>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    marginBottom: 20,
  },
});

export default KPISellSkeleton;
