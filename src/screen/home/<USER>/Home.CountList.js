import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useEffect, useRef, useState} from 'react';
import {
  Animated,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {AppSelectors} from '../../../app.slice';
import Sys from '../../../components/Sys';
import {formatVND} from '../../../services/util';
import {AccountSelectors} from '../../account/services/account.slice';
import {PosActions} from '../../sale-location/services/pos.slice';
import Color from '../../../components/theme/Color';
import THEME from '../../../components/theme/theme';

const HomeCountList = () => {
  const dispatch = useDispatch();
  const {navigate} = useNavigation();

  const [amountCount, setAmountCount] = useState(0);
  const [revenueCount, setRevenueCount] = useState(0);
  const profile = useSelector(AccountSelectors.profile);
  const isFocused = useIsFocused();
  const settings = useSelector(AppSelectors.settings);
  const no_revenue = settings?.pos_warn?.no_revenue;
  const threshold_amount = settings?.pos_warn?.threshold_amount;

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  const data = [
    {
      count: amountCount,
      color: Color.orange,
      gradient: [Color.orange, '#FF8C42'],
      icon: 'alert-circle',
      title: 'ĐBH hạn mức thấp',
      content: `Dưới ${formatVND(threshold_amount)}đ`,
      params: {
        sort_by: 'start_threshold_amount',
        sort_order: 'desc',
        start_threshold_amount_lte: threshold_amount,
      },
    },
    {
      count: revenueCount,
      color: '#D30B0D',
      gradient: ['#D30B0D', '#FF4444'],
      icon: 'trending-down',
      title: 'ĐBH không phát sinh DT',
      content: `${no_revenue} ngày liên tục`,
      params: {
        sort_by: 'recent_revenue',
        sort_order: 'desc',
        recent_revenue_lte: 0,
      },
    },
  ];

  useEffect(() => {
    if (profile && isFocused) {
      dispatch(
        PosActions.getPosListByPageCount({
          body: data[0].params,
          onSuccess: (data, rs) => {
            setAmountCount(rs?.meta?.total);
            // Start entrance animation after data loads
            setTimeout(() => {
              Animated.parallel([
                Animated.timing(fadeAnim, {
                  toValue: 1,
                  duration: 600,
                  useNativeDriver: true,
                }),
                Animated.spring(scaleAnim, {
                  toValue: 1,
                  tension: 50,
                  friction: 8,
                  useNativeDriver: true,
                }),
              ]).start();
            }, 100);
          },
        }),
      );

      dispatch(
        PosActions.getPosListByPage({
          body: data[1].params,
          onSuccess: rs => {
            setRevenueCount(rs?.meta?.total);
          },
        }),
      );
    }
  }, [profile, isFocused]);

  return (
    <Animated.View
      style={[
        modernStyles.container,
        {
          opacity: fadeAnim,
          transform: [{scale: scaleAnim}],
        },
      ]}>
      <FlatList
        data={data}
        scrollEnabled={false}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item, index}) => {
          if (item?.count === 0) {
            return <></>;
          } else {
            return (
              <TouchableOpacity
                onPress={() => {
                  dispatch(PosActions.setBody(item.params));
                  navigate('SaleLocationScreen');
                }}
                style={modernStyles.alertItem}
                activeOpacity={0.8}>
                <LinearGradient
                  colors={['#ffffff', '#f8f9fa']}
                  style={modernStyles.alertItemGradient}>
                  {/* Icon Badge */}
                  <View style={modernStyles.iconContainer}>
                    <LinearGradient
                      colors={item.gradient}
                      style={modernStyles.iconBadge}>
                      <MaterialCommunityIcons
                        name={item.icon}
                        size={16}
                        color="white"
                      />
                    </LinearGradient>
                    <View style={modernStyles.countBadge}>
                      <Sys.Text style={modernStyles.countText}>
                        {item?.count}
                      </Sys.Text>
                    </View>
                  </View>

                  {/* Content */}
                  <View style={modernStyles.contentContainer}>
                    <Sys.Text style={modernStyles.alertTitle}>
                      {item?.title}
                    </Sys.Text>
                    <Sys.Text style={modernStyles.alertContent}>
                      {item?.content}
                    </Sys.Text>
                  </View>

                  {/* Arrow */}
                  <View style={modernStyles.arrowContainer}>
                    <MaterialCommunityIcons
                      name="chevron-right"
                      size={24}
                      color={Color.gray}
                    />
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            );
          }
        }}
      />
    </Animated.View>
  );
};

// Modern Styles
const modernStyles = StyleSheet.create({
  container: {
    marginHorizontal: 10,
  },

  // Alert Items
  alertItem: {
    marginBottom: 12,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
  },
  alertItemGradient: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },

  // Icon Container
  iconContainer: {
    position: 'relative',
    marginRight: 16,
  },
  iconBadge: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  countBadge: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: '#FF4444',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  countText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '700',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },

  // Content
  contentContainer: {
    flex: 1,
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.Color.text,
    lineHeight: 20,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginBottom: 4,
  },
  alertContent: {
    fontSize: 14,
    color: Color.gray,
    lineHeight: 18,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
  },

  // Arrow
  arrowContainer: {
    marginLeft: 12,
  },
});

export default HomeCountList;
