import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useEffect, useState, useRef} from 'react';
import {FlatList, TouchableOpacity, View, StyleSheet, Animated, Dimensions} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {AppSelectors} from '../../../app.slice';
import Sys from '../../../components/Sys';
import {formatVND} from '../../../services/util';
import {AccountSelectors} from '../../account/services/account.slice';
import {PosActions} from '../../sale-location/services/pos.slice';
import RightCountIcon from '../icons/RightCountIcon';
import Color from '../../../components/theme/Color';
import THEME from '../../../components/theme/theme';

const {width} = Dimensions.get('window');

const HomeCountList = () => {
  const dispatch = useDispatch();

  const {navigate} = useNavigation();

  const [amountCount, setAmountCount] = useState(0);
  const [revenueCount, setRevenueCount] = useState(0);
  const profile = useSelector(AccountSelectors.profile);
  const isFocused = useIsFocused();
  const settings = useSelector(AppSelectors.settings);
  const no_revenue = settings?.pos_warn?.no_revenue;
  const threshold_amount = settings?.pos_warn?.threshold_amount;

  const data = [
    {
      count: amountCount,
      color: '#EF7721',
      content: `ĐBH hạn mức thấp dưới ${formatVND(threshold_amount)}đ`,
      params: {
        sort_by: 'start_threshold_amount',
        sort_order: 'desc',
        start_threshold_amount_lte: threshold_amount,
      },
    },
    {
      count: revenueCount,
      color: '#D30B0D',
      content: `ĐBH ${no_revenue} ngày liên tục không phát sinh DT`,
      params: {
        sort_by: 'recent_revenue',
        sort_order: 'desc',
        recent_revenue_lte: 0,
      },
    },
  ];

  useEffect(() => {
    if (profile && isFocused) {
      dispatch(
        PosActions.getPosListByPageCount({
          body: data[0].params,
          onSuccess: (data, rs) => {
            setAmountCount(rs?.meta?.total);
          },
        }),
      );

      dispatch(
        PosActions.getPosListByPage({
          body: data[1].params,
          onSuccess: rs => {
            setRevenueCount(rs?.meta?.total);
          },
        }),
      );
    }
  }, [profile, isFocused]);

  return (
    <View>
      <FlatList
        data={data}
        scrollEnabled={false}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item, index}) => {
          if (item?.count === 0) {
            return <></>;
          } else {
            return (
              <TouchableOpacity
                onPress={() => {
                  dispatch(PosActions.setBody(item.params));
                  navigate('SaleLocationScreen');
                }}
                style={{
                  backgroundColor: '#F5F6FC',
                  borderRadius: 20,
                  marginBottom: 15,
                  padding: 20,
                  marginHorizontal: 10,
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <View>
                  <View
                    style={{
                      backgroundColor: item?.color,
                      width: 32,
                      height: 32,
                      borderRadius: 50,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <Sys.Text
                      style={{
                        color: 'white',
                        fontSize: 10,
                        lineHeight: 20,
                        fontWeight: '700',
                      }}>
                      {item?.count}
                    </Sys.Text>
                  </View>
                </View>
                <View style={{flex: 1, marginHorizontal: 10}}>
                  <Sys.Text
                    style={{
                      color: '#001451',
                      lineHeight: 24,
                      fontWeight: '600',
                    }}>
                    {item?.content}
                  </Sys.Text>
                </View>
                <View>
                  <RightCountIcon />
                </View>
              </TouchableOpacity>
            );
          }
        }}
      />
    </View>
  );
};

export default HomeCountList;
