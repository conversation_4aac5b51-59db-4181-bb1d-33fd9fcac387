import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const MessageIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={23}
    fill="none"
    {...props}>
    <Path
      stroke={props.color}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="M20.324 10.736a7.64 7.64 0 0 1-.865 3.547 8.024 8.024 0 0 1-3.01 3.201 8.348 8.348 0 0 1-4.293 1.186 8.247 8.247 0 0 1-3.652-.84l-5.477 1.774 1.826-5.321a7.64 7.64 0 0 1-.865-3.547c0-1.474.423-2.918 1.22-4.171a8.086 8.086 0 0 1 3.296-2.924 8.247 8.247 0 0 1 3.652-.84h.48a8.26 8.26 0 0 1 5.314 2.306 7.815 7.815 0 0 1 2.374 5.162v.466Z"
    />
  </Svg>
);
export default MessageIcon;
