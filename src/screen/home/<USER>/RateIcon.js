import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

function RateIcon(props) {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={30}
      height={31}
      viewBox="0 0 30 31"
      fill="none"
      {...props}>
      <Path
        d="M14.547 0c.604.028.977.343 1.238.856 1.07 2.098 2.152 4.189 3.231 6.282.129.249.216.544.407.73.27.263.597.586.937.637 1.838.28 3.689.485 5.535.717.816.103 1.633.2 2.444.323.515.08.874.39 1.03.89.16.517.024.974-.358 1.359-1 1.008-1.997 2.018-2.996 3.026-.675.68-1.359 1.35-2.018 2.043-.216.227-.423.315-.734.25-3.789-.782-7.917 1.7-8.625 5.977-.16.968-.598 1.382-1.362 1.78-1.94 1.015-3.85 2.088-5.765 3.15-.518.287-1.031.368-1.538.028-.51-.34-.624-.836-.528-1.432.374-2.322.699-4.652 1.098-6.97.164-.953.034-1.681-.746-2.369-1.719-1.517-3.355-3.127-5.026-4.7a23.37 23.37 0 01-.39-.38c-.361-.36-.461-.802-.319-1.274.149-.492.486-.816 1.013-.903 2.24-.365 4.48-.728 6.72-1.095.356-.058.709-.145 1.066-.189.565-.068.927-.382 1.161-.88 1.088-2.308 2.182-4.612 3.263-6.922.261-.555.643-.906 1.262-.934z"
        fill="#F5000D"
      />
      <Path
        d="M15.364 24.3c-.089-3.395 2.756-6.655 6.712-6.655 3.604 0 6.624 3.055 6.618 6.684-.006 3.689-3.022 6.684-6.722 6.67-3.776-.012-6.718-3.196-6.608-6.7zm10.939 3.564c1.834-2.063 1.826-5.56-.538-7.704-2.342-2.124-6-1.853-8.037.543-2.001 2.354-1.513 5.868.206 7.37.193-1.388.828-2.492 2.02-3.248.195-.123.336-.166.566-.005 1.04.734 2.122.739 3.175.032.259-.173.421-.15.654-.001 1.085.7 1.69 1.713 1.954 3.013z"
        fill="#F5000D"
      />
      <Path
        d="M20.817 24.463c-.077-.137-.133-.293-.235-.408-.814-.917-.743-2.256.172-3.052a2.114 2.114 0 013.003.253c.69.823.65 1.98-.084 2.795-.104.116-.404.198-.148.445-.075.073-.14.165-.227.216-.81.478-1.705.424-2.556-.144l.075-.105z"
        fill="#F5000D"
      />
    </Svg>
  );
}

export default RateIcon;
