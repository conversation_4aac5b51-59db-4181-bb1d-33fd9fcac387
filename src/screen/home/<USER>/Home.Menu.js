import React, {useCallback, useEffect, useState} from 'react';
import {Image, TouchableOpacity, useWindowDimensions, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Sys from '../../../components/Sys';
import ChartIcon from '../icons/ChartIcon';
import HomeIcon from '../icons/HomeIcon';
import MessageIcon from '../icons/MessageIcon';
import POSIcon from '../icons/POSIcon';
import UserIcon from '../icons/UserIcon';
import {useIsFocused} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {PosActions} from '../../sale-location/services/pos.slice';
import PlaneMenuIcon from '../icons/PlaneMenuIcon';
import BellMenuIcon from '../icons/BellMenuIcon';
import {
  getCurrentLocation,
  stopWatchingLocation,
} from '../../checkin-location/locationUtils';
import {AppActions, AppSelectors} from '../../../app.slice';

const HomeMenu = ({state, descriptors, navigation}) => {
  const {width} = useWindowDimensions();
  const insets = useSafeAreaInsets();
  const isFocused = useIsFocused();
  const [watchPosition, setWatchPosition] = useState(false);
  const [highAccuracy, setHighAccuracy] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const {location} = useSelector(AppSelectors.current_location);
  const {isStaff, isGuest, isPos, isBranchOwner, isBranchManager, isAgent} =
    useSelector(state => state.account);
  const dispatch = useDispatch();

  const checkLocationAgain = useCallback(() => {
    getCurrentLocation({
      highAccuracy,
      setCallback: ({data, error}) => {
        setIsLoading(false);
        if (data) {
          if (!watchPosition) {
            setWatchPosition(true);
            setHighAccuracy(true);
          }
          dispatch(AppActions.setLocation(data));
        }
      },
      watchPosition,
    });
  }, [highAccuracy, watchPosition, isFocused]);

  useEffect(() => {
    if (isFocused) {
      stopWatchingLocation();
      checkLocationAgain();
    }
    // return () => stopWatchingLocation();
  }, [isFocused, highAccuracy, watchPosition]);

  if (isGuest) {
    return (
      <View
        style={{
          position: 'absolute',
          bottom: insets.bottom + 5,
          left: 5,
        }}>
        <View
          style={{
            backgroundColor: 'white',
            borderRadius: 30,
            height: 80,
            width: width - 10,
            borderWidth: 1,
            borderColor: '#********',
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
          }}>
          <TouchableOpacity
            onPress={() => navigation.navigate('HomeScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <HomeIcon
              color={
                state?.routeNames[state?.index] === 'HomeScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Trang chủ
            </Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('AccountScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <UserIcon
              color={
                state?.routeNames[state?.index] === 'AccountScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Cá nhân
            </Sys.Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (isBranchManager) {
    return (
      <View
        style={{
          position: 'absolute',
          bottom: insets.bottom + 5,
          left: 5,
        }}>
        <View
          style={{
            backgroundColor: 'white',
            borderRadius: 30,
            height: 80,
            width: width - 10,
            borderWidth: 1,
            borderColor: '#********',
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
          }}>
          <TouchableOpacity
            onPress={() => navigation.navigate('HomeScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <HomeIcon
              color={
                state?.routeNames[state?.index] === 'HomeScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Trang chủ
            </Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('ReportScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <ChartIcon
              color={
                state?.routeNames[state?.index] === 'ReportScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Báo cáo
            </Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() => {
              dispatch(
                PosActions.setBody({
                  latitude: location?.latitude,
                  longitude: location?.longitude,
                  maxDistance: 3,
                  sort_by: 'distance',
                  sort_order: 'asc',
                  page: 1,
                  pageSize: 50,
                }),
              );
              navigation.navigate('SaleLocationScreen', {
                tab: 1,
                subTab: null,
              });
            }}
            style={{flex: 4, alignItems: 'center'}}>
            <View style={{position: 'absolute', top: -75}}>
              <POSIcon />
            </View>
            <View style={{position: 'absolute', bottom: -32}}>
              <Sys.Text
                style={{
                  fontSize: 12,
                  color: '#848484',
                }}>
                POS gần đây
              </Sys.Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('BranchManagerMessageScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <MessageIcon
              color={
                state?.routeNames[state?.index] === 'MessageSupportScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Phản ánh
            </Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('AccountScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <UserIcon
              color={
                state?.routeNames[state?.index] === 'AccountScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Cá nhân
            </Sys.Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (isStaff || isBranchOwner || isAgent) {
    return (
      <View
        style={{
          position: 'absolute',
          bottom: insets.bottom + 5,
          left: 5,
        }}>
        <View
          style={{
            backgroundColor: 'white',
            borderRadius: 30,
            height: 80,
            width: width - 10,
            borderWidth: 1,
            borderColor: '#********',
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
          }}>
          <TouchableOpacity
            onPress={() => navigation.navigate('HomeScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <HomeIcon
              color={
                state?.routeNames[state?.index] === 'HomeScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Trang chủ
            </Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('ReportScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <ChartIcon
              color={
                state?.routeNames[state?.index] === 'ReportScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Báo cáo
            </Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() => {
              dispatch(
                PosActions.setBody({
                  latitude: location?.latitude,
                  longitude: location?.longitude,
                  maxDistance: 3,
                  sort_by: 'distance',
                  sort_order: 'asc',
                  page: 1,
                  pageSize: 50,
                }),
              );
              navigation.navigate('SaleLocationScreen', {
                tab: 1,
                subTab: null,
              });
            }}
            style={{flex: 4, alignItems: 'center'}}>
            <View style={{position: 'absolute', top: -75}}>
              <POSIcon />
            </View>
            <View style={{position: 'absolute', bottom: -32}}>
              <Sys.Text
                style={{
                  fontSize: 12,
                  color: '#848484',
                }}>
                POS gần đây
              </Sys.Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('MessageSupportScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <MessageIcon
              color={
                state?.routeNames[state?.index] === 'MessageSupportScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Phản ánh
            </Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('AccountScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <UserIcon
              color={
                state?.routeNames[state?.index] === 'AccountScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Cá nhân
            </Sys.Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (isPos) {
    return (
      <View
        style={{
          position: 'absolute',
          bottom: insets.bottom + 5,
          left: 5,
        }}>
        <View
          style={{
            backgroundColor: 'white',
            borderRadius: 30,
            height: 60,
            width: width - 10,
            borderWidth: 1,
            borderColor: '#********',
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
          }}>
          <TouchableOpacity
            onPress={() => navigation.navigate('HomeScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <HomeIcon
              color={
                state?.routeNames[state?.index] === 'HomeScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Trang chủ
            </Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('ReportOwnerScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <ChartIcon
              color={
                state?.routeNames[state?.index] === 'ReportOwnerScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Báo cáo
            </Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() => {
              navigation.navigate('MessageSupportScreen');
            }}
            style={{flex: 4, alignItems: 'center'}}>
            <View
              style={{
                position: 'absolute',
                top: -60,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Image
                style={{
                  position: 'absolute',
                  zIndex: 2,
                }}
                width={36}
                height={36}
                source={require('../icons/SendIcon.png')}
              />
              <PlaneMenuIcon />
            </View>
            <View style={{position: 'absolute', bottom: -24}}>
              <Sys.Text
                style={{
                  fontSize: 10,
                  color: '#848484',
                }}>
                Phản ánh
              </Sys.Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('NotificationScreen');
            }}
            style={{flex: 3, alignItems: 'center'}}>
            <BellMenuIcon
              color={
                state?.routeNames[state?.index] === 'MessageSupportScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Thông báo
            </Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('AccountScreen')}
            style={{flex: 3, alignItems: 'center'}}>
            <UserIcon
              color={
                state?.routeNames[state?.index] === 'AccountScreen'
                  ? '#F5000D'
                  : '#848484'
              }
            />
            <Sys.Text
              style={{
                fontSize: 12,
                color: '#848484',
              }}>
              Cá nhân
            </Sys.Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return <View />;
};

export default HomeMenu;
