import React, {useEffect, useRef, useState} from 'react';
import {
  Animated,
  Dimensions,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {VictoryPie} from 'victory-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Sys from '../../../components/Sys';
import SysFetch from '../../../services/fetch';
import KPISellSkeleton from './KPISellSkeleton';
import {formatLargeNumber, formatMoney} from '../../../services/util';
import {useSelector} from 'react-redux';
import {AccountSelectors} from '../../account/services/account.slice';
import Color from '../../../components/theme/Color';
import THEME from '../../../components/theme/theme';

const {width} = Dimensions.get('window');

const calculatePercentage = (actual, total) => {
  return actual && total > 0 ? ((actual / total) * 100).toFixed(0) : 0;
};

const KPISell = ({
  year = undefined,
  showDetail = true,
  quarter = undefined,
}) => {
  const view = useSelector(AccountSelectors.view);
  const [loading, setLoading] = useState(true);
  const [kpiData, setKpiData] = useState({
    name: 'KPI Doanh thu bán hàng',
    totalTicketSold: 0,
    totalRevenue: 0,
    totalActualRevenue: 0,
    provinceRevenues: [],
    districtRevenues: [],
  });
  const [isExpand, setIsExpand] = useState(false);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const expandAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const fetchKpiData = async () => {
      try {
        const response = await SysFetch.get('revplan/user/revenue-kpis', {
          year,
          quarter,
        });

        setKpiData({
          name: response.name,
          totalTicketSold: response.totalTicketSold,
          totalRevenue: response.totalRevenue || 0,
          totalActualRevenue: response.totalActualRevenue,
          provinceRevenues: response.provinceRevenues,
          districtRevenues: response.districtRevenues,
        });
      } catch (error) {
        console.error('Error fetching KPI data:', error);
      } finally {
        setTimeout(() => {
          setLoading(false);
          // Start entrance animation
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 600,
              useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
              toValue: 1,
              tension: 50,
              friction: 8,
              useNativeDriver: true,
            }),
          ]).start();
        }, 300);
      }
    };

    fetchKpiData();
  }, [year, quarter]);

  if (loading) {
    return <KPISellSkeleton />;
  }

  const detailData = kpiData.provinceRevenues.concat(kpiData.districtRevenues);

  const percentage = calculatePercentage(
    kpiData.totalActualRevenue,
    kpiData.totalRevenue,
  );
  const pieChartData = [
    {x: 1, y: Number(percentage)},
    {x: 2, y: 100 - Number(percentage)},
  ];

  const toggleExpand = () => {
    const toValue = isExpand ? 0 : 1;
    setIsExpand(!isExpand);

    Animated.spring(expandAnim, {
      toValue,
      tension: 100,
      friction: 8,
      useNativeDriver: false,
    }).start();
  };
  return (
    <Animated.View
      style={[
        modernStyles.container,
        {
          opacity: fadeAnim,
          transform: [{scale: scaleAnim}],
        },
      ]}>
      <View style={modernStyles.mainCard}>
        {/* Header */}
        <View style={modernStyles.header}>
          <View style={modernStyles.titleContainer}>
            <View style={modernStyles.titleIconContainer}>
              <MaterialCommunityIcons
                name="currency-usd"
                size={18}
                color={Color.green}
              />
            </View>
            <View style={modernStyles.titleTextContainer}>
              <Sys.Text style={modernStyles.titleMain}>
                KPI Doanh thu bán hàng
              </Sys.Text>
              <Sys.Text style={modernStyles.titleSub}>
                {quarter ? `Quý ${quarter}` : 'Tổng quan'}{' '}
                {year && `năm ${year}`}
              </Sys.Text>
            </View>
          </View>
        </View>

        {/* Content */}
        <View style={modernStyles.content}>
          {/* Stats */}
          <View style={modernStyles.statsContainer}>
            <View style={modernStyles.statRow}>
              <Sys.Text style={[modernStyles.statNumber, {color: Color.green}]}>
                {formatMoney(kpiData.totalActualRevenue)}
              </Sys.Text>
              <Sys.Text style={modernStyles.statLabel}>DT thực hiện</Sys.Text>
            </View>

            <View style={modernStyles.statRow}>
              <Sys.Text
                style={[modernStyles.statNumber, {color: Color.orange}]}>
                {kpiData.totalTicketSold.toLocaleString()}
              </Sys.Text>
              <Sys.Text style={modernStyles.statLabel}>Vé bán</Sys.Text>
            </View>

            <View style={modernStyles.statRow}>
              <Sys.Text style={[modernStyles.statNumber, {color: Color.blue}]}>
                {formatMoney(
                  kpiData.totalActualRevenue / kpiData.totalTicketSold || 0,
                )}
              </Sys.Text>
              <Sys.Text style={modernStyles.statLabel}>DT/vé bán</Sys.Text>
            </View>
          </View>
          {/* Chart */}
          <View style={modernStyles.chartContainer}>
            <View style={modernStyles.chartWrapper}>
              <VictoryPie
                cornerRadius={25}
                width={180}
                height={180}
                data={[
                  {x: 1, y: 1},
                  {x: 2, y: 0},
                ]}
                colorScale={['#E8F4FD', 'transparent']}
                innerRadius={60}
                startAngle={-100}
                endAngle={100}
                style={{labels: {fill: 'none'}}}
              />
              <View style={modernStyles.chartProgress}>
                <VictoryPie
                  cornerRadius={25}
                  width={180}
                  height={180}
                  data={pieChartData}
                  colorScale={[Color.green, '#E8F4FD']}
                  innerRadius={60}
                  startAngle={-100}
                  endAngle={100}
                  style={{labels: {fill: 'none'}}}
                />
              </View>

              <View style={modernStyles.chartCenter}>
                <Sys.Text style={modernStyles.percentage}>
                  {percentage && `${formatLargeNumber(percentage)}%`}
                </Sys.Text>
              </View>

              <View style={modernStyles.chartLabels}>
                <Sys.Text style={modernStyles.chartLabelLeft}>0</Sys.Text>
                <Sys.Text style={modernStyles.chartLabelRight}>
                  {formatMoney(kpiData.totalRevenue)}
                </Sys.Text>
              </View>
            </View>
          </View>
        </View>
      </View>
      {showDetail &&
      ['branch.owner'].indexOf(view) < 0 &&
      detailData.length > 0 ? (
        <View>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={toggleExpand}
            style={[
              modernStyles.expandButton,
              {backgroundColor: isExpand ? Color.blue : Color.orange},
            ]}>
            <Sys.Text style={modernStyles.expandButtonText}>
              Chi tiết ({detailData.length})
            </Sys.Text>
            <MaterialCommunityIcons
              name={isExpand ? 'chevron-up' : 'chevron-down'}
              size={20}
              color="white"
            />
          </TouchableOpacity>
          <Animated.View
            style={[
              modernStyles.listContainer,
              {
                maxHeight: expandAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 400],
                }),
                opacity: expandAnim,
              },
            ]}>
            <FlatList
              nestedScrollEnabled
              showsVerticalScrollIndicator={false}
              data={detailData}
              renderItem={({item, index}) => {
                const detailPercentage = calculatePercentage(
                  item.actual_revenue,
                  item.revenue_amount,
                );
                const detailPieChartData = [
                  {x: 1, y: Number(detailPercentage)},
                  {x: 2, y: 100 - Number(detailPercentage)},
                ];
                return (
                  <View style={modernStyles.listItem}>
                    <View style={modernStyles.listItemContent}>
                      <View style={modernStyles.listItemHeader}>
                        <Sys.Text style={modernStyles.listItemTitle}>
                          {item.name}
                        </Sys.Text>
                        <View style={modernStyles.listItemPercentage}>
                          <Sys.Text style={modernStyles.percentageText}>
                            {detailPercentage}%
                          </Sys.Text>
                        </View>
                      </View>

                      <View style={modernStyles.listItemStats}>
                        <View style={modernStyles.statItemRow}>
                          <Sys.Text style={modernStyles.statItemLabel}>
                            Kế hoạch:
                          </Sys.Text>
                          <Sys.Text
                            style={[
                              modernStyles.statItemValue,
                              {color: Color.orange},
                            ]}>
                            {formatMoney(item.revenue_amount)}
                          </Sys.Text>
                        </View>

                        <View style={modernStyles.statItemRow}>
                          <Sys.Text style={modernStyles.statItemLabel}>
                            Thực hiện:
                          </Sys.Text>
                          <Sys.Text
                            style={[
                              modernStyles.statItemValue,
                              {color: Color.green},
                            ]}>
                            {formatMoney(item.actual_revenue)}
                          </Sys.Text>
                        </View>

                        <View style={modernStyles.statItemRow}>
                          <Sys.Text style={modernStyles.statItemLabel}>
                            Số vé bán:
                          </Sys.Text>
                          <Sys.Text
                            style={[
                              modernStyles.statItemValue,
                              {color: Color.blue},
                            ]}>
                            {item?.net_tickets_sold?.toLocaleString()}
                          </Sys.Text>
                        </View>
                      </View>
                    </View>
                  </View>
                );
              }}
              keyExtractor={(item, index) => `${index}-one-record`}
            />
          </Animated.View>
        </View>
      ) : (
        <View />
      )}
    </Animated.View>
  );
};

// Modern Styles - Simplified
const modernStyles = StyleSheet.create({
  container: {
    marginHorizontal: 10,
    marginBottom: 15,
  },
  mainCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },

  // Header
  header: {
    padding: 16,
    paddingBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(144, 187, 59, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  titleTextContainer: {
    flex: 1,
  },
  titleMain: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.Color.text,
    lineHeight: 20,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  titleSub: {
    fontSize: 13,
    fontWeight: '500',
    color: Color.green,
    lineHeight: 16,
    marginTop: 1,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
  },

  // Content
  content: {
    flexDirection: 'row',
    padding: 16,
    paddingTop: 0,
  },

  // Stats
  statsContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  statRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    justifyContent: 'space-between',
  },
  statNumber: {
    fontSize: 12,
    fontWeight: '700',
    textAlign: 'left',
    flex: 1,
  },
  statLabel: {
    fontSize: 12,
    color: Color.gray,
    textAlign: 'right',
    flex: 1,
  },

  // Chart
  chartContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartWrapper: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartProgress: {
    position: 'absolute',
  },
  chartCenter: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    top: 70,
  },
  percentage: {
    fontSize: 20,
    fontWeight: '700',
    color: Color.green,
  },
  chartLabels: {
    position: 'absolute',
    bottom: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: 120,
  },
  chartLabelLeft: {
    fontSize: 11,
    color: Color.gray,
  },
  chartLabelRight: {
    fontSize: 11,
    color: Color.green,
    fontWeight: '600',
  },

  // Expand Button
  expandButton: {
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderRadius: 12,
    marginTop: 8,
  },
  expandButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },

  // List
  listContainer: {
    overflow: 'hidden',
    backgroundColor: '#f5f5f5',
  },
  listItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 8,
    marginHorizontal: 12,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  listItemContent: {
    flex: 1,
  },
  listItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  listItemTitle: {
    color: Color.blue,
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 20,
    flex: 1,
  },
  listItemPercentage: {
    backgroundColor: Color.green,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  percentageText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '700',
  },
  listItemStats: {
    gap: 6,
  },
  statItemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statItemLabel: {
    fontSize: 12,
    color: Color.gray,
    flex: 1,
  },
  statItemValue: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'right',
  },
});

export default KPISell;
