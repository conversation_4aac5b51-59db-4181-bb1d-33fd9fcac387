import React, {useEffect, useState, useRef} from 'react';
import {FlatList, TouchableOpacity, View, StyleSheet, Animated, Dimensions} from 'react-native';
import {VictoryPie} from 'victory-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Sys from '../../../components/Sys';
import SysFetch from '../../../services/fetch';
import KPISellSkeleton from './KPISellSkeleton';
import DownIcon from '../icons/DownIcon';
import UpIcon from '../icons/UpIcon';
import {formatLargeNumber, formatMoney} from '../../../services/util';
import {useSelector} from 'react-redux';
import {AccountSelectors} from '../../account/services/account.slice';
import Color from '../../../components/theme/Color';
import THEME from '../../../components/theme/theme';

const {width} = Dimensions.get('window');

const calculatePercentage = (actual, total) => {
  return actual && total > 0 ? ((actual / total) * 100).toFixed(0) : 0;
};

const KPISell = ({
  year = undefined,
  showDetail = true,
  quarter = undefined,
}) => {
  const view = useSelector(AccountSelectors.view);
  const [loading, setLoading] = useState(true);
  const [kpiData, setKpiData] = useState({
    name: 'KPI Doanh thu bán hàng',
    totalTicketSold: 0,
    totalRevenue: 0,
    totalActualRevenue: 0,
    provinceRevenues: [],
    districtRevenues: [],
  });
  const [isExpand, setIsExpand] = useState(false);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const expandAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const fetchKpiData = async () => {
      try {
        const response = await SysFetch.get('revplan/user/revenue-kpis', {
          year,
          quarter,
        });

        setKpiData({
          name: response.name,
          totalTicketSold: response.totalTicketSold,
          totalRevenue: response.totalRevenue || 0,
          totalActualRevenue: response.totalActualRevenue,
          provinceRevenues: response.provinceRevenues,
          districtRevenues: response.districtRevenues,
        });
      } catch (error) {
        console.error('Error fetching KPI data:', error);
      } finally {
        setTimeout(() => {
          setLoading(false);
          // Start entrance animation
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 600,
              useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
              toValue: 1,
              tension: 50,
              friction: 8,
              useNativeDriver: true,
            }),
          ]).start();
        }, 300);
      }
    };

    fetchKpiData();
  }, [year, quarter]);

  if (loading) {
    return <KPISellSkeleton />;
  }

  const detailData = kpiData.provinceRevenues.concat(kpiData.districtRevenues);

  const percentage = calculatePercentage(
    kpiData.totalActualRevenue,
    kpiData.totalRevenue,
  );
  const pieChartData = [
    {x: 1, y: Number(percentage)},
    {x: 2, y: 100 - Number(percentage)},
  ];

  const toggleExpand = () => {
    const toValue = isExpand ? 0 : 1;
    setIsExpand(!isExpand);

    Animated.spring(expandAnim, {
      toValue,
      tension: 100,
      friction: 8,
      useNativeDriver: false,
    }).start();
  };
  return (
    <Animated.View
      style={[
        modernStyles.container,
        {
          opacity: fadeAnim,
          transform: [{scale: scaleAnim}],
        },
      ]}>
      <View style={modernStyles.mainCard}>
        {/* Header */}
        <View style={modernStyles.header}>
          <View style={modernStyles.titleContainer}>
            <View style={modernStyles.titleIconContainer}>
              <MaterialCommunityIcons
                name="currency-usd"
                size={18}
                color={Color.green}
              />
            </View>
            <View style={modernStyles.titleTextContainer}>
              <Sys.Text style={modernStyles.titleMain}>
                KPI Doanh thu bán hàng
              </Sys.Text>
              <Sys.Text style={modernStyles.titleSub}>
                {quarter ? `Quý ${quarter}` : 'Tổng quan'} {year && `năm ${year}`}
              </Sys.Text>
            </View>
          </View>
        </View>

        {/* Content */}
        <View style={modernStyles.content}>
          {/* Stats */}
          <View style={modernStyles.statsContainer}>
            <View style={modernStyles.statRow}>
              <Sys.Text style={[modernStyles.statNumber, {color: Color.green}]}>
                {formatMoney(kpiData.totalActualRevenue)}
              </Sys.Text>
              <Sys.Text style={modernStyles.statLabel}>
                DT thực hiện
              </Sys.Text>
            </View>

            <View style={modernStyles.statRow}>
              <Sys.Text style={[modernStyles.statNumber, {color: Color.orange}]}>
                {kpiData.totalTicketSold.toLocaleString()}
              </Sys.Text>
              <Sys.Text style={modernStyles.statLabel}>
                Vé bán
              </Sys.Text>
            </View>

            <View style={modernStyles.statRow}>
              <Sys.Text style={[modernStyles.statNumber, {color: Color.blue}]}>
                {formatMoney(
                  kpiData.totalActualRevenue / kpiData.totalTicketSold || 0,
                )}
              </Sys.Text>
              <Sys.Text style={modernStyles.statLabel}>
                DT/vé bán
              </Sys.Text>
            </View>
          </View>
          {/* Chart */}
          <View style={modernStyles.chartContainer}>
            <View style={modernStyles.chartWrapper}>
              <VictoryPie
                cornerRadius={25}
                width={180}
                height={180}
                data={[
                  {x: 1, y: 1},
                  {x: 2, y: 0},
                ]}
                colorScale={['#E8F4FD', 'transparent']}
                innerRadius={60}
                startAngle={-100}
                endAngle={100}
                style={{labels: {fill: 'none'}}}
              />
              <View style={modernStyles.chartProgress}>
                <VictoryPie
                  cornerRadius={25}
                  width={180}
                  height={180}
                  data={pieChartData}
                  colorScale={[Color.green, '#E8F4FD']}
                  innerRadius={60}
                  startAngle={-100}
                  endAngle={100}
                  style={{labels: {fill: 'none'}}}
                />
              </View>

              <View style={modernStyles.chartCenter}>
                <Sys.Text style={modernStyles.percentage}>
                  {percentage && `${formatLargeNumber(percentage)}%`}
                </Sys.Text>
              </View>

              <View style={modernStyles.chartLabels}>
                <Sys.Text style={modernStyles.chartLabelLeft}>0</Sys.Text>
                <Sys.Text style={modernStyles.chartLabelRight}>
                  {formatMoney(kpiData.totalRevenue)}
                </Sys.Text>
              </View>
            </View>
          </View>
        </View>
      </View>
      {showDetail &&
      ['branch.owner'].indexOf(view) < 0 &&
      detailData.length > 0 ? (
        <View>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => setIsExpand(!isExpand)}
            style={{
              backgroundColor: isExpand ? '#0062FF' : '#EF7721',
              padding: 8,
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
            }}>
            <Sys.Text
              style={{
                color: 'white',
                lineHeight: 16,
                fontWeight: '600',
              }}>
              Chi tiết
            </Sys.Text>
            {isExpand ? <UpIcon /> : <DownIcon />}
          </TouchableOpacity>
          {isExpand ? (
            <View
              style={{
                backgroundColor: '#E7E7E7',
                paddingVertical: 10,
              }}>
              <FlatList
                nestedScrollEnabled
                showsVerticalScrollIndicator={false}
                data={detailData}
                renderItem={({item, index}) => {
                  const detailPercentage = calculatePercentage(
                    item.actual_revenue,
                    item.revenue_amount,
                  );
                  const detailPieChartData = [
                    {x: 1, y: Number(detailPercentage)},
                    {x: 2, y: 100 - Number(detailPercentage)},
                  ];
                  return (
                    <View
                      style={{
                        backgroundColor: '#FFFFFF99',
                        marginBottom: 10,
                        padding: 10,
                        borderRadius: 10,
                        marginHorizontal: 10,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          position: 'relative',
                          flex: 1,
                          width: '100%',
                        }}>
                        {/*  <View
                      style={{
                        backgroundColor: '#0062FF',
                        width: 48,
                        height: 48,
                        borderRadius: 50,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <View>
                        <Sys.Text
                          style={{
                            color: 'white',
                            fontSize: 12,
                            fontWeight: '700',
                            lineHeight: 15,
                          }}>
                          {item.province_id || item.district_id}
                        </Text>
                      </View>
                    </View>*/}

                        <View
                          style={{
                            marginLeft: 15,
                            flex: 1,
                          }}>
                          <View>
                            <Sys.Text
                              style={{
                                color: '#0062FF',
                                fontWeight: '700',
                                lineHeight: 16,
                              }}>
                              {item.name}{' '}
                            </Sys.Text>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              marginTop: 4,
                            }}>
                            <Sys.Text
                              style={{
                                lineHeight: 16,
                                fontSize: 12,
                                // marginLeft: 4,
                                flex: 1,
                              }}>
                              Kế hoạch: {formatMoney(item.revenue_amount)}
                            </Sys.Text>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              marginTop: 4,
                            }}>
                            <Sys.Text
                              style={{
                                lineHeight: 16,
                                fontSize: 12,
                                // marginLeft: 4,
                                flex: 1,
                              }}>
                              Thực hiện: {formatMoney(item.actual_revenue)}
                            </Sys.Text>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              marginTop: 4,
                            }}>
                            <Sys.Text
                              style={{
                                lineHeight: 16,
                                fontSize: 12,
                                // marginLeft: 4,
                                flex: 1,
                              }}>
                              Số vé bán:{' '}
                              {item?.net_tickets_sold?.toLocaleString()}
                            </Sys.Text>
                          </View>
                        </View>
                        <View
                          style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            position: 'absolute',
                            right: -40,
                            top: -40,
                          }}>
                          <VictoryPie
                            cornerRadius={10}
                            width={200}
                            height={200}
                            data={detailPieChartData}
                            colorScale={['#0062FF', '#E2E2EA']}
                            innerRadius={40}
                            startAngle={-100}
                            endAngle={100}
                            style={{
                              labels: {fill: 'none'},
                            }}
                          />
                          <Sys.Text
                            style={{
                              position: 'absolute',
                              color: '#0062FF',
                              fontSize: 10,
                            }}>
                            {detailPercentage && `${detailPercentage}%`}
                          </Sys.Text>
                        </View>
                        {/*<View
                      style={{
                        position: 'absolute',
                        bottom: 0,
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        width: '100%',
                        paddingHorizontal: 10,
                        flexDirection: 'row',
                      }}>
                      <View style={{}}>
                        <Sys.Text
                          style={{
                            fontSize: 12,
                            lineHeight: 16,
                            color: '#848484',
                          }}>
                          0
                        </Sys.Text>
                      </View>
                      <View style={{}}>
                        <Sys.Text
                          style={{
                            fontSize: 12,
                            lineHeight: 16,
                            color: '#0062FF',
                          }}>
                          {formatMoney(item.revenue_amount)}
                        </Sys.Text>
                      </View>
                    </View>*/}
                      </View>
                    </View>
                  );
                }}
                keyExtractor={(item, index) => `${index}-one-record`}
              />
            </View>
          ) : (
            <></>
          )}
        </View>
      ) : (
        <View />
      )}
    </View>
  );
};

export default KPISell;
