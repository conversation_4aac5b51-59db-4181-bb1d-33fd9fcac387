import React, {useEffect, useState} from 'react';
import {FlatList, TouchableOpacity, View} from 'react-native';
import {VictoryPie} from 'victory-native';
import Sys from '../../../components/Sys';
import SysFetch from '../../../services/fetch';
import KPISellSkeleton from './KPISellSkeleton';
import DownIcon from '../icons/DownIcon';
import UpIcon from '../icons/UpIcon';
import {formatLargeNumber, formatMoney} from '../../../services/util';
import {useSelector} from 'react-redux';
import {AccountSelectors} from '../../account/services/account.slice';

const calculatePercentage = (actual, total) => {
  return actual && total > 0 ? ((actual / total) * 100).toFixed(0) : 0;
};

const KPISell = ({
  year = undefined,
  showDetail = true,
  quarter = undefined,
}) => {
  const view = useSelector(AccountSelectors.view);
  const [loading, setLoading] = useState(true);
  const [kpiData, setKpiData] = useState({
    name: '<PERSON><PERSON>h thu bán hàng',
    totalTicketSold: 0,
    totalRevenue: 0,
    totalActualRevenue: 0,
    provinceRevenues: [],
    districtRevenues: [],
  });
  const [isExpand, setIsExpand] = useState(false);

  useEffect(() => {
    const fetchKpiData = async () => {
      try {
        const response = await SysFetch.get('revplan/user/revenue-kpis', {
          year,
          quarter,
        });

        setKpiData({
          name: response.name,
          totalTicketSold: response.totalTicketSold,
          totalRevenue: response.totalRevenue || 0,
          totalActualRevenue: response.totalActualRevenue,
          provinceRevenues: response.provinceRevenues,
          districtRevenues: response.districtRevenues,
        });
      } catch (error) {
        console.error('Error fetching KPI data:', error);
      } finally {
        setTimeout(() => {
          setLoading(false);
        }, 300);
      }
    };

    fetchKpiData();
  }, [year, quarter]);

  if (loading) {
    return <KPISellSkeleton />;
  }

  const detailData = kpiData.provinceRevenues.concat(kpiData.districtRevenues);

  const percentage = calculatePercentage(
    kpiData.totalActualRevenue,
    kpiData.totalRevenue,
  );
  const pieChartData = [
    {x: 1, y: Number(percentage)},
    {x: 2, y: 100 - Number(percentage)},
  ];
  return (
    <View
      style={{
        marginHorizontal: 10,
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 15,
      }}>
      <View
        style={{
          backgroundColor: 'white',
          padding: 10,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
          }}>
          <View>
            <Sys.Text
              style={{
                lineHeight: 16,
                fontWeight: '600',
                marginBottom: 10,
              }}>
              {kpiData.name}
            </Sys.Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                color: '#90BB3B',
                marginBottom: 5,
              }}>
              {formatMoney(kpiData.totalActualRevenue)}
            </Sys.Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
                marginBottom: 5,
              }}>
              DT thực hiện
            </Sys.Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                color: '#EF7721',
                marginBottom: 5,
              }}>
              {kpiData.totalTicketSold.toLocaleString()}
            </Sys.Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
              }}>
              Vé bán
            </Sys.Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                color: '#001451',
              }}>
              {formatMoney(
                kpiData.totalActualRevenue / kpiData.totalTicketSold,
              )}
            </Sys.Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
              }}>
              DT/vé bán
            </Sys.Text>
          </View>
        </View>
        <View
          style={{
            flex: 1,
            height: 100,
            alignItems: 'center',
          }}>
          <View
            style={{
              position: 'absolute',
              top: -30,
            }}>
            <VictoryPie
              cornerRadius={25}
              width={200}
              height={200}
              data={pieChartData}
              colorScale={['#0062FF', '#E2E2EA']}
              innerRadius={65}
              startAngle={-100}
              endAngle={100}
              style={{
                labels: {fill: 'none'},
              }}
            />
          </View>
          <View
            style={{
              position: 'absolute',
              bottom: 24,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Sys.Text
              style={{
                color: '#0062FF',
                fontSize: 18,
              }}>
              {percentage && `${formatLargeNumber(percentage)}%`}
            </Sys.Text>
          </View>
          <View
            style={{
              position: 'absolute',
              bottom: 0,
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%',
              paddingHorizontal: 10,
              flexDirection: 'row',
            }}>
            <View style={{}}>
              <Sys.Text
                style={{
                  fontSize: 12,
                  lineHeight: 16,
                  color: '#848484',
                }}>
                0
              </Sys.Text>
            </View>
            <View style={{}}>
              <Sys.Text
                style={{
                  fontSize: 12,
                  lineHeight: 16,
                  color: '#0062FF',
                }}>
                {formatMoney(kpiData.totalRevenue)}
              </Sys.Text>
            </View>
          </View>
        </View>
      </View>
      {showDetail &&
      ['branch.owner'].indexOf(view) < 0 &&
      detailData.length > 0 ? (
        <View>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => setIsExpand(!isExpand)}
            style={{
              backgroundColor: isExpand ? '#0062FF' : '#EF7721',
              padding: 8,
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
            }}>
            <Sys.Text
              style={{
                color: 'white',
                lineHeight: 16,
                fontWeight: '600',
              }}>
              Chi tiếts
            </Sys.Text>
            {isExpand ? <UpIcon /> : <DownIcon />}
          </TouchableOpacity>
          {isExpand ? (
            <View
              style={{
                backgroundColor: '#E7E7E7',
                paddingVertical: 10,
              }}>
              <FlatList
                nestedScrollEnabled
                showsVerticalScrollIndicator={false}
                data={detailData}
                renderItem={({item, index}) => {
                  const detailPercentage = calculatePercentage(
                    item.actual_revenue,
                    item.revenue_amount,
                  );
                  const detailPieChartData = [
                    {x: 1, y: Number(detailPercentage)},
                    {x: 2, y: 100 - Number(detailPercentage)},
                  ];
                  return (
                    <View
                      style={{
                        backgroundColor: '#FFFFFF99',
                        marginBottom: 10,
                        padding: 10,
                        borderRadius: 10,
                        marginHorizontal: 10,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          position: 'relative',
                          flex: 1,
                          width: '100%',
                        }}>
                        {/*  <View
                      style={{
                        backgroundColor: '#0062FF',
                        width: 48,
                        height: 48,
                        borderRadius: 50,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <View>
                        <Sys.Text
                          style={{
                            color: 'white',
                            fontSize: 12,
                            fontWeight: '700',
                            lineHeight: 15,
                          }}>
                          {item.province_id || item.district_id}
                        </Text>
                      </View>
                    </View>*/}

                        <View
                          style={{
                            marginLeft: 15,
                            flex: 1,
                          }}>
                          <View>
                            <Sys.Text
                              style={{
                                color: '#0062FF',
                                fontWeight: '700',
                                lineHeight: 16,
                              }}>
                              {item.name}{' '}
                            </Sys.Text>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              marginTop: 4,
                            }}>
                            <Sys.Text
                              style={{
                                lineHeight: 16,
                                fontSize: 12,
                                // marginLeft: 4,
                                flex: 1,
                              }}>
                              Kế hoạch: {formatMoney(item.revenue_amount)}
                            </Sys.Text>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              marginTop: 4,
                            }}>
                            <Sys.Text
                              style={{
                                lineHeight: 16,
                                fontSize: 12,
                                // marginLeft: 4,
                                flex: 1,
                              }}>
                              Thực hiện: {formatMoney(item.actual_revenue)}
                            </Sys.Text>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              marginTop: 4,
                            }}>
                            <Sys.Text
                              style={{
                                lineHeight: 16,
                                fontSize: 12,
                                // marginLeft: 4,
                                flex: 1,
                              }}>
                              Số vé bán:{' '}
                              {item?.net_tickets_sold?.toLocaleString()}
                            </Sys.Text>
                          </View>
                        </View>
                        <View
                          style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            position: 'absolute',
                            right: -40,
                            top: -40,
                          }}>
                          <VictoryPie
                            cornerRadius={10}
                            width={200}
                            height={200}
                            data={detailPieChartData}
                            colorScale={['#0062FF', '#E2E2EA']}
                            innerRadius={40}
                            startAngle={-100}
                            endAngle={100}
                            style={{
                              labels: {fill: 'none'},
                            }}
                          />
                          <Sys.Text
                            style={{
                              position: 'absolute',
                              color: '#0062FF',
                              fontSize: 10,
                            }}>
                            {detailPercentage && `${detailPercentage}%`}
                          </Sys.Text>
                        </View>
                        {/*<View
                      style={{
                        position: 'absolute',
                        bottom: 0,
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        width: '100%',
                        paddingHorizontal: 10,
                        flexDirection: 'row',
                      }}>
                      <View style={{}}>
                        <Sys.Text
                          style={{
                            fontSize: 12,
                            lineHeight: 16,
                            color: '#848484',
                          }}>
                          0
                        </Sys.Text>
                      </View>
                      <View style={{}}>
                        <Sys.Text
                          style={{
                            fontSize: 12,
                            lineHeight: 16,
                            color: '#0062FF',
                          }}>
                          {formatMoney(item.revenue_amount)}
                        </Sys.Text>
                      </View>
                    </View>*/}
                      </View>
                    </View>
                  );
                }}
                keyExtractor={(item, index) => `${index}-one-record`}
              />
            </View>
          ) : (
            <></>
          )}
        </View>
      ) : (
        <View />
      )}
    </View>
  );
};

export default KPISell;
