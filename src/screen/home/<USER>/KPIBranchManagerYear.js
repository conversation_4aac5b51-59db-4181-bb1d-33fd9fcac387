import {useIsFocused} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {VictoryPie} from 'victory-native';
import {AppActions} from '../../../app.slice';
import {formatVND} from '../../../services/util';
import {AccountSelectors} from '../../account/services/account.slice';
import Sys from '../../../components/Sys';

const KPIBranchManagerYear = ({currentDate = undefined}) => {
  const isFocused = useIsFocused();
  const [kpi, setKpi] = useState(undefined);
  const dispatch = useDispatch();
  const {isBranchManager} = useSelector(state => state.account);
  const globalFilter = useSelector(AccountSelectors.globalFilter);

  const currentDateFilter = currentDate
    ? moment(currentDate, 'YYYY-MM')
    : isBranchManager && globalFilter.date
    ? moment(globalFilter.date, 'YYYY-MM-DD')
    : moment().subtract(1, 'days');

  useEffect(() => {
    if (isFocused) {
      const year = currentDateFilter.year();
      dispatch(
        AppActions.fetch({
          onFail: rs => {
            console.log(rs.response.data);
          },
          onSuccess: rs => {
            setKpi(rs.data.stats);
          },
          url: 'revplan/branch/kpis/year',
          params: {
            year,
          },
        }),
      );
    }
  }, [isFocused, globalFilter]);

  const cal = () => {
    try {
      return kpi?.actual_revenue / kpi?.revenue_amount;
    } catch (error) {
      return '--';
    }
  };

  return (
    <View
      style={{
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 15,
        flex: 1,
      }}>
      <View
        style={{
          backgroundColor: 'white',
          padding: 10,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
          }}>
          <View>
            <Sys.Text
              style={{
                textAlign: 'center',
                fontSize: 10,
                fontWeight: '500',
              }}>
              Lũy kế DT năm {currentDateFilter.year()} (tỷ đồng)
            </Sys.Text>
          </View>
          <View
            style={{
              flex: 1,
              height: 100,
              alignItems: 'center',
            }}>
            <View
              style={{
                position: 'absolute',
                top: -30,
              }}>
              <VictoryPie
                cornerRadius={25}
                width={200} // Đặt chiều rộng của biểu đồ
                height={200} // Đặt chiều cao của biểu đồ
                data={[
                  {x: 1, y: 1}, // Phần dữ liệu chính
                  {x: 2, y: 0}, // Đặt thành 0 để không hiển thị
                ]}
                colorScale={['#E2E2EA', 'transparent']} // Màu xám cho phần nhỏ
                innerRadius={65}
                startAngle={-100} // Bắt đầu từ 90 độ, nơi phần xanh kết thúc
                endAngle={100} // Kết thúc cùng một điểm, tạo bo tròn
                style={{
                  labels: {fill: 'none'},
                }}
              />
            </View>
            <View
              style={{
                position: 'absolute',
                top: -30,
              }}>
              <VictoryPie
                cornerRadius={25}
                width={200} // Đặt chiều rộng của biểu đồ
                height={200} // Đặt chiều cao của biểu đồ
                data={[
                  {x: 1, y: cal()}, // Phần dữ liệu chính
                  {x: 2, y: 1 - Number(cal())}, // Đặt thành 0 để không hiển thị
                ]}
                colorScale={['#0062FF', '#E2E2EA']} // Màu xám cho phần nhỏ
                innerRadius={65}
                startAngle={-100} // Bắt đầu từ 90 độ, nơi phần xanh kết thúc
                endAngle={100} // Kết thúc cùng một điểm, tạo bo tròn
                style={{
                  labels: {fill: 'none'},
                }}
              />
            </View>
            <View
              style={{
                position: 'absolute',
                bottom: 24,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Sys.Text
                style={{
                  fontSize: 16,
                  fontWeight: '600',
                }}>
                {formatVND(Math.ceil(kpi?.actual_revenue / 1000000000))}
              </Sys.Text>
              <Sys.Text
                style={{
                  color: 'grey',
                  fontWeight: '500',
                  fontSize: 10,
                }}>
                {cal() === Infinity
                  ? '∞'
                  : isNaN(cal())
                  ? '--'
                  : `${(cal() * 100).toFixed(0)}%`}
              </Sys.Text>
            </View>
            <View
              style={{
                position: 'absolute',
                bottom: 0,
                alignItems: 'center',
                justifyContent: 'space-between',
                width: '100%',
                paddingHorizontal: 10,
                flexDirection: 'row',
              }}>
              <View style={{}}>
                <Sys.Text
                  style={{
                    fontSize: 12,
                    lineHeight: 16,
                    color: '#848484',
                  }}>
                  0 Tỷ
                </Sys.Text>
              </View>
              <View style={{}}>
                <Sys.Text
                  style={{
                    fontSize: 12,
                    lineHeight: 16,
                    color: '#0062FF',
                  }}>
                  <Sys.Text
                    style={{
                      fontWeight: '600',
                    }}>
                    {formatVND(kpi?.revenue_amount / 1000000000)}
                  </Sys.Text>{' '}
                  Tỷ
                </Sys.Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default KPIBranchManagerYear;
