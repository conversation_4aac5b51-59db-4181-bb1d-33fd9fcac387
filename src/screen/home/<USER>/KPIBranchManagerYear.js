import {useIsFocused} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useState, useRef} from 'react';
import {View, StyleSheet, Animated, Dimensions} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {VictoryPie} from 'victory-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {AppActions} from '../../../app.slice';
import {formatVND} from '../../../services/util';
import {AccountSelectors} from '../../account/services/account.slice';
import Color from '../../../components/theme/Color';
import THEME from '../../../components/theme/theme';
import Sys from '../../../components/Sys';

const {width} = Dimensions.get('window');

const KPIBranchManagerYear = ({currentDate = undefined}) => {
  const isFocused = useIsFocused();
  const [kpi, setKpi] = useState(undefined);
  const dispatch = useDispatch();
  const {isBranchManager} = useSelector(state => state.account);
  const globalFilter = useSelector(AccountSelectors.globalFilter);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  const currentDateFilter = currentDate
    ? moment(currentDate, 'YYYY-MM')
    : isBranchManager && globalFilter.date
    ? moment(globalFilter.date, 'YYYY-MM-DD')
    : moment().subtract(1, 'days');

  useEffect(() => {
    if (isFocused) {
      const year = currentDateFilter.year();
      dispatch(
        AppActions.fetch({
          onFail: rs => {
            console.log(rs.response.data);
          },
          onSuccess: rs => {
            setKpi(rs.data.stats);
            // Start entrance animation
            setTimeout(() => {
              Animated.parallel([
                Animated.timing(fadeAnim, {
                  toValue: 1,
                  duration: 600,
                  useNativeDriver: true,
                }),
                Animated.spring(scaleAnim, {
                  toValue: 1,
                  tension: 50,
                  friction: 8,
                  useNativeDriver: true,
                }),
              ]).start();
            }, 100);
          },
          url: 'revplan/branch/kpis/year',
          params: {
            year,
          },
        }),
      );
    }
  }, [isFocused, globalFilter]);

  const cal = () => {
    try {
      return kpi?.actual_revenue / kpi?.revenue_amount;
    } catch (error) {
      return '--';
    }
  };

  return (
    <Animated.View
      style={[
        modernStyles.container,
        {
          opacity: fadeAnim,
          transform: [{scale: scaleAnim}],
        },
      ]}>
      <View style={modernStyles.mainCard}>
        {/* Header */}
        <View style={modernStyles.header}>
          <View style={modernStyles.titleContainer}>
            <View style={modernStyles.titleIconContainer}>
              <MaterialCommunityIcons
                name="calendar-range"
                size={16}
                color={Color.blue}
              />
            </View>
            <View style={modernStyles.titleTextContainer}>
              <Sys.Text style={modernStyles.titleMain}>
                DT Năm {currentDateFilter.year()}
              </Sys.Text>
              <Sys.Text style={modernStyles.titleSub}>
                (tỷ đồng)
              </Sys.Text>
            </View>
          </View>
        </View>

        {/* Chart */}
        <View style={modernStyles.chartContainer}>
          <View style={modernStyles.chartWrapper}>
            <VictoryPie
              cornerRadius={25}
              width={140}
              height={140}
              data={[
                {x: 1, y: 1},
                {x: 2, y: 0},
              ]}
              colorScale={['#E8F4FD', 'transparent']}
              innerRadius={45}
              startAngle={-100}
              endAngle={100}
              style={{labels: {fill: 'none'}}}
            />
            <View style={modernStyles.chartProgress}>
              <VictoryPie
                cornerRadius={25}
                width={140}
                height={140}
                data={[
                  {x: 1, y: cal() || 0},
                  {x: 2, y: 1 - Number(cal() || 0)},
                ]}
                colorScale={[Color.blue, '#E8F4FD']}
                innerRadius={45}
                startAngle={-100}
                endAngle={100}
                style={{labels: {fill: 'none'}}}
              />
            </View>

            <View style={modernStyles.chartCenter}>
              <Sys.Text style={modernStyles.chartValue}>
                {formatVND(Math.ceil(kpi?.actual_revenue / 1000000000)) || 0}
              </Sys.Text>
              <Sys.Text style={modernStyles.chartPercentage}>
                {cal() === Infinity
                  ? '∞'
                  : isNaN(cal())
                  ? '--'
                  : `${(cal() * 100).toFixed(0)}%`}
              </Sys.Text>
            </View>

            <View style={modernStyles.chartLabels}>
              <Sys.Text style={modernStyles.chartLabelLeft}>0 Tỷ</Sys.Text>
              <Sys.Text style={modernStyles.chartLabelRight}>
                {formatVND(kpi?.revenue_amount / 1000000000)} Tỷ
              </Sys.Text>
            </View>
          </View>
        </View>
      </View>
    </Animated.View>
  );
};

// Modern Styles - Compact for small cards
const modernStyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 4,
    overflow: 'hidden',
  },

  // Header
  header: {
    padding: 12,
    paddingBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(0, 98, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  titleTextContainer: {
    flex: 1,
  },
  titleMain: {
    fontSize: 13,
    fontWeight: '700',
    color: THEME.Color.text,
    lineHeight: 16,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  titleSub: {
    fontSize: 11,
    fontWeight: '500',
    color: Color.blue,
    lineHeight: 14,
    marginTop: 1,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
  },

  // Chart
  chartContainer: {
    alignItems: 'center',
    paddingBottom: 12,
  },
  chartWrapper: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartProgress: {
    position: 'absolute',
  },
  chartCenter: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    top: 50,
  },
  chartValue: {
    fontSize: 16,
    fontWeight: '700',
    color: Color.blue,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  chartPercentage: {
    fontSize: 10,
    color: Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginTop: 2,
  },
  chartLabels: {
    position: 'absolute',
    bottom: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: 100,
  },
  chartLabelLeft: {
    fontSize: 10,
    color: Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
  },
  chartLabelRight: {
    fontSize: 10,
    color: Color.blue,
    fontWeight: '600',
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
  },
});

export default KPIBranchManagerYear;
