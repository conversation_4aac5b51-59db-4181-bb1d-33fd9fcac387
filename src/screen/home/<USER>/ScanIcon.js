import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

function ScanIcon(props) {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={26}
      height={26}
      viewBox="0 0 26 26"
      fill="none"
      {...props}>
      <Path
        d="M8.273 23.636H3.545a1.182 1.182 0 01-1.181-1.181v-4.728a1.182 1.182 0 10-2.364 0v4.727A3.545 3.545 0 003.545 26h4.728a1.182 1.182 0 000-2.364zm16.545-7.09a1.182 1.182 0 00-1.182 1.181v4.727a1.181 1.181 0 01-1.181 1.182h-4.728a1.182 1.182 0 100 2.364h4.727A3.545 3.545 0 0026 22.454v-4.727a1.182 1.182 0 00-1.182-1.181zM22.454 0h-4.727a1.182 1.182 0 100 2.364h4.727a1.182 1.182 0 011.182 1.181v4.728a1.182 1.182 0 002.364 0V3.545A3.545 3.545 0 0022.454 0zM1.183 9.455a1.182 1.182 0 001.182-1.182V3.545a1.182 1.182 0 011.181-1.181h4.728a1.182 1.182 0 000-2.364H3.545A3.545 3.545 0 000 3.545v4.728a1.182 1.182 0 001.182 1.182zm9.454-4.728H5.91A1.182 1.182 0 004.727 5.91v4.727a1.182 1.182 0 001.182 1.182h4.727a1.181 1.181 0 001.182-1.182V5.91a1.182 1.182 0 00-1.182-1.182zM9.455 9.455H7.09V7.09h2.364v2.364zm5.909 2.363h4.727a1.182 1.182 0 001.182-1.182V5.91a1.182 1.182 0 00-1.182-1.182h-4.727a1.182 1.182 0 00-1.182 1.182v4.727a1.181 1.181 0 001.182 1.182zm1.181-4.727h2.364v2.364h-2.363V7.09zm-5.909 7.09H5.91a1.182 1.182 0 00-1.182 1.183v4.727a1.182 1.182 0 001.182 1.182h4.727a1.182 1.182 0 001.182-1.182v-4.727a1.181 1.181 0 00-1.182-1.182zM9.455 18.91H7.09v-2.363h2.364v2.363zm5.909-1.182a1.181 1.181 0 001.181-1.181 1.182 1.182 0 000-2.364h-1.181a1.181 1.181 0 00-1.182 1.182v1.181a1.181 1.181 0 001.182 1.182zm4.727-3.545a1.182 1.182 0 00-1.182 1.182v3.545a1.182 1.182 0 000 2.364h1.182a1.182 1.182 0 001.182-1.182v-4.727a1.182 1.182 0 00-1.182-1.182zm-4.727 4.727a1.181 1.181 0 100 2.363 1.181 1.181 0 000-2.363z"
        fill="#F5000D"
      />
    </Svg>
  );
}

export default ScanIcon;
