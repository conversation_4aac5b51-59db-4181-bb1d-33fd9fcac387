import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

function StoreIcon(props) {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={27}
      height={26}
      viewBox="0 0 27 26"
      fill="none"
      {...props}>
      <Path
        d="M19.143 0H7.796C6.29 0 5.536 0 4.93.375c-.605.373-.942 1.047-1.616 2.395L1.555 7.217c-.407 1.028-.762 2.238-.078 3.107A2.507 2.507 0 005.952 8.77a2.506 2.506 0 005.012 0 2.506 2.506 0 105.012 0 2.506 2.506 0 105.012 0 2.505 2.505 0 004.474 1.551c.685-.868.329-2.078-.077-3.106l-1.76-4.446C22.951 1.422 22.615.748 22.01.375 21.404 0 20.65 0 19.143 0z"
        fill="#F5000D"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23.494 24.12H26A.94.94 0 0126 26H.94a.94.94 0 110-1.88h2.506V13.158c.932 0 1.795-.291 2.506-.786.735.513 1.61.787 2.506.786.932 0 1.795-.291 2.506-.786.735.513 1.61.787 2.506.786.932 0 1.795-.291 2.506-.786.735.513 1.61.787 2.506.786.932 0 1.796-.291 2.506-.786.735.513 1.61.787 2.506.786V24.12zm-13.157 0h6.265v-3.445c0-1.172 0-1.757-.251-2.193a1.88 1.88 0 00-.688-.69c-.436-.25-1.022-.25-2.193-.25-1.172 0-1.757 0-2.193.25a1.88 1.88 0 00-.688.69c-.252.436-.252 1.021-.252 2.193v3.446z"
        fill="#F5000D"
      />
    </Svg>
  );
}

export default StoreIcon;
