import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const CheckInMenuIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={21}
    height={20}
    fill="none"
    {...props}>
    <Path
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      d="M16.583 6.6v4.292c0 2.567-1.466 3.667-3.666 3.667H5.592c-.375 0-.734-.034-1.067-.109a3.243 3.243 0 0 1-.592-.158c-1.25-.467-2.008-1.55-2.008-3.4V6.6c0-2.566 1.467-3.666 3.667-3.666h7.325c1.866 0 3.208.791 3.566 2.6.059.333.1.675.1 1.066Z"
    />
    <Path
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      d="M19.084 9.1v4.292c0 2.567-1.466 3.667-3.666 3.667H8.093c-.617 0-1.175-.084-1.659-.267-.991-.367-1.666-1.125-1.908-2.342.333.075.692.109 1.067.109h7.325c2.2 0 3.666-1.1 3.666-3.667V6.6c0-.391-.033-.741-.1-1.066 1.584.333 2.6 1.45 2.6 3.566Z"
    />
    <Path
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      d="M9.249 10.95a2.2 2.2 0 1 0 0-4.4 2.2 2.2 0 0 0 0 4.4ZM4.483 6.917v3.667M14.018 6.917v3.667"
    />
  </Svg>
);
export default CheckInMenuIcon;
