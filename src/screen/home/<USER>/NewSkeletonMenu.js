import React from 'react';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {Dimensions} from 'react-native'; // Import Rect for SVG elements
const {width} = Dimensions.get('window');

// Loader Component for Categories using react-content-loader/native
const CategoryLoader = props => (
  <ContentLoader
    speed={2}
    width={width - 40}
    height={500}
    viewBox={`0 0 ${width - 40} 500`}
    backgroundColor="#f5f5f5"
    foregroundColor="#dbdbdb"
    {...props}>
    <Rect x="12" y="35" rx="0" ry="0" width="6" height="246" />
    <Rect x="14" y="34" rx="0" ry="0" width="408" height="6" />
    <Rect x="416" y="34" rx="0" ry="0" width="6" height="246" />
    <Rect x="12" y="276" rx="0" ry="0" width="408" height="6" />
    <Rect x="150" y="53" rx="6" ry="6" width="127" height="15" />
    <Rect x="37" y="77" rx="7" ry="7" width="361" height="139" />
    <Rect x="58" y="225" rx="0" ry="0" width="316" height="8" />
    <Rect x="86" y="238" rx="0" ry="0" width="267" height="8" />
    <Rect x="58" y="252" rx="0" ry="0" width="316" height="8" />
  </ContentLoader>
);

// Loader Component for News using react-content-loader/native
const NewsLoader = props => (
  <ContentLoader
    speed={2}
    width={width - 40}
    height={160}
    viewBox={`0 0 ${width - 40} 160`}
    backgroundColor="#f5f5f5"
    foregroundColor="#dbdbdb"
    {...props}>
    <Rect x="10" y="20" rx="5" ry="5" width="150" height="15" />
    <Rect x="10" y="50" rx="5" ry="5" width="300" height="15" />
    <Rect x="10" y="80" rx="5" ry="5" width="250" height="15" />
    <Rect x="10" y="110" rx="5" ry="5" width="200" height="15" />
  </ContentLoader>
);

export {CategoryLoader, NewsLoader};
