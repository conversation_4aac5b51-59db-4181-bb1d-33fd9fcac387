import {useIsFocused} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useRef, useState} from 'react';
import {Animated, Dimensions, StyleSheet, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {VictoryPie} from 'victory-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {AppActions} from '../../../app.slice';
import {formatVND} from '../../../services/util';
import {AccountSelectors} from '../../account/services/account.slice';
import {PosActions} from '../../sale-location/services/pos.slice';
import Color from '../../../components/theme/Color';
import THEME from '../../../components/theme/theme';
import Sys from '../../../components/Sys';

const {width} = Dimensions.get('window');

const KPIBranchManager = () => {
  const isFocused = useIsFocused();
  const profile = useSelector(AccountSelectors.profile);
  const [data, setData] = useState([]);
  const [kpi, setKpi] = useState(undefined);
  const dispatch = useDispatch();

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  const globalFilter = useSelector(AccountSelectors.globalFilter);

  useEffect(() => {
    if (isFocused) {
      const date = moment().subtract(1, 'days');
      const quarter = Math.ceil((date.month() + 1) / 3);
      const year = date.year();
      dispatch(
        AppActions.fetch({
          onFail: rs => {
            console.log(rs.response.data);
          },
          onSuccess: rs => {
            setKpi(rs.data.stats);
            // Start entrance animation
            setTimeout(() => {
              Animated.parallel([
                Animated.timing(fadeAnim, {
                  toValue: 1,
                  duration: 600,
                  useNativeDriver: true,
                }),
                Animated.spring(scaleAnim, {
                  toValue: 1,
                  tension: 50,
                  friction: 8,
                  useNativeDriver: true,
                }),
              ]).start();
            }, 100);
          },
          url: 'revplan/branch/kpis/quarter',
          params: {
            year,
            quarter,
          },
        }),
      );
    }
  }, [data, isFocused, globalFilter]);

  useEffect(() => {
    if (!!profile && isFocused) {
      dispatch(
        PosActions.getPosByURL({
          url: `pos?plan_month=${moment()
            .subtract(1, 'days')
            .format('YYYY-MM')}&checkin_status=1&staff_id=${
            profile?.id
          }&pageSize=-1&with[]=agency&with[]=staff`,
          onSuccess: rs => {
            setData(rs);
          },
        }),
      );
    }
  }, [profile, isFocused]);

  const cal = () => {
    try {
      return kpi?.actual_revenue / kpi?.revenue_amount;
    } catch (error) {
      return '--';
    }
  };

  return (
    <Animated.View
      style={[
        modernStyles.container,
        {
          opacity: fadeAnim,
          transform: [{scale: scaleAnim}],
        },
      ]}>
      <View style={modernStyles.mainCard}>
        {/* Header */}
        <View style={modernStyles.header}>
          <View style={modernStyles.titleContainer}>
            <View style={modernStyles.titleIconContainer}>
              <MaterialCommunityIcons
                name="trending-up"
                size={18}
                color={Color.orange}
              />
            </View>
            <View style={modernStyles.titleTextContainer}>
              <Sys.Text style={modernStyles.titleMain}>
                KPI doanh thu quý {Math.ceil((moment().month() + 1) / 3)}
              </Sys.Text>
              <Sys.Text style={modernStyles.titleSub}>
                Lũy kế tới {moment().subtract(1, 'days').format('DD/MM/YYYY')}
              </Sys.Text>
            </View>
          </View>
        </View>

        {/* Content */}
        <View style={modernStyles.content}>
          {/* Stats */}
          <View style={modernStyles.statsContainer}>
            <View style={modernStyles.statRow}>
              <Sys.Text style={[modernStyles.statNumber, {color: Color.green}]}>
                {formatVND(kpi?.actual_revenue)}
              </Sys.Text>
              <Sys.Text style={modernStyles.statLabel}>DT thực tế</Sys.Text>
            </View>

            <View style={modernStyles.statRow}>
              <Sys.Text
                style={[modernStyles.statNumber, {color: Color.orange}]}>
                {formatVND(kpi?.revenue_amount)}
              </Sys.Text>
              <Sys.Text style={modernStyles.statLabel}>DT kế hoạch</Sys.Text>
            </View>

            <View style={modernStyles.statRow}>
              <Sys.Text style={[modernStyles.statNumber, {color: Color.blue}]}>
                {formatVND(
                  (kpi?.revenue_amount || 0) - (kpi?.actual_revenue || 0),
                )}
              </Sys.Text>
              <Sys.Text style={modernStyles.statLabel}>Còn lại</Sys.Text>
            </View>
          </View>
          {/* Chart */}
          <View style={modernStyles.chartContainer}>
            <View style={modernStyles.chartWrapper}>
              <VictoryPie
                cornerRadius={25}
                width={180}
                height={180}
                data={[
                  {x: 1, y: 1},
                  {x: 2, y: 0},
                ]}
                colorScale={['#E8F4FD', 'transparent']}
                innerRadius={60}
                startAngle={-100}
                endAngle={100}
                style={{labels: {fill: 'none'}}}
              />
              <View style={modernStyles.chartProgress}>
                <VictoryPie
                  cornerRadius={25}
                  width={180}
                  height={180}
                  data={[
                    {x: 1, y: cal() || 0},
                    {x: 2, y: 1 - Number(cal() || 0)},
                  ]}
                  colorScale={[Color.orange, '#E8F4FD']}
                  innerRadius={60}
                  startAngle={-100}
                  endAngle={100}
                  style={{labels: {fill: 'none'}}}
                />
              </View>

              <View style={modernStyles.chartCenter}>
                <Sys.Text style={modernStyles.percentage}>
                  {cal() === Infinity ? '∞' : (cal() * 100).toFixed(0)}%
                </Sys.Text>
              </View>

              <View style={modernStyles.chartLabels}>
                <Sys.Text style={modernStyles.chartLabelLeft}>0 Tỷ</Sys.Text>
                <Sys.Text style={modernStyles.chartLabelRight}>
                  {formatVND(kpi?.revenue_amount / 1000000000)} Tỷ
                </Sys.Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    </Animated.View>
  );
};

// Modern Styles - Simplified
const modernStyles = StyleSheet.create({
  container: {
    marginHorizontal: 10,
    marginBottom: 15,
  },
  mainCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },

  // Header
  header: {
    padding: 16,
    paddingBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(239, 119, 33, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  titleTextContainer: {
    flex: 1,
  },
  titleMain: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.Color.text,
    lineHeight: 20,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  titleSub: {
    fontSize: 13,
    fontWeight: '500',
    color: Color.orange,
    lineHeight: 16,
    marginTop: 1,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
  },

  // Content
  content: {
    flexDirection: 'row',
    padding: 16,
    paddingTop: 0,
  },

  // Stats
  statsContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  statRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    justifyContent: 'space-between',
  },
  statNumber: {
    fontSize: 12,
    fontWeight: '700',
    textAlign: 'left',
    flex: 1,
  },
  statLabel: {
    fontSize: 12,
    color: Color.gray,
    textAlign: 'right',
    flex: 1,
  },

  // Chart
  chartContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartWrapper: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartProgress: {
    position: 'absolute',
  },
  chartCenter: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    top: 70,
  },
  percentage: {
    fontSize: 20,
    fontWeight: '700',
    color: Color.orange,
  },
  chartLabels: {
    position: 'absolute',
    bottom: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: 120,
  },
  chartLabelLeft: {
    fontSize: 11,
    color: Color.gray,
  },
  chartLabelRight: {
    fontSize: 11,
    color: Color.orange,
    fontWeight: '600',
  },
});

export default KPIBranchManager;
