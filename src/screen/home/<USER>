import React, {useEffect} from 'react';
import {ScrollView, useWindowDimensions, View} from 'react-native';
import Sys from '../../components/Sys';

import HeaderHome from './components/Header.Home';
import KPIMonth from './components/KPIMonth';
import KPISell from './components/KPISell';
import HomeRevenue from './components/Home.Revenue';
import HomeCountList from './components/Home.CountList';
import HomePlan from './components/Home.Plan';
import {useDispatch, useSelector} from 'react-redux';
import {PosActions} from '../sale-location/services/pos.slice';
import NewsComponent from './components/NewsComponent';
import OwnerHome from '../owner/OwnerHome';
import BranchManagerHomeScreen from '../branch-manager-home/BranchManagerHomeScreen';
import HomeKPI from './components/Home.KPI';
import KPIBranchManagerYearQuarter from './components/KPIBranchManagerYearQuarter';
import KPIBranchManagerYear from './components/KPIBranchManagerYear';

const HomeScreen = () => {
  const dispatch = useDispatch();

  const {isStaff, isAgent, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);
  const {width} = useWindowDimensions();
  useEffect(() => {
    dispatch(PosActions.getPosType());
  }, []);

  return (
    <Sys.Container
      hasHeader
      hasFooter
      footerColor="#F1F1F1"
      backgroundColor="#001451"
      headerColor="#001451">
      <HeaderHome />
      <ScrollView
        showsVerticalScrollIndicator={false}
        nestedScrollEnabled={true}
        style={{
          flex: 1,
          backgroundColor: '#F1F1F1',
        }}>
        <HomeKPI />
        <View
          style={{
            backgroundColor: '#F1F1F1',
            flex: 1,
          }}>
          {isGuest && <NewsComponent />}
          {isPos && <OwnerHome />}
          {isBranchManager && <BranchManagerHomeScreen />}
          {isAgent && <OwnerHome />}
          {(isBranchOwner || isStaff) && (
            <>
              <KPIMonth />
              {isBranchOwner ? (
                <View
                  style={[
                    {
                      width: width - 20,
                      borderRadius: 20,
                      marginHorizontal: 10,
                      marginTop: 10,
                      gap: 10,
                      flexDirection: 'row',
                    },
                  ]}>
                  <KPIBranchManagerYearQuarter />
                  <KPIBranchManagerYear />
                </View>
              ) : (
                <KPISell />
              )}

              <HomeRevenue />
              <HomePlan />
              <HomeCountList />
              <View style={{height: 110}} />
            </>
          )}
        </View>
      </ScrollView>
    </Sys.Container>
  );
};

export default HomeScreen;
