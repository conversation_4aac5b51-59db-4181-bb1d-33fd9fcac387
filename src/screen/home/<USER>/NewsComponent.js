import React, {useCallback, useEffect, useState} from 'react';
import {
  FlatList,
  TouchableOpacity,
  useColorScheme,
  useWindowDimensions,
  View,
} from 'react-native';
import RenderHTML from 'react-native-render-html';
import Sys from '../../../components/Sys';
import SysFetch from '../../../services/fetch';
import {useIsFocused} from '@react-navigation/native';
import moment from 'moment';
import Color from '../../../components/theme/Color';
import {CategoryLoader, NewsLoader} from './NewSkeletonMenu';

// Optimized NewsComponent
const NewsComponent = () => {
  const [category, setCategory] = useState([]);
  const [loading, setLoading] = useState(true);
  const isFocused = useIsFocused();

  // Fetch category only when the screen is focused
  useEffect(() => {
    if (isFocused) {
      fetchCategory();
    }
  }, [isFocused]);

  const fetchCategory = useCallback(async () => {
    try {
      const rs = await SysFetch.get('categories/announcements');
      setCategory(rs);
    } catch (error) {
      setCategory([]);
    } finally {
      setLoading(false);
    }
  }, []);

  return (
    <View style={{paddingHorizontal: 10, flex: 1}}>
      {loading ? (
        <CategoryLoader />
      ) : (
        <FlatList
          data={category}
          keyExtractor={item => `${item.id}`}
          renderItem={({item, index}) => <OneCate item={item} index={index} />}
          ListFooterComponent={<View style={{marginBottom: 100}} />}
          showsVerticalScrollIndicator={false}
          initialNumToRender={5}
          maxToRenderPerBatch={5}
          removeClippedSubviews={true}
        />
      )}
    </View>
  );
};

// Optimized OneCate component
const OneCate = React.memo(({item, index}) => {
  const [isShow, setIsShow] = useState(index === 0); // Show the first item by default
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false); // Track loading state

  useEffect(() => {
    if (isShow) {
      fetchData();
    } else {
      setData([]); // Clear data when not showing
    }
  }, [isShow]);

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const rs = await SysFetch.get(
        `announcements/${item?.id}?sort_by=updated_at&sort_order=asc`,
      );
      setData(rs.data);
    } catch (error) {
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [item?.id]);

  return (
    <View>
      <TouchableOpacity
        onPress={() => setIsShow(prevState => !prevState)}
        style={{backgroundColor: '#001451', padding: 10}}>
        <Sys.Text style={{fontWeight: '600', color: 'white'}}>
          {item?.name}
        </Sys.Text>
      </TouchableOpacity>

      {isShow && (
        <>
          {loading ? (
            <NewsLoader />
          ) : (
            <View style={{paddingVertical: 20}}>
              <FlatList
                data={data}
                keyExtractor={item => `${item.id}`} // Use unique ID for keys
                renderItem={({item}) => <OneNews item={item} />}
                showsVerticalScrollIndicator={false}
                getItemLayout={(data, index) => ({
                  length: 150,
                  offset: 150 * index,
                  index,
                })} // Pre-calculate item heights to improve performance
              />
            </View>
          )}
        </>
      )}
    </View>
  );
});

// Optimized OneNews component
const OneNews = React.memo(({item}) => {
  const {width} = useWindowDimensions();
  const colorScheme = useColorScheme();

  const tagsStyles = {
    img: {
      width: width - 60,
      height: 'auto',
    },
    body: {
      backgroundColor: Color.white,
      color: colorScheme === 'dark' ? Color.text : Color.black,
    },
  };
  return (
    <View style={{marginBottom: 40}}>
      <Sys.Text style={{fontWeight: '500'}}>{item.title}</Sys.Text>
      <Sys.Text style={{marginTop: 5, color: '#666', fontSize: 12}}>
        {moment(item?.created_at).format('HH:mm DD/MM/YYYY')}
      </Sys.Text>

      <View
        style={{
          backgroundColor: 'white',
          padding: 10,
          borderRadius: 10,
          marginTop: 10,
        }}>
        <RenderHTML
          tagsStyles={tagsStyles}
          contentWidth={width}
          source={{html: item?.body}}
        />
      </View>
    </View>
  );
});

export default NewsComponent;
