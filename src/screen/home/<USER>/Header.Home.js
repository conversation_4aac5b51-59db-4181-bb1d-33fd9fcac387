import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import {AccountSelectors} from '../../account/services/account.slice';
import DetailedViewModal from '../../device-list/components/DetailedViewModal';
import {
  changeMode,
  changeScreenParent,
  fetchEquipmentStatusStart,
  fetchProducts,
} from '../../device-list/services/inventory.slice';
import {handleModalDetailActionMode2} from '../../device-list/services/utilities';
import {OwnerActions} from '../../owner/services/slice';
import NotiIcon from '../../report/icons/NotiIcon';
import {NotificationActions} from '../../notification/services/notification.slice';
import Color from '../../../components/theme/Color';
import {Front} from '../../../components/theme/FontFamily';

const HeaderHome = () => {
  const profile = useSelector(AccountSelectors.profile);
  const {navigate} = useNavigation();
  const {currentItem} = useSelector(state => state.inventory);
  const dispatch = useDispatch();
  const {view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);
  const isFocused = useIsFocused();
  const [ownerData, setOwnerData] = useState(undefined);
  const [ownerMonthData, setOwnerMonthData] = useState(undefined);
  const {
    notifications: {unRead},
  } = useSelector(state => state.notify);
  useEffect(() => {
    if (isFocused) {
      // dispatch(AccountActions.getProfile());
      dispatch(NotificationActions.getNotification());
      dispatch(changeMode(2));
      dispatch(changeScreenParent('HomeScreen'));
      dispatch(fetchProducts());
      dispatch(fetchEquipmentStatusStart());
      dispatch(
        OwnerActions.getOwnerCurrentDay({
          onSuccess: rs => {
            if (rs?.data?.items.length > 0) {
              setOwnerData(rs?.data?.items[0]);
            }
          },
        }),
      );
      dispatch(
        OwnerActions.getOwnerCurrentMonth({
          onSuccess: rs => {
            if (rs?.data?.items.length > 0) {
              setOwnerMonthData(rs?.data?.items[0]);
            }
          },
        }),
      );
    }
  }, [isFocused]);

  const onPressBell = () => {
    navigate('NotificationScreen', {
      initialTab: 'Thông báo',
    });
  };

  return (
    <View style={{paddingHorizontal: 20, paddingVertical: 10}}>
      <View>
        <View style={styles.topContainer}>
          <Image
            style={styles.avatar}
            source={{
              uri: profile?.avatar,
            }}
          />
          <View style={styles.topContainerMain}>
            <Sys.Text style={styles.name}>
              <Sys.Text
                style={{
                  fontWeight: '400',
                  fontSize: 13,
                  color: Color.white,
                }}>
                Xin chào,{' '}
              </Sys.Text>
              {profile?.name}
            </Sys.Text>
            <Sys.Text style={styles.infor}>{`${
              profile?.role?.display_name || ''
            }`}</Sys.Text>
          </View>
          {/*{isBranchManager && (*/}
          {/*  <TouchableOpacity*/}
          {/*    style={{*/}
          {/*      marginRight: 10,*/}
          {/*    }}*/}
          {/*    onPress={() => {*/}
          {/*      navigate('BranchManagerFilterScreen');*/}
          {/*    }}>*/}
          {/*    <FilterIcon />*/}
          {/*  </TouchableOpacity>*/}
          {/*)}*/}
          {(isStaff || isBranchOwner || isBranchManager) && (
            <TouchableOpacity onPress={onPressBell}>
              <NotiIcon />
              {unRead > 0 && (
                <Sys.Text
                  style={[
                    Front.bold,
                    {
                      fontSize: 12,
                      color: Color.red,
                      backgroundColor: Color.white,
                      overflow: 'hidden',
                      borderRadius: 12.5,
                      position: 'absolute',
                      top: -15,
                      right: -10,
                      width: 25,
                      height: 25,
                      paddingTop: 3,
                      paddingLeft: unRead > 99 ? 3 : unRead < 10 ? 10 : 5,
                    },
                  ]}>
                  {unRead > 99 ? '99+' : unRead}
                </Sys.Text>
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>

      <DetailedViewModal
        item={currentItem}
        page={'SaleInformationScreen'}
        onAction={handleModalDetailActionMode2}
      />
    </View>
  );
};

export default HeaderHome;

const styles = StyleSheet.create({
  oneMenuName: {
    fontSize: 12,
    fontWeight: '500',
    lineHeight: 20,
    color: '#0062FF',
    marginTop: 4,
  },
  oneMenu: {flex: 1, alignItems: 'center', justifyContent: 'center'},
  flex1: {
    flex: 1,
  },
  topContainerMain: {
    flex: 1,
  },
  container: {
    marginBottom: 60,
  },
  top: {
    backgroundColor: '#001451',
    paddingHorizontal: 20,
  },
  topContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bottom: {
    backgroundColor: 'white',
    margin: 10,
    borderRadius: 20,
    bottom: -55,
    position: 'absolute',
    zIndex: 9,
    padding: 10,
  },
  bottomContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bottomTitle: {
    lineHeight: 20,
    fontSize: 12,
    color: '#848484',
  },
  bottomContent: {
    color: '#001451',
    fontSize: 18,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  name: {
    lineHeight: 28,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    color: 'white',
    fontSize: 18,
  },
  infor: {
    fontSize: 12,
    color: 'white',
    lineHeight: 20,
  },
  avatar: {
    height: 65,
    width: 65,
    borderRadius: 50,
    marginRight: 20,
    backgroundColor: '#00000010',
  },
});
