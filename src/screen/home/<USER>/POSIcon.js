import * as React from 'react';
import Svg, {Circle, Path} from 'react-native-svg';
const POSIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={88}
    height={88}
    fill="none"
    {...props}>
    <Circle cx={44} cy={44} r={41.5} stroke="#FFDCDE" strokeWidth={5} />
    <Circle cx={43.5} cy={43.5} r={34.5} fill="#F5000D" />
    <Path
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="M43.3 28.913v7.254M27.913 44.3h7.254M43.3 59.687v-7.254M58.687 44.3h-7.254m15.167 0c0 12.969-10.55 23.3-23.3 23.3S20 57.05 20 44.3C20 31.331 30.55 21 43.3 21s23.3 10.551 23.3 23.3Z"
    />
  </Svg>
);
export default POSIcon;
