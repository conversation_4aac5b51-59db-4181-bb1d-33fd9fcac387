import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const HomeIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={26}
    height={25}
    fill="none"
    {...props}>
    <Path
      stroke={props.color}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="m3.557 9.102 9.37-7.08 9.37 7.08v11.124c0 .537-.22 1.051-.61 1.43-.391.38-.92.593-1.473.593H5.64a2.114 2.114 0 0 1-1.472-.593c-.39-.379-.61-.893-.61-1.43V9.102Z"
    />
    <Path
      stroke={props.color}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="M9.804 22.249V12.136h6.246v10.113"
    />
  </Svg>
);
export default HomeIcon;
