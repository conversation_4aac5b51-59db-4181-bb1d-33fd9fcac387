import {useIsFocused, useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useRef, useState} from 'react';
import {
  Animated,
  Dimensions,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {VictoryPie} from 'victory-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {AppActions} from '../../../app.slice';
import {AccountSelectors} from '../../account/services/account.slice';
import LocationSmallIcon from '../icons/LocationSmallIcon';
import StoreSmallIcon from '../icons/StoreSmallIcon';
import {openGoogleMaps} from '../../checkout/Checkout.Infomation';
import KPISellSkeleton from './KPISellSkeleton';
import Color from '../../../components/theme/Color';
import THEME from '../../../components/theme/theme';
import {sortByDateAscending} from '../../../services/util';
import Sys from '../../../components/Sys';

const {width} = Dimensions.get('window');

const KPIMonth = ({isBranchManager = false}) => {
  const {navigate} = useNavigation();
  const {view, isStaff} = useSelector(state => state.account);
  const isFocused = useIsFocused();
  const [isExpand, setIsExpand] = useState(false);
  const profile = useSelector(AccountSelectors.profile);
  const [data, setData] = useState([]);
  const [kpi, setKpi] = useState(undefined);
  const [loading, setLoading] = useState(true);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const expandAnim = useRef(new Animated.Value(0)).current;

  const dispatch = useDispatch();

  useEffect(() => {
    if (isFocused) {
      dispatch(
        AppActions.fetch({
          onFail: rs => {
            console.log(rs.response.data);
          },
          onSuccess: rs => {
            setKpi(rs);
            const sortedItems = sortByDateAscending(rs?.remaining_pos || []);
            setData(sortedItems);
            setTimeout(() => {
              setLoading(false);
              // Start entrance animation
              Animated.parallel([
                Animated.timing(fadeAnim, {
                  toValue: 1,
                  duration: 600,
                  useNativeDriver: true,
                }),
                Animated.spring(scaleAnim, {
                  toValue: 1,
                  tension: 50,
                  friction: 8,
                  useNativeDriver: true,
                }),
              ]).start();
            }, 300);
          },
          url: 'plan/kpi/monthly',
          params: {
            month: moment().format('YYYY-MM'),
          },
        }),
      );
    }
  }, [isFocused]);

  const calculatePercentage = () => {
    return kpi?.in_plan && kpi?.total_plan > 0
      ? ((kpi?.in_plan / kpi?.total_plan) * 100).toFixed(0)
      : 0;
  };

  const toggleExpand = () => {
    const toValue = isExpand ? 0 : 1;
    setIsExpand(!isExpand);

    Animated.spring(expandAnim, {
      toValue,
      tension: 100,
      friction: 8,
      useNativeDriver: false,
    }).start();
  };

  if (loading) {
    return <KPISellSkeleton />;
  }

  return (
    <Animated.View
      style={[
        modernStyles.container,
        {
          opacity: fadeAnim,
          transform: [{scale: scaleAnim}],
        },
      ]}>
      <View style={modernStyles.mainCard}>
        {/* Header */}
        <View style={modernStyles.header}>
          <View style={modernStyles.titleContainer}>
            <View style={modernStyles.titleIconContainer}>
              <MaterialCommunityIcons
                name="chart-line"
                size={18}
                color={Color.blue}
              />
            </View>
            <View style={modernStyles.titleTextContainer}>
              <Sys.Text style={modernStyles.titleMain}>
                KPI ghé thăm ĐBH
              </Sys.Text>
              <Sys.Text style={modernStyles.titleSub}>
                Tháng {moment().format('MM/YYYY')}
              </Sys.Text>
            </View>
          </View>
        </View>

        {/* Content */}
        <View style={modernStyles.content}>
          {/* Stats */}
          <View style={modernStyles.statsContainer}>
            <View style={modernStyles.statRow}>
              <Sys.Text style={[modernStyles.statNumber, {color: Color.green}]}>
                {kpi?.un_plan + kpi?.in_plan}
              </Sys.Text>
              <Sys.Text style={modernStyles.statLabel}>
                điểm đã ghé thăm
              </Sys.Text>
            </View>

            <View style={modernStyles.statRow}>
              <Sys.Text
                style={[modernStyles.statNumber, {color: Color.orange}]}>
                {kpi?.un_plan}
              </Sys.Text>
              <Sys.Text style={modernStyles.statLabel}>
                điểm ngoài kế hoạch
              </Sys.Text>
            </View>

            <View style={modernStyles.statRow}>
              <Sys.Text style={[modernStyles.statNumber, {color: Color.blue}]}>
                {kpi?.total_plan - kpi?.in_plan}
              </Sys.Text>
              <Sys.Text style={modernStyles.statLabel}>điểm cần đi</Sys.Text>
            </View>
          </View>

          {/* Chart */}
          <View style={modernStyles.chartContainer}>
            <View style={modernStyles.chartWrapper}>
              <VictoryPie
                cornerRadius={25}
                width={180}
                height={180}
                data={[
                  {x: 1, y: 1},
                  {x: 2, y: 0},
                ]}
                colorScale={['#E8F4FD', 'transparent']}
                innerRadius={60}
                startAngle={-100}
                endAngle={100}
                style={{labels: {fill: 'none'}}}
              />
              <View style={modernStyles.chartProgress}>
                <VictoryPie
                  cornerRadius={25}
                  width={180}
                  height={180}
                  data={[
                    {x: 1, y: kpi?.in_plan / kpi?.total_plan || 0},
                    {x: 2, y: 1 - (kpi?.in_plan / kpi?.total_plan || 0)},
                  ]}
                  colorScale={[Color.blue, '#E8F4FD']}
                  innerRadius={60}
                  startAngle={-100}
                  endAngle={100}
                  style={{labels: {fill: 'none'}}}
                />
              </View>

              <View style={modernStyles.chartCenter}>
                <Sys.Text style={modernStyles.percentage}>
                  {calculatePercentage()}%
                </Sys.Text>
              </View>

              <View style={modernStyles.chartLabels}>
                <Sys.Text style={modernStyles.chartLabelLeft}>0 ĐBH</Sys.Text>
                <Sys.Text style={modernStyles.chartLabelRight}>
                  {kpi?.total_plan} ĐBH
                </Sys.Text>
              </View>
            </View>
          </View>
        </View>
      </View>
      {data.length > 0 && (
        <View>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={toggleExpand}
            style={[
              modernStyles.expandButton,
              {backgroundColor: isExpand ? Color.blue : Color.orange},
            ]}>
            <Sys.Text style={modernStyles.expandButtonText}>
              Danh sách ĐBH cần đi ({data.length})
            </Sys.Text>
            {isStaff && data[0]?.plan?.plan_status_id && isExpand && (
              <Sys.Text style={modernStyles.expandButtonStatus}>
                ({data[0]?.plan?.plan_status_label})
              </Sys.Text>
            )}
            <MaterialCommunityIcons
              name={isExpand ? 'chevron-up' : 'chevron-down'}
              size={20}
              color="white"
            />
          </TouchableOpacity>
          <Animated.View
            style={[
              modernStyles.listContainer,
              {
                maxHeight: expandAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 350],
                }),
                opacity: expandAnim,
              },
            ]}>
            <FlatList
              nestedScrollEnabled
              showsVerticalScrollIndicator={false}
              data={data}
              renderItem={({item, index}) => {
                return (
                  <TouchableOpacity
                    onPress={() => {
                      if (item) {
                        navigate('SaleInformationScreen', {
                          ...item,
                        });
                      }
                    }}
                    style={modernStyles.listItem}
                    activeOpacity={0.8}>
                    <View style={modernStyles.dateBadge}>
                      <Sys.Text style={modernStyles.dateBadgeDay}>
                        {item?.plan?.date
                          ? moment(item.plan.date, 'YYYY-MM-DD').format('DD')
                          : ''}
                      </Sys.Text>
                      <Sys.Text style={modernStyles.dateBadgeMonth}>
                        {item?.plan?.date
                          ? moment(item.plan.date, 'YYYY-MM-DD').format(
                              'MM-YYYY',
                            )
                          : ''}
                      </Sys.Text>
                    </View>

                    <View style={modernStyles.listItemContent}>
                      <Sys.Text style={modernStyles.listItemTitle}>
                        {item?.posCode}{' '}
                        {item?.staff && (
                          <Sys.Text style={modernStyles.listItemStaff}>
                            ({item?.staff?.name})
                          </Sys.Text>
                        )}
                      </Sys.Text>

                      <View style={modernStyles.listItemRow}>
                        <LocationSmallIcon />
                        <View style={modernStyles.listItemTextContainer}>
                          <Sys.Text style={modernStyles.listItemText}>
                            {item?.address}
                          </Sys.Text>
                          <TouchableOpacity
                            onPress={() => {
                              openGoogleMaps(item?.lat, item?.lon);
                            }}>
                            <Sys.Text style={modernStyles.directionText}>
                              (Chỉ đường)
                            </Sys.Text>
                          </TouchableOpacity>
                        </View>
                      </View>

                      <View style={modernStyles.listItemRow}>
                        <StoreSmallIcon />
                        <Sys.Text style={modernStyles.listItemText}>
                          {item?.agency?.name}
                        </Sys.Text>
                      </View>
                    </View>
                  </TouchableOpacity>
                );
              }}
              keyExtractor={(item, index) => `${index}-one-record`}
            />
          </Animated.View>
        </View>
      )}
    </Animated.View>
  );
};

// Modern Styles - Simplified
const modernStyles = StyleSheet.create({
  container: {
    marginHorizontal: 10,
    marginBottom: 15,
  },
  mainCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },

  // Header
  header: {
    padding: 16,
    paddingBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 98, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  titleTextContainer: {
    flex: 1,
  },
  titleMain: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.Color.text,
    lineHeight: 20,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  titleSub: {
    fontSize: 13,
    fontWeight: '500',
    color: Color.blue,
    lineHeight: 16,
    marginTop: 1,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
  },

  // Content
  content: {
    flexDirection: 'row',
    padding: 16,
    paddingTop: 0,
  },

  // Stats
  statsContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  statRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statNumber: {
    fontSize: 14,
    fontWeight: '700',
    width: 28,
    textAlign: 'left',
  },
  statLabel: {
    fontSize: 12,
    color: Color.gray,
    flex: 1,
    marginLeft: 8,
  },

  // Chart
  chartContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartWrapper: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartProgress: {
    position: 'absolute',
  },
  chartCenter: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    top: 70,
  },
  percentage: {
    fontSize: 20,
    fontWeight: '700',
    color: Color.blue,
  },
  chartLabels: {
    position: 'absolute',
    bottom: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: 120,
  },
  chartLabelLeft: {
    fontSize: 11,
    color: Color.gray,
  },
  chartLabelRight: {
    fontSize: 11,
    color: Color.blue,
    fontWeight: '600',
  },

  // Expand Button
  expandButton: {
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderRadius: 12,
    marginTop: 8,
  },
  expandButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  expandButtonStatus: {
    color: 'white',
    fontSize: 12,
    marginRight: 8,
  },

  // List
  listContainer: {
    overflow: 'hidden',
    backgroundColor: '#f5f5f5',
  },
  listItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 8,
    marginHorizontal: 12,
    padding: 12,
    flexDirection: 'row',
    borderRadius: 12,
  },
  dateBadge: {
    backgroundColor: Color.blue,
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  dateBadgeDay: {
    color: 'white',
    fontSize: 12,
    fontWeight: '700',
    lineHeight: 14,
  },
  dateBadgeMonth: {
    color: 'white',
    fontSize: 8,
    fontWeight: '600',
    lineHeight: 10,
  },
  listItemContent: {
    flex: 1,
  },
  listItemTitle: {
    color: Color.blue,
    fontWeight: '700',
    fontSize: 14,
    lineHeight: 18,
  },
  listItemStaff: {
    color: Color.gray,
    fontWeight: '400',
    fontSize: 14,
  },
  listItemRow: {
    flexDirection: 'row',
    marginTop: 4,
    alignItems: 'flex-start',
  },
  listItemTextContainer: {
    flex: 1,
    marginLeft: 4,
  },
  listItemText: {
    fontSize: 12,
    lineHeight: 16,
    color: THEME.Color.text,
  },
  directionText: {
    fontSize: 12,
    color: Color.blue,
    marginTop: 2,
    marginLeft: 4,
  },
});

export default KPIMonth;
