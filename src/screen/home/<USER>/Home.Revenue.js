import {useIsFocused} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useMemo, useState} from 'react';
import {TouchableOpacity, useWindowDimensions, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {
  VictoryAxis,
  VictoryBar,
  VictoryChart,
  VictoryTheme,
  VictoryTooltip,
} from 'victory-native';
import {AppActions} from '../../../app.slice';
import Sys from '../../../components/Sys';
import {formatVND} from '../../../services/util';
import {AccountSelectors} from '../../account/services/account.slice';

const HomeRevenue = () => {
  const dispatch = useDispatch();
  const {width} = useWindowDimensions();
  const profile = useSelector(AccountSelectors.profile);
  const isFocused = useIsFocused();
  const [current, setCurrent] = useState('D');
  const [data, setData] = useState([]);

  useEffect(() => {
    if (isFocused) {
      setData([]);
      if (current === 'D') {
        const endDate = moment().subtract(1, 'days').format('YYYY-MM-DD');
        const startDate = moment().subtract(7, 'days').format('YYYY-MM-DD');

        dispatch(
          AppActions.fetchChartDay({
            onFail: rs => {},
            onSuccess: rs => {
              setData(
                rs?.data
                  .sort((a, b) => {
                    let dateA = moment(a.event_time, 'YYYY-MM-DD').toDate();
                    let dateB = moment(b.event_time, 'YYYY-MM-DD').toDate();
                    return dateA - dateB; // Sắp xếp từ cũ đến mới
                  })
                  .map((x, index) => {
                    console.log(
                      moment(x?.event_time, 'YYYY-MM-DD')
                        .format('dd')
                        .toLocaleUpperCase('vi'),
                    );
                    return {
                      date: moment(x?.event_time, 'YYYY-MM-DD')
                        .format('dd')
                        .toLocaleUpperCase(),
                      amount: x.amount,
                      realDate: moment(x?.event_time, 'YYYY-MM-DD').format(
                        'DD/MM/YYYY',
                      ),
                    };
                  }),
              );
            },
            url: 'report/pos/daily',
            params: {
              start_date: startDate,
              end_date: endDate,
            },
          }),
        );
      }

      if (current === 'H') {
        const currentDate = moment().subtract('d', 1).format('YYYY-MM-DD');

        dispatch(
          AppActions.fetchChartHour({
            onFail: rs => {},
            onSuccess: rs => {
              setData(
                rs?.data
                  .map((x, index) => {
                    return {
                      date: moment(x?.event_time, 'YYYY-MM-DD HH:mm').format(
                        'HH',
                      ),
                      amount: x?.amount,
                    };
                  })
                  .sort((x, y) => x.date - y.date),
              );
            },
            url: `report/pos/hourly?event_date=${currentDate}`,
          }),
        );
      }

      if (current === 'M') {
        const currentYear = moment().format('YYYY');
        const startDate = moment(`${currentYear}-01-01`, 'YYYY-MM-DD').format(
          'YYYY-MM-DD',
        );
        const endDate = moment(`${currentYear}-12-31`, 'YYYY-MM-DD').format(
          'YYYY-MM-DD',
        );

        dispatch(
          AppActions.fetchChartMonth({
            onFail: rs => {},
            onSuccess: rs => {
              setData(
                rs?.data
                  .sort((a, b) => {
                    let dateA = moment(a.event_time, 'MM/YYYY'); // Chuyển đổi về định dạng moment
                    let dateB = moment(b.event_time, 'MM/YYYY');
                    return dateA - dateB; // Sắp xếp theo thời gian từ cũ đến mới
                  })
                  .map((x, index) => {
                    return {
                      date: x?.event_time,
                      amount: x?.amount,
                    };
                  }),
              );
            },
            url: 'report/pos/monthly',
            params: {
              start_date: startDate,
              end_date: endDate,
            },
          }),
        );
      }

      if (current === 'Q') {
        const startDate = moment().format('YYYY-01-01');
        const endDate = moment().format('YYYY-12-30');

        dispatch(
          AppActions.fetchChartQuarter({
            onFail: rs => {},
            onSuccess: rs => {
              setData(
                rs?.data
                  .map((x, index) => {
                    return {
                      date: x?.event_time,
                      amount: x.amount,
                    };
                  })
                  .sort((x, y) => x.date - y.date),
              );
            },
            url: 'report/pos/quarterly',
          }),
        );
      }
    }
  }, [current, profile, isFocused, moment]);

  const labelList = [
    {
      name: 'H',
      value: 2,
    },
    {
      name: 'D',
      value: 3,
    },
    {
      name: 'M',
      value: 4,
    },
    {
      name: 'Q',
      value: 5,
    },
    {
      name: 'Y',
      value: 6,
    },
  ];
  const CustomLabel = ({x, y, text, ...props}) => (
    <View>
      <Sys.Text style={{fontSize: 12, fontWeight: 'bold'}}>{text}</Sys.Text>
    </View>
  );
  const renderChart = useMemo(() => {
    return (
      <VictoryChart width={width} theme={VictoryTheme.material}>
        <VictoryAxis
          style={{
            tickLabels: {fontSize: 10, padding: 10, angle: -45},
          }}
        />
        <VictoryBar
          cornerRadius={{top: 10, bottom: 10}}
          style={{
            data: {
              fill: '#001451',
              borderRadius: 1,
              width: 20,
            },
            grid: {stroke: 'transparent', width: 0},
          }}
          // animate={{
          //   duration: 500,
          //   onLoad: {duration: 500},
          // }}
          labelComponent={
            <VictoryTooltip
              constrainToVisibleArea
              // Tuỳ chỉnh kiểu cho text trong tooltip
              style={{
                fontSize: 14,
                fill: 'black',
                color: 'black',
                textAlign: 'center',
              }}
              // Tuỳ chỉnh kiểu cho khung tooltip
              flyoutStyle={{
                stroke: '#00000050',
                fill: 'white',
              }}
              // Tuỳ chỉnh padding cho tooltip
              flyoutPadding={{top: 10, bottom: 10, left: 15, right: 15}}
              cornerRadius={10} // Độ bo góc của tooltip
              pointerLength={5}
            />
          }
          labels={({datum}) =>
            `${formatVND(datum.amount)}đ${
              datum?.realDate ? `\n${datum?.realDate}` : ''
            }`
          }
          data={data}
          x="date"
          y="amount"
        />
      </VictoryChart>
    );
  }, [data, current]);

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'white',
        borderRadius: 20,
        marginHorizontal: 10,
        marginBottom: 15,
      }}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
          padding: 15,
        }}>
        <View
          style={{
            borderRadius: 12,
            backgroundColor: '#001451',
            justifyContent: 'center',
            alignItems: 'center',
            height: 40,
            width: 110,
          }}>
          <Sys.Text
            style={{
              color: 'white',
              fontWeight: '700',
              letterSpacing: 1,
            }}>
            DOANH THU
          </Sys.Text>
        </View>
        {labelList.map((x, index) => {
          return (
            <TouchableOpacity
              key={`${index}-chart`}
              onPress={() => setCurrent(x?.name)}
              style={{
                borderRadius: 12,
                backgroundColor: x?.name === current ? '#F5000D' : '#0062FF', //F5000D
                justifyContent: 'center',
                alignItems: 'center',
                height: 40,
                width: 40,
              }}>
              <Sys.Text
                style={{
                  color: 'white',
                  fontWeight: '700',
                  letterSpacing: 1,
                }}>
                {x.name}
              </Sys.Text>
            </TouchableOpacity>
          );
        })}
      </View>
      {renderChart}
      <View
        style={{
          marginBottom: 20,
        }}>
        {current === 'D' && (
          <Sys.Text
            style={{
              fontWeight: '600',
              lineHeight: 24,
            }}>
            Báo cáo doanh thu 7 ngày gần nhất
          </Sys.Text>
        )}
        {current === 'H' && (
          <Sys.Text
            style={{
              fontWeight: '600',
              lineHeight: 24,
            }}>
            Báo cáo doanh thu theo giờ
          </Sys.Text>
        )}
        {current === 'M' && (
          <Sys.Text
            style={{
              fontWeight: '600',
              lineHeight: 24,
            }}>
            Báo cáo doanh thu theo tháng
          </Sys.Text>
        )}
        {current === 'Q' && (
          <Sys.Text
            style={{
              fontWeight: '600',
              lineHeight: 24,
            }}>
            Báo cáo doanh thu theo quý
          </Sys.Text>
        )}
      </View>
    </View>
  );
};

export default HomeRevenue;
