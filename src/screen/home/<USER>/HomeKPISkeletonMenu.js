import React from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';
import ContentLoader, {Circle, Rect} from 'react-content-loader/native';

const {width} = Dimensions.get('window');

const HomeKPISkeletonMenu = () => {
  return (
    <View style={styles.container}>
      <ContentLoader
        speed={2}
        width={width - 40}
        height={100}
        viewBox={`0 0 ${width - 40} 100`}
        backgroundColor="#f3f3f3"
        foregroundColor="#ecebeb">
        <View style={styles.flexRow}>
          {/* First Circle Icon + Label */}
          <View style={styles.iconContainer}>
            <Circle cx="50%" cy="40" r="20" />
            <Rect x="25%" y="70" rx="4" ry="4" width="50%" height="10" />
          </View>

          {/* Second Circle Icon + Label */}
          <View style={styles.iconContainer}>
            <Circle cx="50%" cy="40" r="20" />
            <Rect x="25%" y="70" rx="4" ry="4" width="50%" height="10" />
          </View>
          {/* Third Circle Icon + Label */}
          <View style={styles.iconContainer}>
            <Circle cx="50%" cy="40" r="20" />
            <Rect x="25%" y="70" rx="4" ry="4" width="50%" height="10" />
          </View>
        </View>
      </ContentLoader>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    backgroundColor: 'white',
    borderRadius: 10,
    marginHorizontal: 10,
    marginVertical: 10,
  },
  flexRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    width: width / 3 - 20,
  },
});

export default HomeKPISkeletonMenu;
