import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const UserIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={26}
    height={25}
    fill="none"
    {...props}>
    <Path
      stroke={props.color}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="M21.193 21.238v-2.023a3.987 3.987 0 0 0-1.22-2.86 4.227 4.227 0 0 0-2.944-1.185H8.7a4.227 4.227 0 0 0-2.944 1.185 3.987 3.987 0 0 0-1.22 2.86v2.023M12.865 11.124c2.3 0 4.164-1.81 4.164-4.045 0-2.234-1.865-4.045-4.164-4.045-2.3 0-4.165 1.811-4.165 4.045s1.865 4.045 4.165 4.045Z"
    />
  </Svg>
);
export default UserIcon;
