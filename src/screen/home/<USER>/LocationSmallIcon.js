import * as React from 'react';
import Svg, {Circle, Path} from 'react-native-svg';
const LocationSmallIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={18}
    height={19}
    fill="none"
    {...props}>
    <Circle cx={9} cy={9.371} r={8.5} fill="#001451" stroke="#fff" />
    <Path
      fill="#001451"
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      strokeWidth={0.5}
      d="M10.358 7.738A1.6 1.6 0 0 1 8.75 9.345a1.6 1.6 0 0 1-1.606-1.607 1.6 1.6 0 0 1 1.606-1.606c.893 0 1.607.758 1.607 1.606ZM8.796 4.525a3.276 3.276 0 0 0-3.257 3.258c0 1.785 3.257 6.158 3.257 6.158s3.213-4.373 3.213-6.203-1.428-3.213-3.213-3.213Z"
    />
  </Svg>
);
export default LocationSmallIcon;
