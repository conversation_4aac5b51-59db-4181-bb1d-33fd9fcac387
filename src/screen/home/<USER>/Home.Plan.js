import React, {useRef} from 'react';
import {View, StyleSheet, Animated} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Sys from '../../../components/Sys';
import DatePlan from '../../visit-plan/DatePlan';
import Color from '../../../components/theme/Color';
import THEME from '../../../components/theme/theme';

const HomePlan = () => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <Animated.View
      style={[
        modernStyles.container,
        {
          opacity: fadeAnim,
          transform: [{scale: scaleAnim}],
        },
      ]}>
      <View style={modernStyles.mainCard}>
        {/* Header */}
        <View style={modernStyles.header}>
          <View style={modernStyles.titleContainer}>
            <View style={modernStyles.titleIconContainer}>
              <MaterialCommunityIcons
                name="calendar-check"
                size={18}
                color={Color.blue}
              />
            </View>
            <View style={modernStyles.titleTextContainer}>
              <Sys.Text style={modernStyles.titleMain}>
                Kế hoạch ghé thăm
              </Sys.Text>
              <Sys.Text style={modernStyles.titleSub}>
                Điểm bán hàng
              </Sys.Text>
            </View>
          </View>
        </View>

        {/* Content */}
        <DatePlan style={modernStyles.datePlanContainer} />
      </View>
    </Animated.View>
  );
};

// Modern Styles
const modernStyles = StyleSheet.create({
  container: {
    marginHorizontal: 10,
    marginBottom: 15,
  },
  mainCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },

  // Header
  header: {
    padding: 16,
    paddingBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 98, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  titleTextContainer: {
    flex: 1,
  },
  titleMain: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.Color.text,
    lineHeight: 20,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  titleSub: {
    fontSize: 13,
    fontWeight: '500',
    color: Color.blue,
    lineHeight: 16,
    marginTop: 1,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
  },

  // DatePlan Container
  datePlanContainer: {
    backgroundColor: 'transparent',
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 0,
    flex: 0,
  },
});

export default HomePlan;
