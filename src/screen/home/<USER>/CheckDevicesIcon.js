import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

function CheckDevicesIcon(props) {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={28}
      viewBox="0 0 24 28"
      fill="none"
      {...props}>
      <Path
        d="M11.451 17.07c-.278.753-.532 1.468-.816 2.172-.034.083-.224.146-.343.146-1.406.009-2.814.004-4.22.004-.886 0-1.773.01-2.659-.002-.252-.004-.33.081-.317.326.017.298.014.599-.005.897-.018.266.088.338.345.336 2.182-.009 4.365-.005 6.547-.005h.457c.038 1.38.374 2.648 1.106 3.843-.128.014-.209.03-.289.03-3.202.003-6.404.004-9.605.003-1.01 0-1.65-.643-1.651-1.671-.002-5.45 0-10.902.001-16.352C.002 5.135 0 3.473 0 1.812.001.6.615.006 1.826.007 6.889.01 11.952.009 17.015 0a.88.88 0 01.693.29c.993 1.012 2.006 2.005 2.999 3.017a.727.727 0 01.195.457c.01 3.412.008 6.824.007 10.236 0 .073-.014.146-.02.197-.61-.169-1.194-.38-1.796-.489-2.249-.404-4.248.172-6.02 1.6a.915.915 0 01-.526.2c-3.024.01-6.048.01-9.073 0-.285-.002-.404.06-.38.364a5.85 5.85 0 01-.003.863c-.017.256.068.346.335.345 2.415-.01 4.83-.008 7.245-.01h.78zm6.333-5.426H8.41c-1.661 0-3.322-.002-4.982.008-.11 0-.31.097-.314.158-.027.448-.015.897-.015 1.352h14.686v-1.518zm.022-3.88H8.643c-1.749 0-3.498.004-5.246-.003-.228-.001-.32.064-.305.3.017.276.028.555.004.83-.03.327.067.433.416.432 3.63-.013 7.261-.007 10.892-.007 1.03 0 2.059 0 3.089-.002.133-.001.311.055.313-.18v-1.37zM3.1 5.419h11.58V3.896c-.13-.006-.238-.015-.346-.015H5.5c-.708 0-1.417-.006-2.125.01-.093.003-.259.115-.263.183-.023.44-.011.88-.011 1.344zM16.663.903v3.336H20L16.662.903z"
        fill="#F5000D"
      />
      <Path
        d="M11.613 20.925c.04-3.46 2.837-6.233 6.239-6.192a6.221 6.221 0 016.147 6.314c-.045 3.408-2.859 6.14-6.286 6.101-3.368-.036-6.14-2.865-6.1-6.223zm1.274-.198l3.76 3.727 5.678-5.634-1.086-1.074-4.637 4.652-2.68-2.716-1.035 1.045z"
        fill="#F5000D"
      />
    </Svg>
  );
}

export default CheckDevicesIcon;
