import {useNavigation, useRoute} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {
  FlatList,
  ScrollView,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {Calendar} from 'react-native-calendars';
import ReactNativeModal from 'react-native-modal';
import MonthPicker, {
  ACTION_DATE_SET,
  ACTION_DISMISSED,
} from 'react-native-month-year-picker';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useDispatch, useSelector} from 'react-redux';
import {AppSelectors} from '../../app.slice';
import Sys from '../../components/Sys';
import {AccountSelectors} from '../account/services/account.slice';
import {PosActions, PosSelectors} from '../sale-location/services/pos.slice';
import CloseIcon from './icons/CloseIcon';
import SearchIcon from './icons/SearchIcon';
import SelectIcon from './icons/SelectIcon';

const ReportFilterScreen = () => {
  const {width, height} = useWindowDimensions();
  const view = useSelector(AccountSelectors.view);
  const insets = useSafeAreaInsets();
  const provinceList = useSelector(PosSelectors.provinceList);
  const districtList = useSelector(PosSelectors.districtList);
  const settings = useSelector(AppSelectors.settings);
  const {goBack} = useNavigation();
  const [agencyList, setAgencyList] = useState([]);
  const [branchList, setBranchList] = useState([]);
  const [posCode, setPosCode] = useState('');
  const [agency, setAgency] = useState({
    isShow: false,
    item: undefined,
  });

  const [branch, setBranch] = useState({
    isShow: false,
    item: undefined,
  });

  const [province, setProvince] = useState({
    isShow: false,
    item: undefined,
  });
  const [district, setDistrict] = useState({
    isShow: false,
    item: undefined,
  });
  const [fromDate, setFromDate] = useState({
    isShow: false,
    date: undefined,
  });
  const [toDate, setToDate] = useState({
    isShow: false,
    date: undefined,
  });
  const [month, setMonth] = useState({
    isShow: false,
    date: undefined,
  });

  const visitTimeList = [
    {
      name: 'Nhỏ hơn 90 Ngày',
      value: 1,
    },
    {
      name: 'Từ 90 Ngày đến 120 Ngày',
      value: 2,
    },
    {
      name: 'Lớn hơn 120 Ngày',
      value: 3,
    },
  ];

  const [visitTime, setVisitTime] = useState({
    isShow: false,
    date: undefined,
  });

  const [ticket_type, setTicket_Type] = useState([]);

  const {params} = useRoute();

  const dispatch = useDispatch();

  useEffect(() => {
    setFromDate({
      isShow: false,
      date: params?.body.event_time,
    });
    setToDate({
      isShow: false,
      date: params?.body.end_time,
    });
    setMonth({
      isShow: false,
      date: params?.body.event_time,
    });
    setPosCode(params?.body.search);
    if (params?.body?.checkin_days_gte && params?.body?.checkin_days_lte) {
      setVisitTime({
        isShow: false,
        item: {
          name: 'Từ 90 Ngày đến 120 Ngày',
          value: 2,
        },
      });
    } else {
      if (params?.body?.checkin_days_lte) {
        setVisitTime({
          isShow: false,
          item: {
            name: 'Nhỏ hơn 90 Ngày',
            value: 1,
          },
        });
      }
      if (params?.body?.checkin_days_gte) {
        setVisitTime({
          isShow: false,
          item: {
            name: 'Lớn hơn 120 Ngày',
            value: 3,
          },
        });
      }
    }

    if (view === 'branch.manager' && params.type === 'month') {
      setTicket_Type(params?.body?.ticket_type || []);
    }
  }, []);

  useEffect(() => {
    if (view === 'branch.manager') {
      dispatch(
        PosActions.getBranch({
          onSuccess: rs => {
            if (rs?.items) {
              setBranchList(rs.items);
            }
          },
        }),
      );
    }
  }, []);

  useEffect(() => {
    if (params?.body['branch[]']) {
      setBranch({
        isShow: false,
        item: branchList.filter(x => x.id === params?.body['branch[]'])[0],
      });
    }
  }, [branchList]);

  useEffect(() => {
    if (params?.body['agency[]']) {
      setAgency({
        isShow: false,
        item: agencyList.filter(x => x.id === params?.body['agency[]'])[0],
      });
    }
  }, [agencyList]);

  useEffect(() => {
    if (params?.body?.province) {
      setProvince({
        isShow: false,
        item: provinceList.filter(x => x.id === params?.body?.province)[0],
      });
    }
  }, [provinceList]);

  useEffect(() => {
    if (params?.body?.district) {
      try {
        if (
          districtList.filter(x => x.id === params?.body?.district).length > 0
        ) {
          setDistrict({
            isShow: false,
            item: districtList.filter(x => x.id === params?.body?.district)[0],
          });
        } else {
          setDistrict({
            isShow: false,
            item: undefined,
          });
        }
      } catch (error) {
        console.log(error);
      }
    }
  }, [districtList]);

  useEffect(() => {
    dispatch(
      PosActions.getDistricts({
        province: province?.item?.id,
      }),
    );
    setDistrict({
      isShow: false,
      item: undefined,
    });
  }, [province?.item]);

  useEffect(() => {
    dispatch(
      PosActions.getWards({
        district: district?.item?.id,
      }),
    );
  }, [district?.item]);

  useEffect(() => {
    dispatch(PosActions.getProvinces());
    dispatch(
      PosActions.getAgency({
        onSuccess: rs => {
          setAgencyList(rs);
        },
      }),
    );
  }, []);

  const onReset = () => {
    setDistrict({
      isShow: false,
      item: undefined,
    });
    setProvince({
      isShow: false,
      item: undefined,
    });
    setPosCode('');
    if (params.type === 'day') {
      setFromDate({
        isShow: false,
        date: moment().subtract(1, 'days').format('YYYY-MM-DD'),
      });
    } else {
      setFromDate({
        isShow: false,
        date: moment().format('YYYY-MM-DD'),
      });
    }

    setMonth({
      isShow: false,
      date: moment().format('YYYY-MM'),
    });
    setAgency({
      isShow: false,
      item: undefined,
    });
    setVisitTime({
      isShow: false,
      item: undefined,
    });
  };

  const renderConditions = () => {
    if (visitTime?.item?.value) {
      if (visitTime?.item?.value === 1) {
        return {
          checkin_days_lte: 90,
        };
      }
      if (visitTime?.item?.value === 2) {
        return {
          checkin_days_gte: 90,
          checkin_days_lte: 120,
        };
      }
      if (visitTime?.item?.value === 3) {
        return {
          checkin_days_gte: 120,
        };
      }
    } else {
      return {};
    }
  };

  const onAccept = () => {
    if (params.type === 'day') {
      params.onAccept({
        ticket_type,
        event_time: fromDate.date,
        province: province?.item?.id,
        district: district?.item?.id,
        'agency[]': agency?.item?.id,
        'branch[]': branch?.item?.id,
        ...(() => {
          return posCode
            ? {
                search: posCode,
              }
            : {};
        })(),
        ...renderConditions(),
      });
    }
    if (
      params.type === 'agency' ||
      params.type === 'branch' ||
      params.type === 'quarter'
    ) {
      params.onAccept({
        ticket_type,
        event_time: moment(month.date).format('YYYY-MM'),
        province: province?.item?.id,
        district: district?.item?.id,
        ...(() => {
          return posCode
            ? {
                search: posCode,
              }
            : {};
        })(),
        'agency[]': agency?.item?.id,
        'branch[]': branch?.item?.id,
        ...renderConditions(),
      });
    }
    if (params.type === 'owner') {
      params.onAccept({
        ...params.body,
        ticket_type,
        end_time: toDate.date,
        event_time: fromDate.date,
        province: province?.item?.id,
        district: district?.item?.id,
        ...(() => {
          return posCode
            ? {
                search: posCode,
              }
            : {};
        })(),
        'agency[]': agency?.item?.id,
        ...renderConditions(),
      });
    }
    if (params.type === 'month') {
      params.onAccept({
        ticket_type,
        event_time: moment(month.date).format('YYYY-MM-01'),
        end_time:
          moment(month.date).format('YYYYMM') === moment().format('YYYYMM')
            ? moment().format('YYYY-MM-DD')
            : moment(month.date).format('YYYY-MM-31'),
        province: province?.item?.id,
        district: district?.item?.id,
        ...(() => {
          return posCode
            ? {
                search: posCode,
              }
            : {};
        })(),
        'agency[]': agency?.item?.id,
        'branch[]': branch?.item?.id,
        ...renderConditions(),
      });
    }
    goBack();
  };

  return (
    <Sys.Container
      headerColor="white"
      hasHeader
      hasFooter
      backgroundColor="white"
      footerColor="white">
      {month.isShow && (
        <MonthPicker
          onChange={(e, newDate) => {
            switch (e) {
              case ACTION_DATE_SET:
                setMonth({
                  date: newDate,
                  isShow: false,
                });
                break;
              case ACTION_DISMISSED:
              default:
                setMonth({
                  ...month,
                  isShow: false,
                });
            }
          }}
          value={moment(month.date, 'YYYY-MM').toDate()}
          locale="vi"
        />
      )}
      <ReactNativeModal
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={{
          padding: 0,
          margin: 0,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onBackdropPress={() =>
          setToDate({
            ...toDate,
            isShow: false,
          })
        }
        isVisible={toDate.isShow}
        onSwipeComplete={() => {
          setToDate({
            ...toDate,
            isShow: false,
          });
        }}
        swipeDirection="down">
        <View
          style={{
            flex: 1,
          }}
        />
        <View
          style={{
            backgroundColor: 'white',
            width: width,
            borderRadius: 20,
            padding: 20,
            paddingBottom: insets.bottom + 20,
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
            }}>
            Chọn ngày kết thúc
          </Sys.Text>
          <Calendar
            markingType={'custom'}
            markedDates={{
              [`${toDate.date}`]: {
                customStyles: {
                  container: {
                    backgroundColor: 'blue',
                  },
                  text: {
                    color: 'white',
                  },
                },
              },
            }}
            onDayPress={day => {
              setToDate({
                isShow: false,
                date: day.dateString,
              });
            }}
          />
        </View>
      </ReactNativeModal>
      <ReactNativeModal
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={{
          padding: 0,
          margin: 0,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onBackdropPress={() =>
          setFromDate({
            ...fromDate,
            isShow: false,
          })
        }
        isVisible={fromDate.isShow}
        onSwipeComplete={() => {
          setFromDate({
            ...fromDate,
            isShow: false,
          });
        }}
        swipeDirection="down">
        <View
          style={{
            flex: 1,
          }}
        />
        <View
          style={{
            backgroundColor: 'white',
            width: width,
            borderRadius: 20,
            padding: 20,
            paddingBottom: insets.bottom + 20,
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
            }}>
            Chọn ngày
          </Sys.Text>
          <Calendar
            markingType={'custom'}
            markedDates={{
              [`${fromDate.date}`]: {
                customStyles: {
                  container: {
                    backgroundColor: 'blue',
                  },
                  text: {
                    color: 'white',
                  },
                },
              },
            }}
            onDayPress={day => {
              setFromDate({
                isShow: false,
                date: day.dateString,
              });
            }}
          />
        </View>
      </ReactNativeModal>
      <ReactNativeModal
        propagateSwipe
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={{
          padding: 0,
          margin: 0,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onBackdropPress={() =>
          setAgency({
            ...agency,
            isShow: false,
          })
        }
        isVisible={agency.isShow}>
        <View
          style={{
            flex: 1,
          }}
        />
        <View
          style={{
            backgroundColor: 'white',
            width: width,
            borderRadius: 20,
            padding: 20,
            paddingBottom: insets.bottom + 20,
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
            }}>
            Chọn đại lý
          </Sys.Text>
          <View
            style={{
              marginVertical: 20,
              height: height / 3,
            }}>
            <FlatList
              showsVerticalScrollIndicator={false}
              data={agencyList}
              keyExtractor={(item, index) => `${index}-prov`}
              renderItem={({item, index}) => {
                return (
                  <TouchableOpacity
                    onPress={() => {
                      setAgency({
                        isShow: false,
                        item,
                      });
                    }}
                    style={{
                      padding: 10,
                      marginBottom: 5,
                      backgroundColor: '#d1d1d110',
                      borderRadius: 10,
                    }}>
                    <Sys.Text>{item?.text}</Sys.Text>
                  </TouchableOpacity>
                );
              }}
            />
          </View>
        </View>
      </ReactNativeModal>
      <ReactNativeModal
        propagateSwipe
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={{
          padding: 0,
          margin: 0,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onBackdropPress={() =>
          setBranch({
            ...branch,
            isShow: false,
          })
        }
        isVisible={branch.isShow}>
        <View
          style={{
            flex: 1,
          }}
        />
        <View
          style={{
            backgroundColor: 'white',
            width: width,
            borderRadius: 20,
            padding: 20,
            paddingBottom: insets.bottom + 20,
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
            }}>
            Chọn đại lý
          </Sys.Text>
          <View
            style={{
              marginVertical: 20,
              height: height / 3,
            }}>
            <FlatList
              showsVerticalScrollIndicator={false}
              data={branchList}
              keyExtractor={(item, index) => `${index}-prov`}
              renderItem={({item, index}) => {
                return (
                  <TouchableOpacity
                    onPress={() => {
                      setBranch({
                        isShow: false,
                        item,
                      });
                    }}
                    style={{
                      padding: 10,
                      marginBottom: 5,
                      backgroundColor: '#d1d1d110',
                      borderRadius: 10,
                    }}>
                    <Sys.Text>{item?.text}</Sys.Text>
                  </TouchableOpacity>
                );
              }}
            />
          </View>
        </View>
      </ReactNativeModal>
      <ReactNativeModal
        propagateSwipe
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={{
          padding: 0,
          margin: 0,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onBackdropPress={() =>
          setProvince({
            ...province,
            isShow: false,
          })
        }
        isVisible={province.isShow}>
        <View
          style={{
            flex: 1,
          }}
        />
        <View
          style={{
            backgroundColor: 'white',
            width: width,
            borderRadius: 20,
            padding: 20,
            paddingBottom: insets.bottom + 20,
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
            }}>
            Chọn Tỉnh/Thành phố
          </Sys.Text>
          <View
            style={{
              marginVertical: 20,
              height: height / 3,
            }}>
            <FlatList
              showsVerticalScrollIndicator={false}
              data={provinceList}
              keyExtractor={(item, index) => `${index}-prov`}
              renderItem={({item, index}) => {
                return (
                  <TouchableOpacity
                    onPress={() => {
                      setProvince({
                        isShow: false,
                        item,
                      });
                    }}
                    style={{
                      padding: 10,
                      marginBottom: 5,
                      backgroundColor: '#d1d1d110',
                      borderRadius: 10,
                    }}>
                    <Sys.Text>{item?.text}</Sys.Text>
                  </TouchableOpacity>
                );
              }}
            />
          </View>
        </View>
      </ReactNativeModal>
      <ReactNativeModal
        propagateSwipe
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={{
          padding: 0,
          margin: 0,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onBackdropPress={() =>
          setDistrict({
            ...district,
            isShow: false,
          })
        }
        isVisible={district.isShow}>
        <View
          style={{
            flex: 1,
          }}
        />
        <View
          style={{
            backgroundColor: 'white',
            width: width,
            borderRadius: 20,
            padding: 20,
            paddingBottom: insets.bottom + 20,
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
            }}>
            Chọn Quận/Huyện
          </Sys.Text>
          <View
            style={{
              marginVertical: 20,
              height: height / 3,
            }}>
            <FlatList
              showsVerticalScrollIndicator={false}
              data={districtList}
              keyExtractor={(item, index) => `${index}-prov`}
              renderItem={({item, index}) => {
                return (
                  <TouchableOpacity
                    onPress={() => {
                      setDistrict({
                        isShow: false,
                        item,
                      });
                    }}
                    style={{
                      padding: 10,
                      marginBottom: 5,
                      backgroundColor: '#d1d1d110',
                      borderRadius: 10,
                    }}>
                    <Sys.Text>{item?.text}</Sys.Text>
                  </TouchableOpacity>
                );
              }}
            />
          </View>
        </View>
      </ReactNativeModal>
      <ReactNativeModal
        propagateSwipe
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={{
          padding: 0,
          margin: 0,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onBackdropPress={() =>
          setVisitTime({
            ...visitTime,
            isShow: false,
          })
        }
        isVisible={visitTime.isShow}>
        <View
          style={{
            flex: 1,
          }}
        />
        <View
          style={{
            backgroundColor: 'white',
            width: width,
            borderRadius: 20,
            padding: 20,
            paddingBottom: insets.bottom + 20,
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
            }}>
            Chọn thời gian ghé thăm
          </Sys.Text>
          <View
            style={{
              marginVertical: 20,
              maxHeight: height / 3,
            }}>
            <FlatList
              showsVerticalScrollIndicator={false}
              data={visitTimeList}
              keyExtractor={(item, index) => `${index}-prov`}
              renderItem={({item, index}) => {
                return (
                  <TouchableOpacity
                    onPress={() => {
                      setVisitTime({
                        isShow: false,
                        item,
                      });
                    }}
                    style={{
                      padding: 10,
                      marginBottom: 5,
                      backgroundColor: '#d1d1d110',
                      borderRadius: 10,
                    }}>
                    <Sys.Text>{item?.name}</Sys.Text>
                  </TouchableOpacity>
                );
              }}
            />
          </View>
        </View>
      </ReactNativeModal>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <TouchableOpacity
          onPress={goBack}
          activeOpacity={0.8}
          style={{
            padding: 20,
          }}>
          <CloseIcon />
        </TouchableOpacity>
        <Sys.Text
          style={{
            flex: 1,
            textAlign: 'center',
            color: '#0062FF',
            fontSize: 18,
            fontWeight: '600',
          }}>
          Lọc
        </Sys.Text>
        <View style={{width: 54}} />
      </View>
      <ScrollView
        style={{
          paddingTop: 10,
          paddingBottom: 20,
          paddingHorizontal: 20,
          flex: 1,
        }}
        showsVerticalScrollIndicator={false}>
        {view !== 'pos.owner' && (
          <View style={{marginBottom: 10}}>
            <Sys.Input
              style={{
                paddingLeft: 40,
              }}
              value={posCode}
              onChangeText={e => setPosCode(e)}
              placeholder={'Nhập mã điểm bán'}
              left={
                <View
                  style={{
                    marginTop: 6,
                    marginLeft: 10,
                  }}>
                  <SearchIcon />
                </View>
              }
            />
          </View>
        )}

        {params.type === 'day' || params.type === 'owner' ? (
          <View style={{marginBottom: 10}}>
            <Sys.Text
              style={{
                marginBottom: 5,
                textTransform: 'uppercase',
                fontWeight: '600',
                color: '#44444F',
              }}>
              {params.type === 'owner' ? 'Từ ngày' : 'Chọn ngày'}
            </Sys.Text>
            <TouchableOpacity
              onPress={() => {
                setFromDate({
                  ...fromDate,
                  isShow: true,
                });
              }}
              activeOpacity={0.8}
              style={{
                backgroundColor: 'white',
                borderRadius: 8,
                borderWidth: 1,
                borderColor: '#D1D1D1',
                paddingVertical: 10,
                paddingHorizontal: 20,
              }}>
              <Sys.Text
                style={{
                  color: fromDate?.date ? 'black' : '#D1D1D1',
                }}>
                {fromDate?.date
                  ? moment(fromDate?.date, 'YYYY-MM-DD').format('DD/MM/YYYY')
                  : 'Chọn ngày'}
              </Sys.Text>
              <View
                style={{
                  position: 'absolute',
                  right: 10,
                  top: 10,
                }}>
                <SelectIcon />
              </View>
            </TouchableOpacity>
          </View>
        ) : (
          <></>
        )}
        {params.type === 'owner' ? (
          <View style={{marginBottom: 10}}>
            <Sys.Text
              style={{
                marginBottom: 5,
                textTransform: 'uppercase',
                fontWeight: '600',
                color: '#44444F',
              }}>
              Đến ngày
            </Sys.Text>
            <TouchableOpacity
              onPress={() => {
                setToDate({
                  ...toDate,
                  isShow: true,
                });
              }}
              activeOpacity={0.8}
              style={{
                backgroundColor: 'white',
                borderRadius: 8,
                borderWidth: 1,
                borderColor: '#D1D1D1',
                paddingVertical: 10,
                paddingHorizontal: 20,
              }}>
              <Sys.Text
                style={{
                  color: toDate?.date ? 'black' : '#D1D1D1',
                }}>
                {toDate?.date
                  ? moment(toDate?.date, 'YYYY-MM-DD').format('DD/MM/YYYY')
                  : 'Chọn ngày'}
              </Sys.Text>
              <View
                style={{
                  position: 'absolute',
                  right: 10,
                  top: 10,
                }}>
                <SelectIcon />
              </View>
            </TouchableOpacity>
          </View>
        ) : (
          <></>
        )}
        {params.type === 'month' ||
        params.type === 'agency' ||
        params.type === 'branch' ||
        params.type === 'quarter' ? (
          <View style={{marginBottom: 10}}>
            <Sys.Text
              style={{
                marginBottom: 5,
                textTransform: 'uppercase',
                fontWeight: '600',
                color: '#44444F',
              }}>
              Chọn tháng
            </Sys.Text>
            <TouchableOpacity
              onPress={() => {
                setMonth({
                  ...month,
                  isShow: true,
                });
              }}
              activeOpacity={0.8}
              style={{
                backgroundColor: 'white',
                borderRadius: 8,
                borderWidth: 1,
                borderColor: '#D1D1D1',
                paddingVertical: 10,
                paddingHorizontal: 20,
              }}>
              <Sys.Text
                style={{
                  color: month?.date ? 'black' : '#D1D1D1',
                }}>
                {month?.date
                  ? moment(month?.date).format('MM/YYYY')
                  : 'Chọn tháng'}
              </Sys.Text>
              <View
                style={{
                  position: 'absolute',
                  right: 10,
                  top: 10,
                }}>
                <SelectIcon />
              </View>
            </TouchableOpacity>
          </View>
        ) : (
          <></>
        )}
        {params.type === 'owner' ? (
          <></>
        ) : (
          <>
            {view === 'branch.manager' ? (
              <View style={{marginBottom: 10}}>
                <Sys.Text
                  style={{
                    marginBottom: 5,
                    textTransform: 'uppercase',
                    fontWeight: '600',
                    color: '#44444F',
                  }}>
                  Chi nhánh
                </Sys.Text>
                <TouchableOpacity
                  onPress={() => {
                    setBranch({
                      ...branch,
                      isShow: true,
                    });
                  }}
                  activeOpacity={0.8}
                  style={{
                    backgroundColor: 'white',
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: '#D1D1D1',
                    paddingVertical: 10,
                    paddingHorizontal: 20,
                  }}>
                  <Sys.Text
                    style={{
                      color: branch.item ? 'black' : '#D1D1D1',
                    }}>
                    {branch?.item ? branch?.item?.text : 'Chọn chi nhánh'}
                  </Sys.Text>
                  <View
                    style={{
                      position: 'absolute',
                      right: 10,
                      top: 10,
                    }}>
                    <SelectIcon />
                  </View>
                </TouchableOpacity>
              </View>
            ) : (
              <></>
            )}
            <View style={{marginBottom: 10}}>
              <Sys.Text
                style={{
                  marginBottom: 5,
                  textTransform: 'uppercase',
                  fontWeight: '600',
                  color: '#44444F',
                }}>
                Tỉnh/Thành phố
              </Sys.Text>
              <TouchableOpacity
                onPress={() => {
                  setProvince({
                    ...province,
                    isShow: true,
                  });
                }}
                activeOpacity={0.8}
                style={{
                  backgroundColor: 'white',
                  borderRadius: 8,
                  borderWidth: 1,
                  borderColor: '#D1D1D1',
                  paddingVertical: 10,
                  paddingHorizontal: 20,
                }}>
                <Sys.Text
                  style={{
                    color: province.item ? 'black' : '#D1D1D1',
                  }}>
                  {province?.item
                    ? province?.item?.text
                    : 'Chọn Tỉnh/Thành Phố'}
                </Sys.Text>
                <View
                  style={{
                    position: 'absolute',
                    right: 10,
                    top: 10,
                  }}>
                  <SelectIcon />
                </View>
              </TouchableOpacity>
            </View>
            <View style={{marginBottom: 10}}>
              <Sys.Text
                style={{
                  marginBottom: 5,
                  textTransform: 'uppercase',
                  fontWeight: '600',
                  color: '#44444F',
                }}>
                Quận/Huyện
              </Sys.Text>
              <TouchableOpacity
                onPress={() => {
                  if (districtList.length !== 0) {
                    setDistrict({
                      ...district,
                      isShow: true,
                    });
                  }
                }}
                activeOpacity={0.8}
                style={{
                  backgroundColor: 'white',
                  borderRadius: 8,
                  borderWidth: 1,
                  borderColor: '#D1D1D1',
                  paddingVertical: 10,
                  paddingHorizontal: 20,
                }}>
                <Sys.Text
                  style={{
                    color: district.item ? 'black' : '#D1D1D1',
                  }}>
                  {district?.item ? district?.item?.text : 'Chọn Quận/Huyện'}
                </Sys.Text>
                <View
                  style={{
                    position: 'absolute',
                    right: 10,
                    top: 10,
                  }}>
                  <SelectIcon />
                </View>
              </TouchableOpacity>
            </View>
            <View style={{marginBottom: 10}}>
              <Sys.Text
                style={{
                  marginBottom: 5,
                  textTransform: 'uppercase',
                  fontWeight: '600',
                  color: '#44444F',
                }}>
                Đại lý
              </Sys.Text>
              <TouchableOpacity
                onPress={() => {
                  setAgency({
                    ...agency,
                    isShow: true,
                  });
                }}
                activeOpacity={0.8}
                style={{
                  backgroundColor: 'white',
                  borderRadius: 8,
                  borderWidth: 1,
                  borderColor: '#D1D1D1',
                  paddingVertical: 10,
                  paddingHorizontal: 20,
                }}>
                <Sys.Text
                  style={{
                    color: agency.item ? 'black' : '#D1D1D1',
                  }}>
                  {agency?.item ? agency?.item?.text : 'Chọn đại lý'}
                </Sys.Text>
                <View
                  style={{
                    position: 'absolute',
                    right: 10,
                    top: 10,
                  }}>
                  <SelectIcon />
                </View>
              </TouchableOpacity>
            </View>
            <View style={{marginBottom: 10}}>
              <Sys.Text
                style={{
                  marginBottom: 5,
                  textTransform: 'uppercase',
                  fontWeight: '600',
                  color: '#44444F',
                }}>
                Thời gian ghé thăm
              </Sys.Text>
              <TouchableOpacity
                onPress={() => {
                  setVisitTime({
                    ...visitTime,
                    isShow: true,
                  });
                }}
                activeOpacity={0.8}
                style={{
                  backgroundColor: 'white',
                  borderRadius: 8,
                  borderWidth: 1,
                  borderColor: '#D1D1D1',
                  paddingVertical: 10,
                  paddingHorizontal: 20,
                }}>
                <Sys.Text
                  style={{
                    color: visitTime?.item?.name ? 'black' : '#D1D1D1',
                  }}>
                  {visitTime?.item?.name || 'Chọn thời gian ghé thắm'}
                </Sys.Text>
                <View
                  style={{
                    position: 'absolute',
                    right: 10,
                    top: 10,
                  }}>
                  <SelectIcon />
                </View>
              </TouchableOpacity>
            </View>
          </>
        )}

        {/* {view === 'branch.manager' ? (
          <View style={{marginBottom: 10}}>
            <Sys.Text
              style={{
                marginBottom: 5,
                textTransform: 'uppercase',
                fontWeight: '600',
                color: '#44444F',
              }}>
              Kiểu vé
            </Sys.Text>
            <FlatList
              data={[
                {
                  name: 'eTicket',
                  value: 1,
                },
                {
                  name: 'Vé POS',
                  value: 0,
                },
              ]}
              keyExtractor={(item, index) => `${index}`}
              renderItem={props => {
                const checked = ticket_type.indexOf(props.item?.value) > -1;

                return (
                  <View
                    style={{
                      flexDirection: 'row',
                      gap: 8,
                      alignItems: 'center',
                      marginTop: 10,
                    }}>
                    <Sys.Checkbox
                      onPress={() => {
                        if (checked) {
                          setTicket_Type(
                            ticket_type.filter(y => y !== props.item?.value),
                          );
                        } else {
                          setTicket_Type([...ticket_type, props.item?.value]);
                        }
                      }}
                      checked={checked}
                    />
                    <Sys.Text>{props?.item?.name}</Sys.Text>
                  </View>
                );
              }}
            />
          </View>
        ) : (
          <></>
        )} */}
      </ScrollView>
      <View
        style={{
          paddingHorizontal: 20,
          paddingVertical: 10,
          backgroundColor: 'white',
          flexDirection: 'row',
        }}>
        <View
          style={{
            marginRight: 10,
            flex: 1,
          }}>
          <TouchableOpacity
            onPress={onReset}
            style={{
              borderWidth: 1,
              borderRadius: 50,
              padding: 10,
              borderColor: '#0062FF',
              alignItems: 'center',
            }}>
            <Sys.Text
              style={{
                color: '#0062FF',
                fontWeight: '600',
              }}>
              Thiết lập lại
            </Sys.Text>
          </TouchableOpacity>
        </View>
        <View
          style={{
            marginLeft: 10,
            flex: 1,
          }}>
          <TouchableOpacity
            onPress={onAccept}
            style={{
              borderWidth: 1,
              borderRadius: 50,
              padding: 10,
              borderColor: '#0062FF',
              alignItems: 'center',
              backgroundColor: '#0062FF',
            }}>
            <Sys.Text
              style={{
                color: 'white',
                fontWeight: '600',
              }}>
              Áp dụng
            </Sys.Text>
          </TouchableOpacity>
        </View>
      </View>
    </Sys.Container>
  );
};

export default ReportFilterScreen;
