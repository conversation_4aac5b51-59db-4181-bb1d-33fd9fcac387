import * as React from 'react';
import Svg, {G, <PERSON>, Defs, ClipPath} from 'react-native-svg';
const SortIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={21}
    height={21}
    fill="none"
    {...props}>
    <G fill="#fff" clipPath="url(#a)">
      <Path d="M17.85 16.8V1.312a.788.788 0 0 0-1.575 0V16.8H13.65l3.412 3.675 3.413-3.675H17.85ZM.788.788h11.025v2.625H.788V.788Z" />
      <Path d="M11.55 1.05v2.1H1.05v-2.1h10.5Zm.525-.525H.525v3.15h11.55V.525ZM.788 4.987h8.925v2.625H.788V4.987Z" />
      <Path d="M9.45 5.25v2.1h-8.4v-2.1h8.4Zm.525-.525H.525v3.15h9.45v-3.15ZM.788 9.188h6.825v2.624H.788V9.188Z" />
      <Path d="M7.35 9.45v2.1h-6.3v-2.1h6.3Zm.525-.525H.525v3.15h7.35v-3.15ZM.787 13.388h4.725v2.625H.787v-2.625Z" />
      <Path d="M5.25 13.65v2.1h-4.2v-2.1h4.2Zm.525-.525H.525v3.15h5.25v-3.15ZM.787 17.588h2.625v2.625H.787v-2.625Z" />
      <Path d="M3.15 17.85v2.1h-2.1v-2.1h2.1Zm.525-.525H.525v3.15h3.15v-3.15Z" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h21v21H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default SortIcon;
