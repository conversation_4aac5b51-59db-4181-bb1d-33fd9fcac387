import {useIsFocused, useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {TouchableOpacity, View} from 'react-native';
import Sys from '../../components/Sys';
import OwnerReport from '../owner/report/OwnerReport';
import FilterIcon from './icons/FilterIcon';

const ReportOwnerScreen = () => {
  const {navigate} = useNavigation();
  const [body, setBody] = useState({
    event_time: moment().subtract('days', 7).format('YYYY-MM-DD'),
    end_time: moment().subtract('days', 1).format('YYYY-MM-DD'),
    sort_by: 'code',
    stacked_by: 'game',
    sort_order: 'desc',
  });
  const isFocused = useIsFocused();
  useEffect(() => {
    if (!isFocused) {
      setBody({
        event_time: moment().subtract('days', 7).format('YYYY-MM-DD'),
        end_time: moment().subtract('days', 1).format('YYYY-MM-DD'),
        sort_by: 'code',
        stacked_by: 'game',
        sort_order: 'desc',
      });
    }
  }, [isFocused]);

  return (
    <Sys.Container hasHeader hasFooter footerColor="white">
      <Sys.Header
        hideGoBack
        title="Báo cáo"
        right={
          <View>
            <TouchableOpacity
              onPress={() => {
                navigate('ReportFilterScreen', {
                  type: 'owner',
                  body,
                  onAccept: newBody => {
                    setBody(newBody);
                  },
                });
              }}
              style={{
                paddingHorizontal: 20,
              }}>
              <FilterIcon />
            </TouchableOpacity>
          </View>
        }
      />
      <OwnerReport body={body} setBody={setBody} />
    </Sys.Container>
  );
};

export default ReportOwnerScreen;
