import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import {AccountSelectors} from '../account/services/account.slice';
import RightIcon from './icons/RightIcon';
import OwnerReport from '../owner/report/OwnerReport';

const ReportScreen = () => {
  const view = useSelector(AccountSelectors.view);
  const {navigate} = useNavigation();
  const dispatch = useDispatch();

  return (
    <Sys.Container hasHeader>
      <Sys.Header hideGoBack title="Báo cáo" />
      {['pos.manager', 'branch.owner', 'branch.manager', 'pos.agent'].indexOf(
        view,
      ) > -1 && (
        <View
          style={{
            marginHorizontal: 10,
            marginTop: 20,
          }}>
          <TouchableOpacity
            style={{
              backgroundColor: '#F5000D',
              marginBottom: 2,
            }}>
            <Sys.Text
              style={{
                color: 'white',
                padding: 20,
                fontWeight: '600',
              }}>
              Báo cáo doanh thu
            </Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigate('ReportDayScreen')}
            style={{
              backgroundColor: '#E7E7E7',
              marginBottom: 2,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: 20,
            }}>
            <Sys.Text
              style={{
                fontWeight: '600',
              }}>
              Báo cáo doanh thu theo ngày
            </Sys.Text>
            <RightIcon />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigate('ReportMonthScreen')}
            style={{
              backgroundColor: '#E7E7E7',
              marginBottom: 2,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: 20,
            }}>
            <Sys.Text
              style={{
                fontWeight: '600',
              }}>
              Báo cáo doanh thu theo tháng
            </Sys.Text>
            <RightIcon />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigate('ReportQuarterScreen')}
            style={{
              backgroundColor: '#E7E7E7',
              marginBottom: 2,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: 20,
            }}>
            <Sys.Text
              style={{
                fontWeight: '600',
              }}>
              Báo cáo doanh thu theo quý
            </Sys.Text>
            <RightIcon />
          </TouchableOpacity>
          {view === 'branch.manager' ? (
            <TouchableOpacity
              onPress={() => navigate('ReportBranchScreen')}
              style={{
                backgroundColor: '#E7E7E7',
                marginBottom: 2,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: 20,
              }}>
              <Sys.Text
                style={{
                  fontWeight: '600',
                }}>
                Báo cáo doanh thu theo chi nhánh
              </Sys.Text>
              <RightIcon />
            </TouchableOpacity>
          ) : (
            <></>
          )}

          <TouchableOpacity
            onPress={() => navigate('ReportAgencyScreen')}
            style={{
              backgroundColor: '#E7E7E7',
              marginBottom: 2,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: 20,
            }}>
            <Sys.Text
              style={{
                fontWeight: '600',
              }}>
              Báo cáo doanh thu theo đại lý
            </Sys.Text>
            <RightIcon />
          </TouchableOpacity>
          {/*{view === 'branch.manager' ? (
            <TouchableOpacity
              onPress={() => {
                dispatch(AppActions.setDev(true));
                // navigate('ReportETicketScreen');
              }}
              style={{
                backgroundColor: '#E7E7E7',
                marginBottom: 2,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: 20,
              }}>
              <Sys.Text
                style={{
                  fontWeight: '600',
                }}>
                Báo cáo eTicket
              </Sys.Text>
              <RightIcon />
            </TouchableOpacity>
          ) : (
            <></>
          )}*/}
          {/*<TouchableOpacity
            onPress={() => dispatch(AppActions.setDev(true))}
            style={{
              backgroundColor: '#0062FF',
              marginBottom: 2,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: 20,
            }}>
            <Sys.Text
              style={{
                fontWeight: '600',
                color: 'white',
              }}>
              Báo cáo vật phẩm Marketing
            </Sys.Text>
            <RightIcon color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => dispatch(AppActions.setDev(true))}
            style={{
              backgroundColor: '#0062FF',
              marginBottom: 2,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: 20,
            }}>
            <Sys.Text
              style={{
                fontWeight: '600',
                color: 'white',
              }}>
              Báo cáo ghé thăm điểm bán
            </Sys.Text>
            <RightIcon color="#fff" />
          </TouchableOpacity>*/}
        </View>
      )}
      {view === 'pos.owner' && <OwnerReport />}
    </Sys.Container>
  );
};

export default ReportScreen;
