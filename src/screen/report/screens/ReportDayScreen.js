import React, {useCallback, useEffect, useState} from 'react';
import {
  FlatList,
  ScrollView,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {
  VictoryAxis,
  VictoryBar,
  VictoryChart,
  VictoryLabel,
  VictoryTheme,
} from 'victory-native';
import Sys from '../../../components/Sys';
import {formatVND} from '../../../services/util';
import DownIcon from '../icons/DownIcon';
import RightCityIcon from '../icons/RightCityIcon';
import UpIcon from '../icons/UpIcon';
import {ReportActions} from '../services/report.slice';
import moment from 'moment';
import FilterIcon from '../icons/FilterIcon';
import {useNavigation} from '@react-navigation/native';
import DownsIcon from '../icons/DownsIcon';
import UpLIcon from '../icons/UpLIcon';
import DownLIcon from '../icons/DownLIcon';
import SortIcon from '../icons/SortIcon';
import _ from 'lodash';

export function createArrayWithSteps(number) {
  const step = Number(number) / 2;

  const array = [];

  for (let i = 0; i <= 2; i++) {
    array.push(Math.round((i * step) / 1000) * 1000);
  }
  return array;
}

const ReportDayScreen = () => {
  const {navigate} = useNavigation();
  const dispatch = useDispatch();
  const {width} = useWindowDimensions();
  const [city, setCity] = useState([]);
  const [count, setCount] = useState([]);
  const [chart, setChart] = useState([]);

  const [body, setBody] = useState({
    event_time: moment().subtract(1, 'days').format('YYYY-MM-DD'),
    sort_order: 'desc',
  });

  useEffect(() => {
    getData();
  }, [body]);

  const getData = useCallback(() => {
    dispatch(
      ReportActions.getCityDayChart({
        onSuccess: rs => {
          setChart(rs?.data?.items);
        },
        onFail: rs => {},
        body,
      }),
    );

    dispatch(
      ReportActions.getCityDayCount({
        onSuccess: rs => {
          setCount(rs?.data?.items[0]);
        },
        onFail: rs => {},
        body,
      }),
    );

    dispatch(
      ReportActions.getCityDay({
        onSuccess: rs => {
          setCity(rs?.data?.items);
        },
        onFail: rs => {},
        body,
      }),
    );
  }, [body]);

  const cal = (current, pre) => {
    try {
      if (pre === 0) {
        return '∞';
      }
      const temp = Number(((current - pre) / pre) * 100);
      if (temp < 0) {
        return `${temp.toFixed(2)}`;
      } else {
        return `+${temp.toFixed(2)}`;
      }
    } catch (error) {
      return '';
    }
  };

  const compareList = [
    {
      title: 'POS có DT',
      percent: cal(count?.pos_count, count?.previous_pos_count),
      value: formatVND(count?.pos_count),
      compareWith: formatVND(count?.previous_pos_count),
    },
    {
      title: 'DT/POS',
      percent: cal(
        Number(count?.total_revenue / count?.pos_count).toFixed(0),
        Number(count?.previous_revenue / count?.previous_pos_count).toFixed(0),
      ),
      value: formatVND(
        Number(count?.total_revenue / count?.pos_count).toFixed(0),
      ),
      compareWith: formatVND(
        Number(count?.previous_revenue / count?.previous_pos_count).toFixed(0),
      ),
    },
    {
      title: 'Số vé',
      percent: cal(count?.net_tickets_sold, count?.previous_net_tickets_sold),
      value: formatVND(count?.net_tickets_sold),
      compareWith: formatVND(count?.previous_net_tickets_sold),
    },
    {
      title: 'DT/Vé',
      percent: cal(
        Number(count?.total_revenue / count?.net_tickets_sold).toFixed(0),
        Number(
          count?.previous_revenue / count?.previous_net_tickets_sold,
        ).toFixed(0),
      ),
      value: formatVND(
        Number(count?.total_revenue / count?.net_tickets_sold).toFixed(0),
      ),
      compareWith: formatVND(
        Number(
          count?.previous_revenue / count?.previous_net_tickets_sold,
        ).toFixed(0),
      ),
    },
  ];

  return (
    <Sys.Container backgroundColor="white" hasHeader>
      <Sys.Header
        title={'Báo cáo ngày'}
        right={
          <View>
            <TouchableOpacity
              onPress={() => {
                navigate('ReportFilterScreen', {
                  type: 'day',
                  body,
                  onAccept: newBody => {
                    setBody(newBody);
                  },
                });
              }}
              style={{
                paddingHorizontal: 20,
              }}>
              <FilterIcon />
            </TouchableOpacity>
          </View>
        }
      />
      <ScrollView showsVerticalScrollIndicator={false}>
        <View
          style={{
            marginHorizontal: 20,
            marginVertical: 10,
          }}>
          <FlatList
            scrollEnabled={false}
            numColumns={2}
            key={(item, index) => `${index}`}
            data={compareList}
            renderItem={({item, index}) => {
              return (
                <View
                  style={{
                    flex: 1,
                    margin: 20,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Sys.Text
                      style={{
                        fontWeight: '600',
                        lineHeight: 18,
                        flex: 1,
                      }}>
                      {item.title}
                    </Sys.Text>
                    <Sys.Text
                      style={{
                        color: item.percent > 0 ? '#3DD598' : 'red',
                        fontWeight: '600',
                        lineHeight: 18,
                      }}>
                      {item.percent}
                    </Sys.Text>
                    <View
                      style={{
                        width: 10,
                        marginLeft: 10,
                      }}>
                      {item.percent > 0 ? <UpLIcon /> : <DownLIcon />}
                    </View>
                  </View>
                  <Sys.Text
                    style={{
                      fontSize: 24,
                      fontWeight: '600',
                      lineHeight: 33,
                    }}>
                    {item.value}
                  </Sys.Text>
                  <Sys.Text
                    style={{
                      color: '#92929D',
                      lineHeight: 16,
                    }}>
                    So sánh với ({item.compareWith})
                  </Sys.Text>
                </View>
              );
            }}
          />
        </View>
        <View>
          <Sys.Text
            style={{
              textAlign: 'center',
              color: '#0062FF',
              fontWeight: '600',
              marginBottom: 5,
            }}>
            Doanh thu theo giờ
          </Sys.Text>
          <Sys.Text
            style={{
              textAlign: 'center',
              color: '#848484',
              fontWeight: '600',
            }}>
            ({moment(body?.event_time, 'YYYY-MM-DD').format('DD/MM/YYYY')})
          </Sys.Text>
        </View>
        <VictoryChart
          padding={50}
          domainPadding={{x: 10}}
          width={width}
          theme={VictoryTheme.material}>
          <VictoryAxis
            tickValues={chart.map(x => {
              return x?.code;
            })}
            tickFormat={tick => `${tick}`}
          />
          <VictoryAxis
            dependentAxis
            // axisComponent={<></>}
            // tickComponent={<></>}
            tickLabelComponent={<CustomTickLabel />}
            labelComponent={<></>}
            tickFormat={createArrayWithSteps(
              _.maxBy(chart || [], 'total_revenue')?.total_revenue,
            )}
          />

          <VictoryBar
            horizontal
            style={{
              data: {fill: '#376DF7'},
            }}
            data={chart.map(x => {
              return {
                x: Number(x?.code),
                y: Number(x?.total_revenue),
              };
            })}
          />
        </VictoryChart>
        <View style={{marginHorizontal: 10}}>
          <View
            style={{
              backgroundColor: '#376DF7',
              flexDirection: 'row',
              padding: 10,
            }}>
            <Sys.Text
              style={{
                color: 'white',
                flex: 1,
                fontWeight: '500',
              }}>
              Chi nhánh
            </Sys.Text>
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                width: 120,
              }}
              onPress={() => {
                setBody({
                  ...body,
                  sort_order: body.sort_order === 'desc' ? 'asc' : 'desc',
                });
              }}>
              <Sys.Text
                style={{
                  color: 'white',
                  marginRight: 6,
                  fontWeight: '500',
                }}>
                Doanh thu
              </Sys.Text>
              <SortIcon />
            </TouchableOpacity>
            <Sys.Text
              style={{
                color: 'white',
                width: 60,
                textAlign: 'right',
                fontWeight: '500',
              }}>
              Thay đổi
            </Sys.Text>
          </View>
          <FlatList
            data={city}
            keyExtractor={(item, index) => `${index}`}
            renderItem={({item, index}) => {
              return <OneItem body={body} item={item} />;
            }}
          />
        </View>
      </ScrollView>
    </Sys.Container>
  );
};

export default ReportDayScreen;

const OneItem = ({item, body}) => {
  const dispatch = useDispatch();
  const [isShow, setIsShow] = useState(false);
  const [branch, setBranch] = useState([]);

  useEffect(() => {
    setIsShow(false);
  }, [body]);

  const getData = () => {
    dispatch(
      ReportActions.getCityBranchDay({
        onSuccess: rs => {
          setBranch(rs?.data?.items);
        },
        onFail: rs => {},
        body: {
          ...body,
          branch: item?.code,
        },
      }),
    );
  };

  return (
    <View
      style={{
        marginBottom: 2,
      }}>
      <TouchableOpacity
        onPress={() => {
          setIsShow(!isShow);
          if (!isShow) {
            getData();
          }
        }}
        style={{
          flexDirection: 'row',
          padding: 10,
          backgroundColor: !isShow ? '#F1F1F1' : '#001451',
          alignItems: 'center',
        }}>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            marginRight: 10,
          }}>
          {!isShow ? <RightCityIcon /> : <DownIcon />}
          <Sys.Text
            style={{
              color: !isShow ? '#001451' : 'white',
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.name}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 120,
          }}>
          <Sys.Text
            style={{
              textAlign: 'center',
              color: !isShow ? '#001451' : 'white',
              fontWeight: '600',
            }}>
            {formatVND(item?.total_revenue)}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 60,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          {item?.percentage_change == 0 ? (
            <></>
          ) : item?.percentage_change > 0 ? (
            <UpIcon />
          ) : (
            <DownsIcon />
          )}
          <Sys.Text
            style={{
              textAlign: 'right',
              color: !isShow ? '#001451' : 'white',
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.percentage_change}%
          </Sys.Text>
        </View>
      </TouchableOpacity>
      {isShow ? (
        <View
          style={{
            backgroundColor: '#F5F6FC',
          }}>
          <FlatList
            data={branch}
            keyExtractor={(item, index) => `${index}`}
            renderItem={({item, index}) => {
              return <OneBranch item={item} body={body} />;
            }}
          />
        </View>
      ) : (
        <></>
      )}
    </View>
  );
};

const OneBranch = ({item, body}) => {
  const dispatch = useDispatch();
  const [agency, setAgency] = useState([]);

  const [isShow, setIsShow] = useState(false);
  const getData = () => {
    dispatch(
      ReportActions.getCityAgencyOfDay({
        onSuccess: rs => {
          setAgency(rs?.data?.items);
        },
        onFail: rs => {},
        body: {
          ...body,
          province: item?.code,
        },
      }),
    );
  };

  return (
    <View>
      <TouchableOpacity
        onPress={() => {
          setIsShow(!isShow);
          if (!isShow) {
            getData();
          }
        }}
        style={{
          flexDirection: 'row',
          padding: 10,
          alignItems: 'center',
          marginBottom: 2,
        }}>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.name}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 120,
          }}>
          <Sys.Text
            style={{
              textAlign: 'center',
              fontWeight: '600',
            }}>
            {formatVND(item?.total_revenue)}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 60,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          {item?.percentage_change == 0 ? (
            <></>
          ) : item?.percentage_change > 0 ? (
            <UpIcon />
          ) : (
            <DownsIcon />
          )}
          <Sys.Text
            style={{
              textAlign: 'right',
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.percentage_change}%
          </Sys.Text>
        </View>
      </TouchableOpacity>
      <View
        style={{
          backgroundColor: '#D1D1D1',
          height: 1,
          marginLeft: 20,
        }}
      />
      {isShow && (
        <View
          style={{
            marginLeft: 10,
          }}>
          <FlatList
            data={agency}
            keyExtractor={(item, index) => `${index}-agency`}
            renderItem={({item, index}) => {
              return (
                <View>
                  <View
                    style={{
                      flexDirection: 'row',
                      padding: 10,
                      alignItems: 'center',
                      marginBottom: 2,
                    }}>
                    <View
                      style={{
                        flex: 1,
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      {item?.name ? (
                        <Sys.Text
                          style={{
                            marginLeft: 10,
                          }}>
                          {item?.name}
                        </Sys.Text>
                      ) : (
                        <Sys.Text
                          style={{
                            marginLeft: 10,
                          }}>
                          {item?.code} {item?.address}
                        </Sys.Text>
                      )}
                    </View>
                    <View
                      style={{
                        width: 120,
                      }}>
                      <Sys.Text
                        style={{
                          textAlign: 'center',
                        }}>
                        {formatVND(item?.total_revenue)}
                      </Sys.Text>
                    </View>
                    <View
                      style={{
                        width: 60,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      {item?.percentage_change == 0 ? (
                        <></>
                      ) : item?.percentage_change > 0 ? (
                        <UpIcon />
                      ) : (
                        <DownsIcon />
                      )}
                      <Sys.Text
                        style={{
                          textAlign: 'right',
                          marginLeft: 10,
                        }}>
                        {item?.percentage_change}%
                      </Sys.Text>
                    </View>
                  </View>
                  <View
                    style={{
                      backgroundColor: '#D1D1D1',
                      height: 1,
                      marginLeft: 20,
                    }}
                  />
                </View>
              );
            }}
          />
        </View>
      )}
    </View>
  );
};
const CustomTickLabel = props => {
  return <VictoryLabel {...props} text={`${formatVND(props.text)}`} />;
};
