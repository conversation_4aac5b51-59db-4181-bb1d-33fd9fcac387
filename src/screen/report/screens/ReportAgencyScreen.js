import {useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useMemo, useState} from 'react';
import {
  FlatList,
  ScrollView,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {
  VictoryAxis,
  VictoryBar,
  VictoryChart,
  VictoryLabel,
  VictoryStack,
  VictoryTheme,
} from 'victory-native';
import Sys from '../../../components/Sys';
import {formatVND} from '../../../services/util';
import DownIcon from '../icons/DownIcon';
import DownsIcon from '../icons/DownsIcon';
import FilterIcon from '../icons/FilterIcon';
import RightCityIcon from '../icons/RightCityIcon';
import SortIcon from '../icons/SortIcon';
import UpIcon from '../icons/UpIcon';
import {ReportActions} from '../services/report.slice';
import {createArrayWithSteps} from './ReportDayScreen';

const ReportAgencyScreen = () => {
  const {navigate} = useNavigation();
  const dispatch = useDispatch();
  const {width} = useWindowDimensions();
  const [city, setCity] = useState([]);
  const [game, setGame] = useState([]);
  const [chart, setChart] = useState([]);
  const [totalRevenue, setTotalRevenue] = useState(0);
  const [body, setBody] = useState({
    event_time: moment().format('YYYY-MM'),
    sort_order: 'asc',
    sort_by: 'total_revenue',
  });

  useEffect(() => {
    getData();
  }, [body]);

  const getData = () => {
    dispatch(
      ReportActions.getCityAgencyChart({
        onSuccess: rs => {
          const temp = formatData(rs?.data?.items);
          setChart(temp.data);
          setTotalRevenue(temp?.maxCompany?.totalRevenue);
        },
        onFail: rs => {},
        body,
      }),
    );

    dispatch(
      ReportActions.getGames({
        onSuccess: rs => {
          setGame(rs?.data);
        },
        onFail: rs => {},
      }),
    );

    dispatch(
      ReportActions.getCityAgencyList({
        onSuccess: rs => {
          setCity(rs?.data?.items);
        },
        onFail: rs => {},
        body: {
          ...body,
        },
      }),
    );
  };

  const renderChart = useMemo(() => {
    const getHeight = () => {
      try {
        if (chart.length == 0) {
          return 200;
        }

        const dynamicHeight = chart.filter(x => x != undefined)[0].length * 30;

        return dynamicHeight > width
          ? dynamicHeight
          : chart.filter(x => x != undefined)[0].length * 50;
      } catch (error) {
        console.log({error});
        return 0;
      }
    };

    return (
      <ScrollView
        nestedScrollEnabled={true}
        showsVerticalScrollIndicator={false}
        style={{
          maxHeight: width,
        }}>
        <VictoryChart
          theme={VictoryTheme.material}
          padding={{
            left: 80,
            top: 40,
            bottom: 40,
            right: 40,
          }}
          height={getHeight()}
          width={width}>
          <VictoryAxis axisComponent={<></>} />
          <VictoryAxis
            dependentAxis
            axisComponent={<></>}
            tickComponent={<></>}
            tickLabelComponent={<CustomTickLabel />}
            labelComponent={<></>}
            tickFormat={createArrayWithSteps(totalRevenue)}
          />
          <VictoryStack
            horizontal
            colorScale={(game || []).filter(x => !!x?.color).map(x => x.color)}>
            {chart
              .filter(x => x != undefined)
              .map((x, index) => {
                return (
                  <VictoryBar
                    style={{
                      data: {
                        width: 20,
                      },
                    }}
                    data={x}
                  />
                );
              })}
          </VictoryStack>
        </VictoryChart>
        <View
          style={{
            height: 40,
          }}
        />
      </ScrollView>
    );
  }, [chart]);

  return (
    <Sys.Container backgroundColor="white" hasHeader>
      <Sys.Header
        title={'Báo cáo đại lý'}
        right={
          <View>
            <TouchableOpacity
              onPress={() => {
                navigate('ReportFilterScreen', {
                  type: 'agency',
                  body,
                  onAccept: newBody => {
                    setBody(newBody);
                  },
                });
              }}
              style={{
                paddingHorizontal: 20,
              }}>
              <FilterIcon />
            </TouchableOpacity>
          </View>
        }
      />
      <ScrollView showsVerticalScrollIndicator={false}>
        <View>
          <Sys.Text
            style={{
              textAlign: 'center',
              color: '#0062FF',
              fontWeight: '600',
              marginBottom: 5,
              marginTop: 12,
            }}>
            Doanh thu theo tháng{' '}
            {moment(body?.event_time, 'YYYY-MM-DD').format('MM/YYYY')}
          </Sys.Text>
        </View>
        {renderChart}
        <View
          style={{
            padding: 30,
            paddingTop: 0,
          }}>
          <FlatList
            data={(game || []).filter(x => !!x?.color)}
            numColumns={3}
            renderItem={({item, index}) => {
              return (
                <View
                  style={{
                    width: '33%',
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <View
                    style={{
                      width: 10,
                      height: 10,
                      marginRight: 10,
                      backgroundColor: item?.color,
                    }}
                  />
                  <Sys.Text
                    style={{
                      color: '#666',
                      fontSize: 12,
                    }}>
                    {item?.name}
                  </Sys.Text>
                </View>
              );
            }}
            keyExtractor={(item, index) => `${index}`}
          />
        </View>
        <View
          style={{
            marginHorizontal: 10,
          }}>
          <View
            style={{
              backgroundColor: '#376DF7',
              flexDirection: 'row',
              padding: 10,
            }}>
            <Sys.Text
              style={{
                color: 'white',
                flex: 1,
                fontWeight: '500',
              }}>
              Đại lý
            </Sys.Text>
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                width: 120,
              }}
              onPress={() => {
                setBody({
                  ...body,
                  sort_order: body.sort_order === 'desc' ? 'asc' : 'desc',
                });
              }}>
              <Sys.Text
                style={{
                  color: 'white',
                  marginRight: 6,
                  fontWeight: '500',
                }}>
                Doanh thu
              </Sys.Text>
              <SortIcon />
            </TouchableOpacity>
            <Sys.Text
              style={{
                color: 'white',
                width: 60,
                textAlign: 'right',
                fontWeight: '500',
              }}>
              Thay đổi
            </Sys.Text>
          </View>
          <FlatList
            data={city}
            keyExtractor={(item, index) => `${index}`}
            renderItem={({item, index}) => {
              return <OneItem item={item} body={body} />;
            }}
            ListFooterComponent={<View style={{height: 50}} />}
          />
        </View>
      </ScrollView>
    </Sys.Container>
  );
};

export default ReportAgencyScreen;

const OneItem = ({item, body}) => {
  const dispatch = useDispatch();
  const [isShow, setIsShow] = useState(false);
  const [branch, setBranch] = useState([]);
  useEffect(() => {
    setIsShow(false);
  }, [body]);

  const getData = () => {
    dispatch(
      ReportActions.getCityAgencyByAgency({
        onSuccess: rs => {
          setBranch(rs?.data?.items);
        },
        onFail: rs => {},
        body: {
          ...body,
          agency: [item?.code],
          event_time: moment(body.event_time, 'YYYY-MM-DD').format('YYYY-MM'),
        },
      }),
    );
  };

  return (
    <View
      style={{
        marginBottom: 2,
      }}>
      <TouchableOpacity
        onPress={() => {
          setIsShow(!isShow);
          if (!isShow) {
            getData();
          }
        }}
        style={{
          flexDirection: 'row',
          padding: 10,
          backgroundColor: !isShow ? '#F1F1F1' : '#001451',
          alignItems: 'center',
        }}>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            marginRight: 10,
          }}>
          {!isShow ? <RightCityIcon /> : <DownIcon />}
          <Sys.Text
            style={{
              color: !isShow ? '#001451' : 'white',
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.name}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 120,
          }}>
          <Sys.Text
            style={{
              textAlign: 'center',
              color: !isShow ? '#001451' : 'white',
              fontWeight: '600',
            }}>
            {formatVND(item?.total_revenue)}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 60,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          {item?.percentage_change == 0 ? (
            <></>
          ) : item?.percentage_change > 0 ? (
            <UpIcon />
          ) : (
            <DownsIcon />
          )}
          <Sys.Text
            style={{
              textAlign: 'right',
              color: !isShow ? '#001451' : 'white',
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.percentage_change}%
          </Sys.Text>
        </View>
      </TouchableOpacity>
      {isShow ? (
        <View
          style={{
            backgroundColor: '#F5F6FC',
          }}>
          <FlatList
            data={branch}
            keyExtractor={(item, index) => `${index}`}
            renderItem={itemProps => {
              return (
                <OneBranch
                  item={itemProps.item}
                  agencyCode={item?.code}
                  body={body}
                />
              );
            }}
          />
        </View>
      ) : (
        <></>
      )}
    </View>
  );
};

const OneBranch = ({item, body, agencyCode}) => {
  const dispatch = useDispatch();
  const [agency, setAgency] = useState([]);
  const [isShow, setIsShow] = useState(false);

  const getData = () => {
    dispatch(
      ReportActions.getCityAgencyByPos({
        onSuccess: rs => {
          setAgency(rs?.data?.items);
        },
        onFail: rs => {},
        body: {
          ...body,
          agency: [agencyCode],
          province: item?.code,
          event_time: moment(body.event_time, 'YYYY-MM-DD').format('YYYY-MM'),
        },
      }),
    );
  };

  return (
    <View>
      <TouchableOpacity
        onPress={() => {
          setIsShow(!isShow);
          if (!isShow) {
            getData();
          }
        }}
        style={{
          flexDirection: 'row',
          padding: 10,
          alignItems: 'center',
          marginBottom: 2,
        }}>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.name}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 120,
          }}>
          <Sys.Text
            style={{
              textAlign: 'center',
              fontWeight: '600',
            }}>
            {formatVND(item?.total_revenue)}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 60,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          {item?.percentage_change == 0 ? (
            <></>
          ) : item?.percentage_change > 0 ? (
            <UpIcon />
          ) : (
            <DownsIcon />
          )}
          <Sys.Text
            style={{
              textAlign: 'right',
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.percentage_change}%
          </Sys.Text>
        </View>
      </TouchableOpacity>
      <View
        style={{
          backgroundColor: '#D1D1D1',
          height: 1,
          marginLeft: 20,
        }}
      />
      <View
        style={{
          marginLeft: 10,
        }}>
        <FlatList
          data={agency}
          keyExtractor={(item, index) => `${index}-agency`}
          renderItem={({item, index}) => {
            return (
              <View>
                <View
                  style={{
                    flexDirection: 'row',
                    padding: 10,
                    alignItems: 'center',
                    marginBottom: 2,
                  }}>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    {item?.name ? (
                      <Sys.Text
                        style={{
                          marginLeft: 10,
                        }}>
                        {item?.name}
                      </Sys.Text>
                    ) : (
                      <Sys.Text
                        style={{
                          marginLeft: 10,
                        }}>
                        {item?.code} {item?.address}
                      </Sys.Text>
                    )}
                  </View>
                  <View
                    style={{
                      width: 120,
                    }}>
                    <Sys.Text
                      style={{
                        textAlign: 'center',
                      }}>
                      {formatVND(item?.total_revenue)}
                    </Sys.Text>
                  </View>
                  <View
                    style={{
                      width: 60,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    {item?.percentage_change == 0 ? (
                      <></>
                    ) : item?.percentage_change > 0 ? (
                      <UpIcon />
                    ) : (
                      <DownsIcon />
                    )}
                    <Sys.Text
                      style={{
                        textAlign: 'right',
                        marginLeft: 10,
                      }}>
                      {item?.percentage_change}%
                    </Sys.Text>
                  </View>
                </View>
                <View
                  style={{
                    backgroundColor: '#D1D1D1',
                    height: 1,
                    marginLeft: 20,
                  }}
                />
              </View>
            );
          }}
        />
      </View>
    </View>
  );
};

const CustomTickLabel = props => {
  const formatRevenue = amount => {
    if (amount == 0) {
      return 0;
    }
    if (amount >= 1e9) {
      return `${(amount / 1e9).toFixed(2)} Tỷ`;
    } else {
      return `${(amount / 1e6).toFixed(2)} Triệu`;
    }
  };

  return (
    <VictoryLabel {...props} text={`${formatRevenue(Number(props.text))}`} />
  );
};

export function formatData(data) {
  let result = [];
  let maxRevenue = 0;
  let maxCompany = null;

  for (let key in data) {
    let company = data[key];
    let items = company.items;
    let totalCompanyRevenue = 0;

    for (let itemKey in items) {
      if (result[itemKey - 1] === undefined) {
        result[itemKey - 1] = [];
      }

      let revenue = items[itemKey].total_revenue;
      totalCompanyRevenue += revenue;

      result[itemKey - 1].push({
        x: company.code,
        y: revenue,
      });
    }

    if (totalCompanyRevenue > maxRevenue) {
      maxRevenue = totalCompanyRevenue;
      maxCompany = company;
    }
  }

  return {
    data: result,
    maxCompany: {
      company: maxCompany,
      totalRevenue: maxRevenue,
    },
  };
}
