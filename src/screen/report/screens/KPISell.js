import React from 'react';
import {View} from 'react-native';
import {VictoryPie} from 'victory-native';
import Sys from '../../../components/Sys';
const KPISell = () => {
  return (
    <View
      style={{
        margin: 20,
        borderRadius: 12,
        overflow: 'hidden',
      }}>
      <View
        style={{
          backgroundColor: 'white',
          padding: 10,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
          }}>
          <View>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                marginBottom: 10,
              }}>
              K<PERSON> Doanh thu bán hàng
            </Sys.Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                color: '#90BB3B',
                width: 75,
                marginBottom: 5,
              }}>
              6 Tỷ
            </Sys.Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
                marginBottom: 5,
              }}>
              doanh thu
            </Sys.Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                color: '#EF7721',
                width: 75,
                marginBottom: 5,
              }}>
              98.255
            </Sys.Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
              }}>
              vé bán
            </Sys.Text>
          </View>
        </View>
        <View
          style={{
            flex: 1,
            height: 100,
            alignItems: 'center',
          }}>
          <View
            style={{
              position: 'absolute',
              top: -30,
            }}>
            <VictoryPie
              cornerRadius={25}
              width={200} // Đặt chiều rộng của biểu đồ
              height={200} // Đặt chiều cao của biểu đồ
              data={[
                {x: 1, y: 1}, // Phần dữ liệu chính
                {x: 2, y: 0}, // Đặt thành 0 để không hiển thị
              ]}
              colorScale={['#E2E2EA', 'transparent']} // Màu xám cho phần nhỏ
              innerRadius={65}
              startAngle={-100} // Bắt đầu từ 90 độ, nơi phần xanh kết thúc
              endAngle={100} // Kết thúc cùng một điểm, tạo bo tròn
              style={{
                labels: {fill: 'none'},
              }}
            />
          </View>
          <View
            style={{
              position: 'absolute',
              top: -30,
            }}>
            <VictoryPie
              cornerRadius={25}
              width={200} // Đặt chiều rộng của biểu đồ
              height={200} // Đặt chiều cao của biểu đồ
              data={[
                {x: 1, y: 0.6}, // Phần dữ liệu chính
                {x: 2, y: 0.15}, // Đặt thành 0 để không hiển thị
              ]}
              colorScale={['#0062FF', '#E2E2EA']} // Màu xám cho phần nhỏ
              innerRadius={65}
              startAngle={-100} // Bắt đầu từ 90 độ, nơi phần xanh kết thúc
              endAngle={100} // Kết thúc cùng một điểm, tạo bo tròn
              style={{
                labels: {fill: 'none'},
              }}
            />
          </View>
          <View
            style={{
              position: 'absolute',
              bottom: 24,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Sys.Text
              style={{
                color: '#0062FF',
                fontSize: 25,
              }}>
              60%
            </Sys.Text>
          </View>
          <View
            style={{
              position: 'absolute',
              bottom: 0,
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%',
              paddingHorizontal: 10,
              flexDirection: 'row',
            }}>
            <View style={{}}>
              <Sys.Text
                style={{
                  fontSize: 12,
                  lineHeight: 16,
                  color: '#848484',
                }}>
                0 Tỷ
              </Sys.Text>
            </View>
            <View style={{}}>
              <Sys.Text
                style={{
                  fontSize: 12,
                  lineHeight: 16,
                  color: '#0062FF',
                }}>
                10 Tỷ
              </Sys.Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default KPISell;
