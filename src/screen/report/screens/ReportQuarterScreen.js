import {useNavigation} from '@react-navigation/native';
import * as _ from 'lodash';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {
  FlatList,
  ScrollView,
  TouchableOpacity,
  View,
  useWindowDimensions,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {
  VictoryAxis,
  VictoryBar,
  VictoryChart,
  VictoryGroup,
  VictoryLabel,
  VictoryTheme,
} from 'victory-native';
import Sys from '../../../components/Sys';
import {formatVND} from '../../../services/util';
import {AccountSelectors} from '../../account/services/account.slice';
import KPIBranchManager from '../../home/<USER>/KPIBranchManager';
import DownIcon from '../icons/DownIcon';
import DownsIcon from '../icons/DownsIcon';
import FilterIcon from '../icons/FilterIcon';
import RightCityIcon from '../icons/RightCityIcon';
import SortIcon from '../icons/SortIcon';
import UpIcon from '../icons/UpIcon';
import {ReportActions} from '../services/report.slice';
import KPIBranchManagerYearQuarter from '../../home/<USER>/KPIBranchManagerYearQuarter';
import KPIBranchManagerYear from '../../home/<USER>/KPIBranchManagerYear';
const ReportQuarterScreen = () => {
  const dispatch = useDispatch();
  const {navigate} = useNavigation();
  const {width} = useWindowDimensions();
  const [city, setCity] = useState([]);
  const [count, setCount] = useState([]);
  const [chart, setChart] = useState([]);
  const view = useSelector(AccountSelectors.view);

  const labelList = [
    {
      label: 'Năm hiện tại',
      color: '#094DFA',
    },
    {
      label: 'Kỳ trước',
      color: '#F5A623',
    },
    {
      label: 'Kế hoạch',
      color: '#D0021B',
    },
  ];

  const [body, setBody] = useState({
    event_time: moment().format('YYYY-MM'),
  });
  useEffect(() => {
    getData();
  }, [body]);

  const getData = () => {
    dispatch(
      ReportActions.getReportQuarterChartBLD({
        onSuccess: rs => {
          const temp = _.cloneDeep(rs.data);
          delete temp.stats;
          if (temp) {
            setChart(Object.values(temp).map(x => x.stats));
          }
        },
        onFail: rs => {},
        body: {
          year: moment(body.event_time, 'YYYY-MM').format('YYYY'),
        },
      }),
    );
    // lấy dữ liệu chart
    // if (view === 'branch.manager') {
    //   dispatch(
    //     ReportActions.getReportQuarterChartBLD({
    //       onSuccess: rs => {
    //         const temp = _.cloneDeep(rs.data);
    //         delete temp.stats;
    //         if (temp) {
    //           setChart(Object.values(temp).map(x => x.stats));
    //         }
    //       },
    //       onFail: rs => {},
    //       body: {
    //         year: moment(body.event_time, 'YYYY-MM').format('YYYY'),
    //       },
    //     }),
    //   );
    // } else {
    //   dispatch(
    //     ReportActions.getReportQuarterChart({
    //       onSuccess: rs => {
    //         setChart(rs);
    //       },
    //       onFail: rs => {},
    //       body: {
    //         year: moment(body.event_time, 'YYYY-MM').format('YYYY'),
    //       },
    //     }),
    //   );
    // }

    dispatch(
      ReportActions.getReportQuarterCity({
        onSuccess: rs => {
          setCity(rs?.data?.items);
        },
        onFail: rs => {},
        body: {
          ...body,
          sort_order: 'desc',
          sort_by: 'total_revenue',
        },
      }),
    );
  };

  const cal = (current, pre) => {
    try {
      const temp = Number(((current - pre) / pre) * 100);
      if (temp < 0) {
        return `-${temp}`;
      } else {
        return `+${temp}`;
      }
    } catch (error) {
      return '';
    }
  };

  const compareList = [
    {
      title: 'POS có DT',
      percent: cal(count?.pos_count, count?.previous_pos_count),
      value: formatVND(count?.pos_count),
      compareWith: formatVND(count?.previous_pos_count),
    },
    {
      title: 'DT/POS',
      percent: cal(
        Number(count?.total_revenue / count?.pos_count).toFixed(0),
        Number(count?.previous_revenue / count?.previous_pos_count).toFixed(0),
      ),
      value: formatVND(
        Number(count?.total_revenue / count?.pos_count).toFixed(0),
      ),
      compareWith: formatVND(
        Number(count?.previous_revenue / count?.previous_pos_count).toFixed(0),
      ),
    },
    {
      title: 'Số vé',
      percent: cal(count?.net_tickets_sold, count?.previous_net_tickets_sold),
      value: formatVND(count?.net_tickets_sold),
      compareWith: formatVND(count?.previous_net_tickets_sold),
    },
    {
      title: 'DT/Vé',
      percent: cal(
        Number(count?.total_revenue / count?.net_tickets_sold).toFixed(0),
        Number(
          count?.previous_revenue / count?.previous_net_tickets_sold,
        ).toFixed(0),
      ),
      value: formatVND(
        Number(count?.total_revenue / count?.net_tickets_sold).toFixed(0),
      ),
      compareWith: formatVND(
        Number(
          count?.previous_revenue / count?.previous_net_tickets_sold,
        ).toFixed(0),
      ),
    },
  ];

  return (
    <Sys.Container backgroundColor="white" hasHeader>
      <Sys.Header
        title={'Báo cáo quý'}
        right={
          <View>
            <TouchableOpacity
              onPress={() => {
                navigate('ReportFilterScreen', {
                  type: 'quarter',
                  body,
                  onAccept: newBody => {
                    setBody(newBody);
                  },
                });
              }}
              style={{
                paddingHorizontal: 20,
              }}>
              <FilterIcon />
            </TouchableOpacity>
          </View>
        }
      />
      <ScrollView showsVerticalScrollIndicator={false}>
        <View>
          {['branch.manager', 'branch.owner'].indexOf(view) > -1 ? (
            <View
              style={[
                {
                  width: width - 20,
                  borderRadius: 20,
                  marginHorizontal: 10,
                  marginTop: 10,
                  gap: 10,
                  flexDirection: 'row',
                },
              ]}>
              <KPIBranchManagerYearQuarter currentDate={body?.event_time} />
              <KPIBranchManagerYear currentDate={body?.event_time} />
            </View>
          ) : (
            <KPIBranchManager />
          )}
        </View>
        <View
          style={{
            marginTop: 20,
          }}>
          <Sys.Text
            style={{
              textAlign: 'center',
              color: '#0062FF',
              fontWeight: '600',
              marginBottom: 5,
            }}>
            Doanh thu theo quý năm{' '}
            {moment(body.event_time, 'YYYY-MM').format('YYYY')}
          </Sys.Text>
        </View>
        <VictoryChart
          padding={{
            left: 70,
            bottom: 50,
            top: 30,
            right: 20,
          }}
          domainPadding={{x: 40}}
          width={width}
          theme={VictoryTheme.material}>
          <VictoryAxis
            tickValues={chart.map(x => {
              return Number(x?.name);
            })}
          />
          <VictoryAxis
            dependentAxis
            axisComponent={<></>}
            tickComponent={<></>}
            tickLabelComponent={<CustomTickLabel />}
            labelComponent={<></>}
          />
          <VictoryGroup offset={16}>
            <VictoryBar
              style={{
                data: {
                  width: 15,
                  fill: '#094DFA',
                },
              }}
              data={chart.map((x, index) => {
                if (!!x) {
                  return {
                    x: `${index + 1}`,
                    y: Number(x?.actual_revenue),
                  };
                } else {
                  return {
                    x: `${index + 1}`,
                    y: 0,
                  };
                }
              })}
            />
            <VictoryBar
              style={{
                data: {
                  width: 15,
                  fill: '#D0021B',
                },
              }}
              data={chart.map((x, index) => {
                if (!!x) {
                  return {
                    x: `${index + 1}`,
                    y: Number(x?.revenue_amount),
                  };
                } else {
                  return {
                    x: `${index + 1}`,
                    y: 0,
                  };
                }
              })}
            />
            <VictoryBar
              style={{
                data: {
                  width: 15,
                  fill: '#F5A623',
                },
              }}
              data={chart.map((x, index) => {
                if (!!x) {
                  return {
                    x: `${index + 1}`,
                    y: Number(x?.previous_actual_revenue),
                  };
                } else {
                  return {
                    x: `${index + 1}`,
                    y: 0,
                  };
                }
              })}
            />
          </VictoryGroup>
        </VictoryChart>
        {/* {view === 'branch.manager' ? (
          <VictoryChart
            padding={{
              left: 70,
              bottom: 50,
              top: 30,
              right: 20,
            }}
            domainPadding={{x: 40}}
            width={width}
            theme={VictoryTheme.material}>
            <VictoryAxis
              tickValues={chart.map(x => {
                return Number(x?.name);
              })}
            />
            <VictoryAxis
              dependentAxis
              axisComponent={<></>}
              tickComponent={<></>}
              tickLabelComponent={<CustomTickLabel />}
              labelComponent={<></>}
            />
            <VictoryGroup offset={16}>
              <VictoryBar
                style={{
                  data: {
                    width: 15,
                    fill: '#094DFA',
                  },
                }}
                data={chart.map((x, index) => {
                  if (!!x) {
                    return {
                      x: `${index + 1}`,
                      y: Number(x?.actual_revenue),
                    };
                  } else {
                    return {
                      x: `${index + 1}`,
                      y: 0,
                    };
                  }
                })}
              />
              <VictoryBar
                style={{
                  data: {
                    width: 15,
                    fill: '#D0021B',
                  },
                }}
                data={chart.map((x, index) => {
                  if (!!x) {
                    return {
                      x: `${index + 1}`,
                      y: Number(x?.revenue_amount),
                    };
                  } else {
                    return {
                      x: `${index + 1}`,
                      y: 0,
                    };
                  }
                })}
              />
              <VictoryBar
                style={{
                  data: {
                    width: 15,
                    fill: '#F5A623',
                  },
                }}
                data={chart.map((x, index) => {
                  if (!!x) {
                    return {
                      x: `${index + 1}`,
                      y: Number(x?.previous_actual_revenue),
                    };
                  } else {
                    return {
                      x: `${index + 1}`,
                      y: 0,
                    };
                  }
                })}
              />
            </VictoryGroup>
          </VictoryChart>
        ) : (
          <VictoryChart
            padding={{
              left: 70,
              bottom: 50,
              top: 30,
              right: 20,
            }}
            domainPadding={{x: 40}}
            width={width}
            theme={VictoryTheme.material}>
            <VictoryAxis
              tickValues={chart.map(x => {
                return Number(x?.name);
              })}
            />
            <VictoryAxis
              dependentAxis
              axisComponent={<></>}
              tickComponent={<></>}
              tickLabelComponent={<CustomTickLabel />}
              labelComponent={<></>}
            />
            <VictoryGroup offset={16}>
              <VictoryBar
                style={{
                  data: {
                    width: 15,
                    fill: '#094DFA',
                  },
                }}
                data={chart.map((x, index) => {
                  return {
                    x: `${index + 1}`,
                    y: x.totalRevenue,
                  };
                })}
              />
              <VictoryBar
                style={{
                  data: {
                    width: 15,
                    fill: '#D0021B',
                  },
                }}
                data={chart.map((x, index) => {
                  return {
                    x: `${index + 1}`,
                    y: x.totalActualRevenue,
                  };
                })}
              />
              <VictoryBar
                style={{
                  data: {
                    width: 15,
                    fill: '#F5A623',
                  },
                }}
                data={chart.map((x, index) => {
                  return {
                    x: `${index + 1}`,
                    y: x.totalPreviousYearRevenue,
                  };
                })}
              />
            </VictoryGroup>
          </VictoryChart>
        )} */}
        <View
          style={{
            flexDirection: 'row',
            marginHorizontal: 20,
            marginBottom: 30,
          }}>
          {labelList.map((x, index) => {
            return (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}
                key={`${index}`}>
                <View
                  style={{
                    backgroundColor: x.color,
                    width: 10,
                    height: 10,
                    marginRight: 10,
                  }}
                />
                <Sys.Text>{x.label}</Sys.Text>
              </View>
            );
          })}
        </View>
        {/* 
        <View
          style={{
            backgroundColor: '#376DF7',
            flexDirection: 'row',
            padding: 10,
          }}>
          <Sys.Text
            style={{
              color: 'white',
              flex: 1,
              fontWeight: '500',
            }}>
            Thành phố
          </Sys.Text>
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              width: 120,
            }}
            onPress={() => {
              setBody({
                ...body,
                sort_order: body.sort_order === 'desc' ? 'asc' : 'desc',
              });
            }}>
            <Sys.Text
              style={{
                color: 'white',
                marginRight: 6,
                fontWeight: '500',
              }}>
              Doanh thu
            </Sys.Text>
            <SortIcon />
          </TouchableOpacity>
          <Sys.Text
            style={{
              color: 'white',
              width: 60,
              textAlign: 'right',
              fontWeight: '500',
            }}>
            Thay đổi
          </Sys.Text>
        </View>
        <FlatList
          data={city}
          keyExtractor={(item, index) => `${index}`}
          renderItem={({item, index}) => {
            return <OneItem body={body} item={item} />;
          }}
        /> */}
      </ScrollView>
    </Sys.Container>
  );
};

export default ReportQuarterScreen;
const CustomTickLabel = props => {
  function formatVND(value) {
    if (value < 1000) {
      return '';
    }
    // Chuyển đổi giá trị thành số và chia cho 1 triệu
    let formattedValue = Number(value.replace(/,/g, '')) / 1000000;

    // Kiểm tra nếu giá trị lớn hơn 1000 triệu (tức là 1 tỷ)
    if (formattedValue > 999) {
      // Chuyển đổi giá trị thành tỷ và cập nhật đơn vị
      return `${formattedValue / 1000} Tỷ`;
    } else {
      // Giữ nguyên giá trị triệu và cập nhật đơn vị
      return `${formattedValue} Tr`;
    }
  }

  return <VictoryLabel {...props} text={`${formatVND(props.text)}`} />;
};
const OneItem = ({item, body}) => {
  const dispatch = useDispatch();
  const [isShow, setIsShow] = useState(false);
  const [branch, setBranch] = useState([]);
  useEffect(() => {
    setIsShow(false);
  }, [body]);

  const getData = () => {
    dispatch(
      ReportActions.getReportQuarterBranch({
        onSuccess: rs => {
          setBranch(rs?.data?.items);
        },
        onFail: rs => {},
        body: {
          ...body,
          province: item?.code,
          event_time: moment(body.event_time, 'YYYY-MM-DD').format('YYYY-MM'),
        },
      }),
    );
  };

  return (
    <View
      style={{
        marginBottom: 2,
      }}>
      <TouchableOpacity
        onPress={() => {
          setIsShow(!isShow);
          if (!isShow) {
            getData();
          }
        }}
        style={{
          flexDirection: 'row',
          padding: 10,
          backgroundColor: !isShow ? '#F1F1F1' : '#001451',
          alignItems: 'center',
        }}>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            marginRight: 10,
          }}>
          {!isShow ? <RightCityIcon /> : <DownIcon />}
          <Sys.Text
            style={{
              color: !isShow ? '#001451' : 'white',
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.name}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 120,
          }}>
          <Sys.Text
            style={{
              textAlign: 'center',
              color: !isShow ? '#001451' : 'white',
              fontWeight: '600',
            }}>
            {formatVND(item?.total_revenue)}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 60,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          {item?.percentage_change == 0 ? (
            <></>
          ) : item?.percentage_change > 0 ? (
            <UpIcon />
          ) : (
            <DownsIcon />
          )}
          <Sys.Text
            style={{
              textAlign: 'right',
              color: !isShow ? '#001451' : 'white',
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.percentage_change}%
          </Sys.Text>
        </View>
      </TouchableOpacity>
      {isShow ? (
        <View
          style={{
            backgroundColor: '#F5F6FC',
          }}>
          <FlatList
            data={branch}
            keyExtractor={(item, index) => `${index}`}
            renderItem={({item, index}) => {
              return <OneBranch item={item} body={body} />;
            }}
          />
        </View>
      ) : (
        <></>
      )}
    </View>
  );
};

const OneBranch = ({item, body}) => {
  const dispatch = useDispatch();
  const [agency, setAgency] = useState([]);
  const [isShow, setIsShow] = useState(false);

  const getData = () => {
    dispatch(
      ReportActions.getReportQuarterPos({
        onSuccess: rs => {
          setAgency(rs?.data?.items);
        },
        onFail: rs => {},
        body: {
          ...body,
          agency: [item?.code],
          event_time: moment(body.event_time, 'YYYY-MM-DD').format('YYYY-MM'),
        },
      }),
    );
  };

  return (
    <View>
      <TouchableOpacity
        onPress={() => {
          setIsShow(!isShow);
          if (!isShow) {
            getData();
          }
        }}
        style={{
          flexDirection: 'row',
          padding: 10,
          alignItems: 'center',
          marginBottom: 2,
        }}>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <Sys.Text
            style={{
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.name}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 120,
          }}>
          <Sys.Text
            style={{
              textAlign: 'center',
              fontWeight: '600',
            }}>
            {formatVND(item?.total_revenue)}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 60,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          {item?.percentage_change == 0 ? (
            <></>
          ) : item?.percentage_change > 0 ? (
            <UpIcon />
          ) : (
            <DownsIcon />
          )}
          <Sys.Text
            style={{
              textAlign: 'right',
              fontWeight: '600',
              marginLeft: 10,
            }}>
            {item?.percentage_change}%
          </Sys.Text>
        </View>
      </TouchableOpacity>
      <View
        style={{
          backgroundColor: '#D1D1D1',
          height: 1,
          marginLeft: 20,
        }}
      />
      {isShow ? (
        <View
          style={{
            marginLeft: 10,
          }}>
          <FlatList
            data={agency}
            keyExtractor={(item, index) => `${index}-agency`}
            renderItem={({item, index}) => {
              return (
                <View>
                  <View
                    style={{
                      flexDirection: 'row',
                      padding: 10,
                      alignItems: 'center',
                      marginBottom: 2,
                    }}>
                    <View
                      style={{
                        flex: 1,
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      {item?.name ? (
                        <Sys.Text
                          style={{
                            marginLeft: 10,
                          }}>
                          {item?.name}
                        </Sys.Text>
                      ) : (
                        <Sys.Text
                          style={{
                            marginLeft: 10,
                          }}>
                          {item?.code} {item?.address}
                        </Sys.Text>
                      )}
                    </View>
                    <View
                      style={{
                        width: 120,
                      }}>
                      <Sys.Text
                        style={{
                          textAlign: 'center',
                        }}>
                        {formatVND(item?.total_revenue)}
                      </Sys.Text>
                    </View>
                    <View
                      style={{
                        width: 60,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      {item?.percentage_change == 0 ? (
                        <></>
                      ) : item?.percentage_change > 0 ? (
                        <UpIcon />
                      ) : (
                        <DownsIcon />
                      )}
                      <Sys.Text
                        style={{
                          textAlign: 'right',
                          marginLeft: 10,
                        }}>
                        {item?.percentage_change}%
                      </Sys.Text>
                    </View>
                  </View>
                  <View
                    style={{
                      backgroundColor: '#D1D1D1',
                      height: 1,
                      marginLeft: 20,
                    }}
                  />
                </View>
              );
            }}
          />
        </View>
      ) : (
        <></>
      )}
    </View>
  );
};
