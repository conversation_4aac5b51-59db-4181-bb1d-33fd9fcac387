import {createSlice} from '@reduxjs/toolkit';
import moment from 'moment';

const initialState = {};

const ReportSlice = createSlice({
  name: 'report',
  initialState,
  reducers: {
    getCityMonth: () => {},
    getCityBranchMonth: () => {},
    getCityAgencyByPos: () => {},
    getCityAgencyChart: () => {},
    getGames: () => {},
    getCityMonthCount: () => {},
    getCityMonthChart: () => {},
    getCityDay: () => {},
    getCityBranchDay: () => {},
    getCityAgencyByAgency: () => {},
    getCityDayCount: () => {},
    getCityDayChart: () => {},
    getCityAgencyList: () => {},
    getCityAgencyOfDay: () => {},
    getCityAgencyOfMonth: () => {},
    getReportQuarterChart: () => {},
    getReportQuarterChartBLD: () => {},
    getReportQuarterCount: () => {},
    getReportQuarterCity: () => {},
    getReportBranchChart: () => {},
    getReportBranchList: () => {},
    getReportBranchByBranch: () => {},
    getReportQuarterBranch: () => {},
    getReportQuarterPos: () => {},
    getAgentHomeChart: () => {},
  },
});

const ReportReducers = ReportSlice.reducer;
export default ReportReducers;

export const ReportActions = ReportSlice.actions;
export const ReportSelectors = {};
