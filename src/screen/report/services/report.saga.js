import {takeEvery, takeLatest} from 'redux-saga/effects';
import SysFetch from '../../../services/fetch';
import {ReportActions} from './report.slice';

import qs from 'qs';
import moment from 'moment';

function* ReportSaga() {
  yield takeEvery(ReportActions.getCityDay, getCityDay);
  yield takeEvery(ReportActions.getCityBranchDay, getCityBranchDay);
  yield takeEvery(ReportActions.getCityAgencyByAgency, getCityAgencyByAgency);
  yield takeEvery(ReportActions.getCityDayCount, getCityDayCount);
  yield takeEvery(ReportActions.getCityDayChart, getCityDayChart);
  yield takeEvery(ReportActions.getCityMonth, getCityMonth);
  yield takeEvery(ReportActions.getCityBranchMonth, getCityBranchMonth);
  yield takeEvery(ReportActions.getCityAgencyByPos, getCityAgencyByPos);
  yield takeEvery(ReportActions.getCityMonthCount, getCityMonthCount);
  yield takeEvery(ReportActions.getCityMonthChart, getCityMonthChart);
  yield takeEvery(ReportActions.getCityAgencyChart, getCityAgencyChart);
  yield takeEvery(ReportActions.getGames, getGames);
  yield takeEvery(ReportActions.getCityAgencyList, getCityAgencyList);
  yield takeEvery(ReportActions.getCityAgencyOfDay, getCityAgencyOfDay);
  yield takeEvery(ReportActions.getCityAgencyOfMonth, getCityAgencyOfMonth);
  yield takeLatest(ReportActions.getReportQuarterChart, getReportQuarterChart);
  yield takeLatest(
    ReportActions.getReportQuarterChartBLD,
    getReportQuarterChartBLD,
  );
  yield takeLatest(ReportActions.getReportQuarterCount, getReportQuarterCount);
  yield takeLatest(ReportActions.getReportQuarterCity, getReportQuarterCity);
  yield takeLatest(ReportActions.getReportBranchChart, getReportBranchChart);
  yield takeLatest(ReportActions.getReportBranchList, getReportBranchList);
  yield takeLatest(
    ReportActions.getReportQuarterBranch,
    getReportQuarterBranch,
  );
  yield takeLatest(
    ReportActions.getReportBranchByBranch,
    getReportBranchByBranch,
  );
  yield takeLatest(ReportActions.getReportQuarterPos, getReportQuarterPos);
  yield takeLatest(ReportActions.getAgentHomeChart, getAgentHomeChart);
}

export default ReportSaga;

function* getAgentHomeChart({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const endDate = moment().subtract(1, 'days').format('YYYY-MM-DD');
    const startDate = moment().subtract(7, 'days').format('YYYY-MM-DD');
    const rs = yield SysFetch.get(
      `report/stats/daily/days?sort_by=code&sort_order=asc&stacked_by=game&event_time=${startDate}&end_date=${endDate}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getReportQuarterPos({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/monthly/pos?sort_order=desc&${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getReportQuarterBranch({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/monthly/agency?sort_order=desc&${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getReportBranchByBranch({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/monthly/game?sort_order=desc&${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getReportBranchList({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/monthly/branch?${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getReportBranchChart({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/monthly/branch?sort_by=code&sort_order=asc&stacked_by=game&${qs.stringify(
        body,
        {
          encode: false,
        },
      )}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getReportQuarterCity({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/monthly/province?${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log({error});
  }
}

function* getReportQuarterCount({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `revplan/user/year?${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {}
}

function* getReportQuarterChartBLD({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `revplan/branch/kpis/year?${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {}
}

function* getReportQuarterChart({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `revplan/user/year?${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {}
}

function* getGames({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get('report/game');
    onSuccess && onSuccess(rs);
  } catch (error) {}
}

function* getCityAgencyOfMonth({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/monthly/district?${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getCityAgencyOfDay({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/daily/district?${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getCityAgencyList({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/monthly/agency?${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getCityAgencyChart({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/monthly/agency?sort_by=code&sort_order=asc&stacked_by=game&${qs.stringify(
        body,
        {
          encode: false,
        },
      )}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getCityDayChart({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/daily/hours?sort_by=code&sort_order=asc&${qs.stringify(
        body,
        {
          encode: false,
        },
      )}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* getCityDayCount({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/daily/days?${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {}
}

function* getCityAgencyByAgency({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/monthly/pos?sort_order=desc&${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
  }
}

function* getCityBranchDay({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/daily/province?sort_order=desc&${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* getCityDay({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/daily/branch?${qs.stringify(body, {
        encode: false,
      })}&sort_by=total_revenue`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* getCityMonthChart({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/daily/days?sort_by=code&${qs.stringify(body, {
        encode: false,
      })}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* getCityMonthCount({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get('report/stats/monthly/months', body);
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* getCityAgencyByPos({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      'report/stats/monthly/district?sort_by=total_revenue',
      body,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* getCityBranchMonth({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/monthly/province?sort_by=total_revenue&${qs.stringify(
        body,
        {
          encode: false,
        },
      )}`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* getCityMonth({payload}) {
  const {onSuccess, onFail, body} = payload;
  try {
    const rs = yield SysFetch.get(
      `report/stats/monthly/branch?${qs.stringify(body, {
        encode: false,
      })}&sort_by=total_revenue`,
    );
    onSuccess && onSuccess(rs);
  } catch (error) {
    console.log(error);
    // if (onFail) {
    //   onFail(error?.response?.data);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}
