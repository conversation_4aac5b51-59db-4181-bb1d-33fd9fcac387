import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

function AddGroupIcon(props) {
  return (
    <Svg
      width={27}
      height={20}
      viewBox="0 0 27 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <Path
        d="M16.33 12.537c1.533 1.647 2.366 3.73 2.367 5.79.005.31-.021.62-.079.923h5.85l.021-.001a1.226 1.226 0 00.46-.125.693.693 0 00.273-.237c.064-.099.133-.265.133-.554 0-.658-.346-2.154-1.456-3.476-1.079-1.285-2.906-2.44-5.952-2.44v0h-.009a9.363 9.363 0 00-1.608.12zm8.136 6.713c-.001 0-.001 0 0 0h0zm-9.788 0l.022-.001a1.225 1.225 0 00.46-.125.692.692 0 00.273-.237c.064-.099.133-.265.133-.554 0-.658-.347-2.154-1.456-3.476-1.08-1.285-2.907-2.44-5.952-2.44s-4.873 1.155-5.952 2.44C1.096 16.18.75 17.675.75 18.333c0 .289.069.455.133.554.067.102.16.179.274.237a1.226 1.226 0 00.481.126h13.04zM5.233 8.01a4.093 4.093 0 002.925 1.24 4.093 4.093 0 002.925-1.24A4.305 4.305 0 0012.303 5c0-1.132-.44-2.215-1.22-3.01A4.093 4.093 0 008.158.75a4.093 4.093 0 00-2.925 1.24A4.305 4.305 0 004.013 5c0 1.132.44 2.215 1.22 3.01zm11.182.245c.625.639 1.47.995 2.348.995.878 0 1.723-.356 2.348-.995.627-.64.981-1.51.981-2.422 0-.91-.354-1.782-.98-2.421a3.286 3.286 0 00-2.349-.995c-.878 0-1.723.356-2.348.995-.626.64-.98 1.51-.98 2.421 0 .912.354 1.782.98 2.422z"
        stroke="#fff"
        strokeWidth={1.5}
      />
    </Svg>
  );
}

export default AddGroupIcon;
