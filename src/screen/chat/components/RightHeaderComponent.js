import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import AddGroupIcon from '../icons/AddGroupIcon';
import SearchIcon from '../icons/SearchIcon';

const RightHeaderComponent = () => {
  const styles = StyleSheet.create({
    btnContainer: {
      marginRight: 15,
    },
    container: {
      flexDirection: 'row',
    },
  });

  return (
    <View style={styles.container}>
      <TouchableOpacity activeOpacity={0.8} style={styles.btnContainer}>
        <SearchIcon />
      </TouchableOpacity>
      <TouchableOpacity activeOpacity={0.8} style={styles.btnContainer}>
        <AddGroupIcon />
      </TouchableOpacity>
    </View>
  );
};

export default RightHeaderComponent;
