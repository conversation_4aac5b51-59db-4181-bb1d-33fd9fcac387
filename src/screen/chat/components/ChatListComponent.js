import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  Touchable,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import {useNavigation} from '@react-navigation/native';

const ChatListComponent = () => {
  const styles = StyleSheet.create({
    container: {
      padding: 20,
      flex: 1,
    },
  });

  return (
    <View style={styles.container}>
      <FlatList
        ListFooterComponent={<View style={{height: 50}} />}
        showsVerticalScrollIndicator={false}
        data={chatList}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item, index}) => <OneItem item={item} />}
      />
    </View>
  );
};

export default ChatListComponent;

const OneItem = ({item, index}) => {
  const {navigate} = useNavigation();

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      paddingVertical: 15,
      borderBottomColor: '#DADADA',
      borderBottomWidth: 1,
    },
    avatarView: {marginRight: 15},
    contentView: {flex: 1},
    timeView: {
      alignItems: 'flex-end',
    },
    avatar: {
      width: 50,
      height: 50,
      borderRadius: 50,
    },
    name: {lineHeight: 24, fontFamily: THEME.FrontFamily['Roboto-Bold']},
    content: {lineHeight: 24},
    time: {
      color: '#848484',
      fontSize: 12,
      lineHeight: 20,
    },
    messageCount: {
      color: 'white',
      fontSize: 9,
      lineHeight: 18,
    },
    messageCountView: {
      justifyContent: 'center',
      alignItems: 'center',
      height: 18,
      width: 18,
      backgroundColor: '#F96472',
      borderRadius: 50,
    },
  });

  const onPress = () => {
    navigate('ChatDetailScreen', item);
  };

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <View style={styles.avatarView}>
        <Image
          source={{
            uri: item.avatar,
          }}
          style={styles.avatar}
        />
      </View>
      <View style={styles.contentView}>
        <Sys.Text style={styles.name}>{item.name}</Sys.Text>
        <Sys.Text style={styles.content}>{item.content}</Sys.Text>
      </View>
      <View style={styles.timeView}>
        <Sys.Text style={styles.time}>{item.time}</Sys.Text>
        <View style={styles.messageCountView}>
          <Sys.Text style={styles.messageCount}>{item.messageCount}+</Sys.Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const chatList = [
  {
    id: 1,
    avatar:
      'https://mediacms.thethaovanhoa.vn/Upload/QDN4HPIpMrJuoPNyIvLDA/files/2021/12/Jimin-Insta5.jpg',
    name: 'My Name',
    content: 'My Content',
    time: '1 Phút',
    messageCount: 5,
  },
  {
    id: 2,
    avatar:
      'https://mediacms.thethaovanhoa.vn/Upload/QDN4HPIpMrJuoPNyIvLDA/files/2021/12/Jimin-Insta5.jpg',
    name: 'My Name',
    content: 'My Content',
    time: '1 Phút',
    messageCount: 5,
  },
  {
    id: 3,
    avatar:
      'https://mediacms.thethaovanhoa.vn/Upload/QDN4HPIpMrJuoPNyIvLDA/files/2021/12/Jimin-Insta5.jpg',
    name: 'My Name',
    content: 'My Content',
    time: '1 Phút',
    messageCount: 5,
  },
  {
    id: 4,
    avatar:
      'https://mediacms.thethaovanhoa.vn/Upload/QDN4HPIpMrJuoPNyIvLDA/files/2021/12/Jimin-Insta5.jpg',
    name: 'My Name',
    content: 'My Content',
    time: '1 Phút',
    messageCount: 5,
  },
  {
    id: 5,
    avatar:
      'https://mediacms.thethaovanhoa.vn/Upload/QDN4HPIpMrJuoPNyIvLDA/files/2021/12/Jimin-Insta5.jpg',
    name: 'My Name',
    content: 'My Content',
    time: '1 Phút',
    messageCount: 5,
  },
  {
    id: 6,
    avatar:
      'https://mediacms.thethaovanhoa.vn/Upload/QDN4HPIpMrJuoPNyIvLDA/files/2021/12/Jimin-Insta5.jpg',
    name: 'My Name',
    content: 'My Content',
    time: '1 Phút',
    messageCount: 5,
  },
  {
    id: 7,
    avatar:
      'https://mediacms.thethaovanhoa.vn/Upload/QDN4HPIpMrJuoPNyIvLDA/files/2021/12/Jimin-Insta5.jpg',
    name: 'My Name',
    content: 'My Content',
    time: '1 Phút',
    messageCount: 5,
  },
  {
    id: 8,
    avatar:
      'https://mediacms.thethaovanhoa.vn/Upload/QDN4HPIpMrJuoPNyIvLDA/files/2021/12/Jimin-Insta5.jpg',
    name: 'My Name',
    content: 'My Content',
    time: '1 Phút',
    messageCount: 5,
  },
  {
    id: 9,
    avatar:
      'https://mediacms.thethaovanhoa.vn/Upload/QDN4HPIpMrJuoPNyIvLDA/files/2021/12/Jimin-Insta5.jpg',
    name: 'My Name',
    content: 'My Content',
    time: '1 Phút',
    messageCount: 5,
  },
  {
    id: 10,
    avatar:
      'https://mediacms.thethaovanhoa.vn/Upload/QDN4HPIpMrJuoPNyIvLDA/files/2021/12/Jimin-Insta5.jpg',
    name: 'My Name',
    content: 'My Content',
    time: '1 Phút',
    messageCount: 5,
  },
];
