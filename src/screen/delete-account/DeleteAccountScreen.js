import React, {useState} from 'react';
import {View} from 'react-native';
import Sys from '../../components/Sys';
import {useDispatch} from 'react-redux';
import {AccountActions} from '../account/services/account.slice';
import {useNavigation} from '@react-navigation/native';
import Toast from 'react-native-toast-message';

const DeleteAccountScreen = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const [password, setPassword] = useState('');

  const onDelete = () => {
    dispatch(
      AccountActions.deleteAccount({
        password,
        onSuccess: () => {
          Toast.show({
            type: 'success',
            text1: 'Thông báo',
            text2: 'Đã xóa tài khoản thành công!',
          });
          navigation.navigate('LoginScreen');
        },
      }),
    );
  };

  return (
    <Sys.Container hasHeader>
      <Sys.Header title={'Xóa tài khoản'} />
      <View
        style={{
          flex: 1,
          padding: 20,
        }}>
        <View style={{marginBottom: 10}}>
          <Sys.Text>M<PERSON>t khẩu</Sys.Text>
          <Sys.Input
            value={password}
            onChangeText={e => setPassword(e)}
            secureTextEntry={true}
          />
        </View>
        <Sys.Button onPress={onDelete}>Xác nhận</Sys.Button>
      </View>
    </Sys.Container>
  );
};

export default DeleteAccountScreen;
