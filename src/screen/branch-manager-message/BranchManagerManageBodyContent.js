import React, {useEffect, useState} from 'react';
import {FlatList, TouchableOpacity, View} from 'react-native';
import {useDispatch} from 'react-redux';
import Sys from '../../components/Sys';
import {SupportActions} from '../message-support/services/support.slice';
import BranchManagerMessageList from './BranchManagerMessageList';
import {useRoute} from '@react-navigation/native';

const BranchManagerMessageBodyContent = () => {
  const {params} = useRoute();
  const dispatch = useDispatch();
  const [currentTab, setCurrentTab] = useState(params?.currentCategoryId || 0);
  const [categoryList, setCategoryList] = useState([]);
  useEffect(() => {
    dispatch(
      SupportActions.getStatus({
        onSuccess: rs => {
          setCategoryList([
            {
              id: 0,
              name: 'Tất cả',
            },
            ...Object.keys(rs).map(x => {
              return {
                id: x,
                name: rs[`${x}`],
              };
            }),
          ]);
        },
      }),
    );
  }, []);
  return (
    <Sys.Container hasHeader>
      <Sys.Header
        title={!params?.branchName ? 'Phản ánh' : params?.branchName}
        hideGoBack={!params?.branchCode}
      />
      <View
        style={{
          padding: 10,
          backgroundColor: 'white',
        }}>
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={categoryList}
          keyExtractor={(item, index) => `${index}`}
          renderItem={({item, index}) => {
            return (
              <View>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={() => {
                    setCurrentTab(item.id);
                  }}
                  style={{
                    padding: 10,
                  }}>
                  <Sys.Text
                    style={{
                      color: currentTab === item.id ? '#0062FF' : 'black',
                      fontWeight: currentTab === item.id ? '600' : '400',
                    }}>
                    {item?.name}
                  </Sys.Text>
                </TouchableOpacity>
                <View
                  style={{
                    height: 3,
                    backgroundColor:
                      currentTab === item.id ? '#0062FF' : undefined,
                    borderTopEndRadius: 50,
                    borderTopStartRadius: 50,
                  }}
                />
              </View>
            );
          }}
        />
      </View>
      <BranchManagerMessageList currentCategory={categoryList[currentTab]} />
    </Sys.Container>
  );
};

export default BranchManagerMessageBodyContent;
