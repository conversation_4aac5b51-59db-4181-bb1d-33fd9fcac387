import {View, Text, FlatList, TouchableOpacity} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {useDispatch} from 'react-redux';
import {SupportActions} from '../message-support/services/support.slice';
import * as _ from 'lodash';
import Sys from '../../components/Sys';
import RightIcon from './icons/RightIcon';
import {useNavigation, useRoute} from '@react-navigation/native';
const BranchManagerMessageList = ({currentCategory}) => {
  const {params} = useRoute();
  const dispatch = useDispatch();
  const [data, setData] = useState([]);
  useEffect(() => {
    if (currentCategory) getData();
  }, [currentCategory]);

  const getData = useCallback(() => {
    const body = {};

    if (params?.branchCode) {
      body.branch = [params?.branchCode];
    }
    dispatch(
      SupportActions.getBMListByCategory({
        id: currentCategory?.id,
        body,
        onSuccess: rs => {
          setData(rs.data);
        },
      }),
    );
  }, [currentCategory]);

  return (
    <View
      style={{
        margin: 20,
        borderRadius: 10,
        overflow: 'hidden',
      }}>
      <FlatList
        data={data}
        keyExtractor={(item, index) => `${index}`}
        renderItem={props => {
          return (
            <OneItem
              currentCategory={currentCategory}
              item={props.item}
              underline={props.index !== data.length - 1}
            />
          );
        }}
      />
    </View>
  );
};

export default BranchManagerMessageList;

const OneItem = ({item, underline = true, currentCategory}) => {
  const {navigate} = useNavigation();
  const {params} = useRoute();

  return (
    <TouchableOpacity
      onPress={() => {
        if (!params?.branchCode) {
          navigate('BranchManagerMessageSubScreen', {
            branchCode: item?.branchCode,
            branchName: item?.locationName,
            currentCategoryId: currentCategory?.id,
          });
        } else {
          navigate('MessageSupportScreen', {
            branchCode: item?.branchCode,
            currentCategoryId: currentCategory?.id,
          });
        }
      }}
      style={{
        backgroundColor: 'white',
        flexDirection: 'row',
        alignItems: 'center',
      }}>
      <View
        style={{
          marginHorizontal: 15,
          width: 34,
          height: 34,
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#001451',
          borderRadius: 100,
        }}>
        <Sys.Text
          style={{
            color: 'white',
            fontWeight: '600',
          }}>
          {item?.count}
        </Sys.Text>
      </View>
      <View
        style={{
          borderBottomColor: '#c6c6c650',
          borderBottomWidth: underline ? 1 : 0,
          justifyContent: 'center',
          flex: 1,
          paddingRight: 15,
          height: 54,
        }}>
        <Sys.Text>{item?.locationName}</Sys.Text>
        <View
          style={{
            position: 'absolute',
            right: 15,
          }}>
          <RightIcon />
        </View>
      </View>
    </TouchableOpacity>
  );
};
