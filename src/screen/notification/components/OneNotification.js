import React from 'react';
import {Linking, StyleSheet, TouchableOpacity, View} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import ItemIcon from '../icons/ItemIcon';
import {formatDate} from '../../../services/util';
import Color from '../../../components/theme/Color';

const OneNotification = ({item}) => {
  const {navigate} = useNavigation();
  const {
    title,
    body,
    data: {screen, actionUrl, actionText, params, type} = {},
    created_at,
  } = item.data || {};
  const onPress = () => {
    if (actionUrl) {
      Linking.openURL(actionUrl).catch(err => {
        console.error('Failed to open URL:', err);
      });
    } else if (screen && screen !== 'NotificationList') {
      navigate(screen, params);
    } else {
      // navigate('NotificationDetailScreen', {id: item.id});
    }
  };
  const isDetail = (screen && screen !== 'NotificationList') || actionUrl;
  const color =
    type === 'default'
      ? Color.gray
      : type === 'info'
      ? Color.blue
      : type === 'warning'
      ? Color.orange
      : Color.red;
  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <View style={[styles.iconContainer]}>
          <ItemIcon color={color} />
        </View>
        <View style={styles.contentContainer}>
          <View style={styles.topContainer}>
            <Sys.Text style={styles.noti}>Thông báo ●</Sys.Text>
            <Sys.Text style={styles.date}>
              {formatDate(created_at, true)}
            </Sys.Text>
          </View>
          <Sys.Text style={styles.title}>{item.data?.title}</Sys.Text>
          <Sys.Text style={styles.content}>{item.data?.body}</Sys.Text>
          {isDetail && (
            <View>
              <TouchableOpacity activeOpacity={0.9} onPress={onPress}>
                <Sys.Text style={styles.link}>
                  {actionText || 'Chi tiết'}
                </Sys.Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
      <View style={styles.bottomLine} />
    </View>
  );
};

export default OneNotification;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    backgroundColor: 'white',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 15,
  },
  contentContainer: {
    flex: 1,
  },
  topContainer: {
    flexDirection: 'row',
  },
  noti: {
    fontSize: 12,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginRight: 5,
    lineHeight: 20,
  },
  date: {
    fontSize: 12,
    lineHeight: 20,
  },
  title: {
    color: '#001451',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    lineHeight: 24,
  },
  content: {
    lineHeight: 24,
  },
  link: {
    color: THEME.Color.blue,
    textDecorationLine: 'underline',
  },
  bottomLine: {
    backgroundColor: '#e1e1e1',
    height: 1,
    width: '100%',
    marginTop: 15,
    marginBottom: 15,
  },
  deleteButton: {
    padding: 10,
  },
  deleteButtonText: {
    fontSize: 12,
    color: 'red',
  },
});
