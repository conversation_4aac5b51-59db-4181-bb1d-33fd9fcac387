import React from 'react';
import {StyleSheet, View} from 'react-native';
import Input from '../../../components/bases/Input';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import SearchIcon from '../icons/SearchIcon';
const SearchComponent = () => {
  const styles = StyleSheet.create({
    container: {
      padding: 20,
    },
    searchIconContainer: {
      position: 'absolute',
      right: 10,
      top: 7,
    },
  });

  return (
    <View style={styles.container}>
      <View>
        <Input placeholder={'Tìm kiếm'} />
        <View style={styles.searchIconContainer}>
          <SearchIcon />
        </View>
      </View>
    </View>
  );
};

export default SearchComponent;
