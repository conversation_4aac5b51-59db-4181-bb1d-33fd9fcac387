import React, {useEffect, useState} from 'react';
import {ScrollView, StyleSheet, useWindowDimensions, View} from 'react-native';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import ItemIcon from '../../notification/icons/ItemIcon';
import SysFetch from '../../../services/fetch';
import moment from 'moment';
import NotificationDetailSkeleton from '../../notification-detail/NotificationDetailSkeleton';
import RenderHtml, {defaultSystemFonts} from 'react-native-render-html';

const NotificationNewDetail = ({route}) => {
  const {id} = route.params;
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const {width} = useWindowDimensions();
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await SysFetch.get(`announcements/items/${id}`);
        setData(response.data);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [id]);

  if (loading) {
    return <NotificationDetailSkeleton />;
  }
  const source = {html: data.body || '<p></p>'};
  return (
    <Sys.Container hasHeader>
      <Sys.Header title={'Chi Tiết thông báo'} />
      <ScrollView>
        <View style={styles.container}>
          <View style={styles.topContainer}>
            <View style={styles.iconContainer}>
              <ItemIcon />
            </View>
            <View style={styles.contentContainer}>
              <View style={styles.headerContainer}>
                <Sys.Text style={styles.noti}>{data.title}</Sys.Text>
              </View>
              <Sys.Text style={styles.date}>
                {moment(data?.published_at || data?.schedule_at).format(
                  'DD/MM/YYYY',
                )}
              </Sys.Text>
              <RenderHtml
                style={{
                  flex: 1,
                }}
                source={source}
                tagsStyles={{
                  img: {
                    width: width - 60,
                    height: 'auto',
                    borderRadius: 5,
                    marginVertical: 10,
                  },
                }}
                systemFonts={[...defaultSystemFonts, 'Roboto-Bold']}
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </Sys.Container>
  );
};
const customStyles = {
  p: {
    textAlign: 'justify',
  },
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    width: '100%',
  },
  img: {
    maxWidth: '100%',
    height: 'auto',
    borderRadius: 5, // Adding rounded corners to the images
    marginVertical: 10, // Ensuring there's space above and below the image
  },
  p: {
    textAlign: 'justify',
  },
  title: {
    color: '#001451',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    lineHeight: 28,
    fontSize: 18,
  },
  content: {
    lineHeight: 24,
  },
  iconContainer: {
    marginRight: 15,
  },
  noti: {
    lineHeight: 20,
    fontSize: 12,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginRight: 5,
  },
  date: {
    color: '#848484',
    lineHeight: 20,
    fontSize: 12,
  },
  topContainer: {
    flexDirection: 'row',
  },
  contentContainer: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
  },
});

export default NotificationNewDetail;
