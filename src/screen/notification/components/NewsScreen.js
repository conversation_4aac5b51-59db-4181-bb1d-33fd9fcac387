import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import SysFetch from '../../../services/fetch';
import ContentLoader, {Rect} from 'react-content-loader/native';
import moment from 'moment';
import Divider from '../../device-list/components/Divider';
import Color from '../../../components/theme/Color';
import {useNavigation} from '@react-navigation/native';
import Sys from '../../../components/Sys';

const NewsScreen = () => {
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [newsItems, setNewsItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);
  const navigation = useNavigation();

  const fetchCategories = async () => {
    try {
      const response = await SysFetch.get('/categories/announcements');
      setCategories(response);
      setSelectedCategory(response[0].id);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchNews = async (categoryId, page) => {
    try {
      const response = await SysFetch.get(`/announcements/${categoryId}`, {
        page: page,
        pageSize: 10,
      });
      setNewsItems(prevNewsItems => [...prevNewsItems, ...response.data]);
      setCurrentPage(response.meta.current_page);
      setLastPage(response.meta.last_page);
    } catch (error) {
      console.error('Error fetching news:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    if (selectedCategory) {
      setNewsItems([]);
      fetchNews(selectedCategory, 1);
    }
  }, [selectedCategory]);

  useEffect(() => {
    if (currentPage > 1) {
      fetchNews(selectedCategory, currentPage);
    }
  }, [currentPage]);

  const handleLoadMore = () => {
    if (currentPage < lastPage && !loading) {
      setCurrentPage(prevPage => prevPage + 1);
    }
  };

  const renderFooter = () => {
    if (!loading) {
      return null;
    }
    return <ActivityIndicator style={{color: '#000'}} />;
  };

  const renderSkeleton = () => (
    <ContentLoader
      speed={1}
      width={'100%'}
      height={80}
      backgroundColor="#f3f3f3"
      foregroundColor="#ecebeb">
      <Rect x="0" y="10" rx="5" ry="5" width="100%" height="60" />
    </ContentLoader>
  );

  const renderCategory = ({item}) => (
    <TouchableOpacity
      onPress={() => setSelectedCategory(item.id)}
      style={styles.categoryItem}>
      <Sys.Text
        style={[
          styles.categoryText,
          selectedCategory === item.id && styles.categoryTextSelected,
        ]}>
        {item.name}
      </Sys.Text>
    </TouchableOpacity>
  );

  const renderNewsItem = ({item}) => (
    <>
      <TouchableOpacity
        onPress={() =>
          navigation.navigate('NotificationNewDetail', {id: item.id})
        }
        style={styles.newsItem}>
        {item.image && (
          <Image width={'100%'} height={180} source={{uri: item.image}} />
        )}

        <Sys.Text style={styles.newsItemTitle}>{item.title}</Sys.Text>
        <Sys.Text style={styles.newsItemDate}>
          {moment(item?.schedule_at).format('DD/MM/YYYY')}
        </Sys.Text>
      </TouchableOpacity>
      <Divider />
    </>
  );

  return (
    <View style={styles.container}>
      {/*<SearchComponent />*/}
      <View>
        <FlatList
          data={categories}
          renderItem={renderCategory}
          keyExtractor={item => item.id.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoryList}
        />
      </View>
      <View>
        <FlatList
          data={loading ? Array.from({length: 10}) : newsItems}
          renderItem={({item, index}) =>
            loading ? renderSkeleton() : renderNewsItem({item})
          }
          keyExtractor={(item, index) =>
            loading ? `skeleton-${index}` : item.id.toString()
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{paddingBottom: 20}}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    padding: 10,
  },
  categoryList: {
    padding: 10,
    backgroundColor: '#f8f8f8',
  },
  categoryItem: {
    marginRight: 15,
  },
  categoryText: {
    fontSize: 16,
    color: '#888',
  },
  categoryTextSelected: {
    fontWeight: 'bold',
    color: Color.primary,
  },
  newsItem: {
    margin: 10,
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 5,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 5,
  },
  newsItemTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 10,
  },
  newsItemDate: {
    fontSize: 16,
    color: '#888',
  },
  skeletonItem: {
    width: '100%',
    height: 80,
    marginVertical: 10,
    borderRadius: 5,
  },
});

export default NewsScreen;
