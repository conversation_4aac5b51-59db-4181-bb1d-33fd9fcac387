import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {SwipeListView} from 'react-native-swipe-list-view';
import OneNotification from './OneNotification';
import SysFetch from '../../../services/fetch';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {NotificationActions} from '../services/notification.slice';
import {useDispatch} from 'react-redux';
import DeleteIcon from '../../visit-plan/icons/DeleteIcon';
import Sys from '../../../components/Sys';
import Toast from 'react-native-toast-message';

const NotificationList = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);
  const dispatch = useDispatch();

  const fetchNotifications = async page => {
    try {
      const response = await SysFetch.get('me/notifications', {
        page: page,
        pageSize: 10,
      });
      setNotifications(prevNotifications => [
        ...prevNotifications,
        ...response.data,
      ]);
      setCurrentPage(response.meta.current_page);
      setLastPage(response.meta.last_page);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const markNotificationsAsRead = async () => {
    try {
      await SysFetch.get('/me/notifications/mark-as-read');
      dispatch(NotificationActions.resetNotification());
    } catch (error) {
      console.error('Error marking notifications as read:', error);
    }
  };

  useEffect(() => {
    fetchNotifications(currentPage);
    markNotificationsAsRead();
  }, [currentPage]);

  const handleLoadMore = () => {
    if (currentPage < lastPage && !loading) {
      setCurrentPage(prevPage => prevPage + 1);
    }
  };

  const handleDeleteNotification = notificationId => {
    setNotifications(currentNotifications =>
      currentNotifications.filter(
        notification => notification.id !== notificationId,
      ),
    );
  };

  const deleteNotification = async id => {
    try {
      const response = await SysFetch.post('me/notifications/delete/multiple', {
        ids: [id],
      });

      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'Thông báo',
          text2: 'Xóa thành công',
        });

        handleDeleteNotification(id);
      } else {
        throw new Error('Failed to delete notification');
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
      Toast.show({
        type: 'error',
        text1: 'Thông báo',
        text2: 'Xóa không thành công',
      });
    }
  };

  const renderFooter = () => {
    if (!loading) {
      return null;
    }
    return <ActivityIndicator style={{color: '#000'}} />;
  };

  const renderSkeleton = () => (
    <ContentLoader
      speed={1}
      width={'100%'}
      height={80}
      backgroundColor="#f3f3f3"
      foregroundColor="#ecebeb">
      <Rect x="0" y="10" rx="5" ry="5" width="100%" height="60" />
    </ContentLoader>
  );
  const renderHiddenItem = (data, rowMap) => (
    <View style={styles.hiddenItem}>
      <TouchableOpacity
        onPress={() => {
          rowMap[data.index].closeRow();
          deleteNotification(data.item.id);
        }}
        style={styles.deleteButton}>
        <DeleteIcon />
        <Sys.Text style={styles.deleteButtonText}>Xóa </Sys.Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <SwipeListView
        data={notifications}
        renderItem={({item, index}) => (
          <OneNotification key={index} item={item} />
        )}
        keyExtractor={(item, index) => index.toString()}
        renderHiddenItem={renderHiddenItem}
        contentContainerStyle={styles.contentContainer}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        showsVerticalScrollIndicator={false}
        rightOpenValue={-75}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    padding: 10,
  },
  hiddenItem: {
    justifyContent: 'center',
    alignItems: 'flex-end',
    flex: 1,
    backgroundColor: 'white',
  },
  deleteButton: {
    width: 75,
    padding: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteButtonText: {
    fontSize: 10,
    color: '#848484',
    textAlign: 'center',
    marginTop: 2,
  },
  contentContainer: {paddingBottom: 50},
});

export default NotificationList;
