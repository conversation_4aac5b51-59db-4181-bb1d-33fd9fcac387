import {call, put, takeLatest} from 'redux-saga/effects';
import {NotificationActions} from './notification.slice';
import SysFetch from '../../../services/fetch';

function* NotificationSaga() {
  yield takeLatest(NotificationActions.getNotification, getNotification);
  yield takeLatest(NotificationActions.getAnnouncement, getAnnouncement);
}

export default NotificationSaga;

function* getNotification({payload = {}}) {
  const {params = {page: 1, pageSize: 1}} = payload;
  try {
    const response = yield call(SysFetch.get, 'me/notifications', params);
    const data = {
      data: response?.data || [],
      total: response?.meta?.total || 0,
      unRead: response?.unread_count || 0,
    };
    yield put(NotificationActions.setNotification(data));
  } catch (error) {
    console.log(error);
  }
}

function* getAnnouncement({payload}) {
  const {params = {page: 1, pageSize: 1}} = payload;
  try {
    const response = yield call(SysFetch.get, 'announcements', params);
    const rs = {
      data: response?.data || [],
      total: response?.meta?.total || 0,
    };
    yield put(NotificationActions.setAnnouncement(rs));
  } catch (error) {
    console.log(error);
  }
}
