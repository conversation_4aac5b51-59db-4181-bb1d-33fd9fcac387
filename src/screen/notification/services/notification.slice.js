import {createSlice} from '@reduxjs/toolkit';
import _ from 'lodash';

const initialState = {
  notifications: {
    data: [],
    total: 0,
    unRead: 0,
  },
  announcements: {
    data: [],
    total: 0,
    unRead: 0,
  },
};

const NotificationSlice = createSlice({
  name: 'notify',
  initialState,
  reducers: {
    getNotification: () => {},
    getAnnouncement: () => {},
    setNotification: (state, {payload}) => {
      _.forEach(payload, (value, key) => {
        state.notifications[key] = value;
      });
    },
    resetNotification: (state, {payload}) => {
      state.notifications.unRead = 0;
    },
    setAnnouncement: (state, {payload}) => {
      _.forEach(payload, (value, key) => {
        state.announcements[key] = value;
      });
    },
  },
});

const NotificationReducers = NotificationSlice.reducer;

export default NotificationReducers;
export const NotificationActions = NotificationSlice.actions;
