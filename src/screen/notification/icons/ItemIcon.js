import * as React from 'react';
import Svg, {<PERSON>, <PERSON>lip<PERSON><PERSON>, Defs, G, Path} from 'react-native-svg';

const ItemIcon = ({color, ...props}) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={21}
    height={21}
    fill="none"
    {...props}>
    <Circle cx={10.5} cy={10.5} r={10.5} fill={color} />
    <G
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#a)">
      <Path d="M10.5 15.75a5.25 5.25 0 1 0 0-10.5 5.25 5.25 0 0 0 0 10.5ZM10.5 8.4v2.1" />
      <Path strokeWidth={1.5} d="M10.5 12.6h.005" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M4.2 4.2h12.6v12.6H4.2z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default ItemIcon;
