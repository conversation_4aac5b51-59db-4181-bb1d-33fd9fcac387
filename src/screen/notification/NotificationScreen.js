import React, {useEffect} from 'react';
import Sys from '../../components/Sys';
import NewsScreen from './components/NewsScreen';
import NotificationList from './components/NotificationList';
import Color from '../../components/theme/Color';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {useRoute} from '@react-navigation/native';
import NotificationIcon from '../account/icons/NotificationIcon';
import NewIcon from '../account/icons/NewIcon';
import {useDispatch, useSelector} from 'react-redux';
import {NotificationActions} from './services/notification.slice';

const Tab = createBottomTabNavigator();

const NotificationScreen = () => {
  const {params} = useRoute();
  const {
    notifications: {unRead},
  } = useSelector(state => state.notify);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(NotificationActions.getNotification());
  }, []);

  return (
    <Sys.Container hasHeader hasFooter footerColor="white">
      <Sys.Header title={'Thông báo và Bản tin'} />
      <Tab.Navigator
        initialRouteName={params?.initialTab || 'Bản tin'}
        screenOptions={({route}) => ({
          tabBarIcon: ({color, size}) => {
            if (route.name === 'Bản tin') {
              return <NewIcon color={color} />;
            } else if (route.name === 'Thông báo') {
              return <NotificationIcon color={color} />;
            }
          },
          headerShown: false,
        })}
        tabBarOptions={{
          activeTintColor: Color.primary,
          inactiveTintColor: 'gray',
        }}>
        <Tab.Screen
          name="Bản tin"
          component={NewsScreen}
          options={{
            tabBarBadge: null,
          }}
        />
        <Tab.Screen
          name="Thông báo"
          component={NotificationList}
          options={{
            tabBarBadge: unRead > 0 ? unRead : null,
          }}
        />
      </Tab.Navigator>
    </Sys.Container>
  );
};

export default NotificationScreen;
