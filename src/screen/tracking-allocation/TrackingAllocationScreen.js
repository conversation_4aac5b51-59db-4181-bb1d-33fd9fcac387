import React from 'react';
import Sys from '../../components/Sys';
import BoxIcon from './icons/BoxIcon';
import StoreIcon from './icons/StoreIcon';
import {FlatList, StyleSheet, TouchableOpacity} from 'react-native';
import {View} from 'react-native-animatable';
import MaterialCommunityIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useNavigation} from '@react-navigation/native';
const list = [
  {
    name: 'Vật phẩm MKT đã cấp cho đại lý',
    screen: 'ItemsForStoreScreen',
    icon: <BoxIcon />,
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON> bán đã nhận vật phẩm MKT',
    screen: 'StoreHasItemsScreen',
    icon: <StoreIcon />,
  },
];

const TrackingAllocationScreen = () => {
  return (
    <Sys.Container hasHeader>
      <Sys.Header title={'Theo dõi cấp phát'} />
      <View
        style={{
          margin: 10,
        }}>
        <FlatList
          data={list}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({item, index}) => <OneItem item={item} />}
        />
      </View>
    </Sys.Container>
  );
};

export default TrackingAllocationScreen;

const OneItem = ({item}) => {
  const {navigate} = useNavigation();
  const styles = StyleSheet.create({
    container: {
      margin: 10,
      paddingVertical: 20,
      paddingRight: 12,
      paddingLeft: 20,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#D1D1D1',
      backgroundColor: 'white',
      flexDirection: 'row',
      alignItems: 'center',
    },
    icon: {
      marginRight: 20,
    },
  });
  return (
    <TouchableOpacity
      onPress={() => navigate(item.screen)}
      style={styles.container}>
      <View style={styles.icon}>{item.icon}</View>
      <View
        style={{
          flex: 1,
        }}>
        <Sys.Text>{item.name}</Sys.Text>
      </View>
      <View>
        <MaterialCommunityIcon size={24} color="black" name="chevron-right" />
      </View>
    </TouchableOpacity>
  );
};
