import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const StoreIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={26}
    height={26}
    fill="none"
    {...props}>
    <Path
      stroke="#F5000D"
      strokeWidth={1.8}
      d="M3.25 10.833v9.75a2.167 2.167 0 0 0 2.167 2.167h15.166a2.167 2.167 0 0 0 2.167-2.167v-9.75"
    />
    <Path
      stroke="#F5000D"
      strokeMiterlimit={16}
      strokeWidth={1.8}
      d="M16.07 22.75v-6.5a2.167 2.167 0 0 0-2.168-2.167h-2.166a2.167 2.167 0 0 0-2.167 2.167v6.5"
    />
    <Path
      stroke="#F5000D"
      strokeWidth={1.8}
      d="m23.636 10.144-1.835-6.423a.65.65 0 0 0-.625-.471h-4.384l.514 6.18a.626.626 0 0 0 .302.487c.422.252 1.247.718 1.892.916 1.1.34 2.708.217 3.625.104a.617.617 0 0 0 .511-.793Z"
    />
    <Path
      stroke="#F5000D"
      strokeWidth={1.8}
      d="M15.167 10.833c.615-.19 1.395-.622 1.83-.88a.627.627 0 0 0 .304-.594l-.51-6.109H9.209L8.7 9.359a.626.626 0 0 0 .304.596c.435.256 1.215.689 1.83.878 1.618.499 2.716.499 4.334 0Z"
    />
    <Path
      stroke="#F5000D"
      strokeWidth={1.8}
      d="m4.2 3.721-1.836 6.424a.618.618 0 0 0 .511.791c.916.114 2.524.235 3.625-.103.645-.198 1.471-.664 1.893-.915a.626.626 0 0 0 .3-.489l.515-6.179H4.824a.65.65 0 0 0-.625.471Z"
    />
  </Svg>
);
export default StoreIcon;
