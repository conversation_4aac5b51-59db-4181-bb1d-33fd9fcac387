import * as React from 'react';
import Svg, {G, <PERSON>, Defs, ClipPath} from 'react-native-svg';
const BoxIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={28}
    height={28}
    fill="none"
    {...props}>
    <G clipPath="url(#a)">
      <Path
        fill="#F5000D"
        d="M23.333 3.5a2.334 2.334 0 0 1 2.328 2.158l.006.175V10.5H24.5v11.667a2.333 2.333 0 0 1-2.158 2.327l-.175.006H5.833a2.333 2.333 0 0 1-2.327-2.158l-.006-.175V10.5H2.333V5.833a2.333 2.333 0 0 1 2.159-2.327l.175-.006h18.666Zm-1.166 7H5.833v11.667h16.334V10.5Zm-5.834 4.667a1.167 1.167 0 1 1 0 2.333h-4.666a1.167 1.167 0 1 1 0-2.333h4.666Zm7-9.334H4.667v2.334h18.666V5.833Z"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h28v28H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default BoxIcon;
