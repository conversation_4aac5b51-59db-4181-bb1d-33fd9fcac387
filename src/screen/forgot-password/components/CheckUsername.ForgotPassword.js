import {View, Text, StyleSheet, useAnimatedValue} from 'react-native';
import React, {useCallback, useState} from 'react';
import Sys from '../../../components/Sys';
import {isEmpty} from 'lodash';
import {useNavigation} from '@react-navigation/native';
const CheckUsernameForgotPassword = () => {
  const [username, setUsername] = useState('');
  const {navigate} = useNavigation();

  const onChangeUsername = e => {
    setUsername(e);
  };

  const styles = StyleSheet.create({
    container: {
      padding: 20,
    },
    btnArea: {
      marginTop: 40,
    },
  });

  const canNext = useCallback(() => {
    return isEmpty(username);
  }, [username]);

  const onNext = () => {
    navigate('InputUserInfoScreen', {
      username,
    });
  };

  return (
    <View style={styles.container}>
      <Sys.Input
        label={'Tên đăng nhập'}
        value={username}
        onChangeText={onChangeUsername}
        placeholder={'Nhập tên đăng nhập'}
      />
      <View style={styles.btnArea}>
        <Sys.Button disabled={canNext()} onPress={onNext}>
          Tiếp tục
        </Sys.Button>
      </View>
    </View>
  );
};

export default CheckUsernameForgotPassword;
