import {View, Text, StyleSheet} from 'react-native';
import React, {useState} from 'react';
import {useNavigation, useRoute} from '@react-navigation/native';
import Sys from '../../../components/Sys';
import {isEmpty} from 'lodash';

const ConformInformationForgotPassword = () => {
  const {navigate} = useNavigation();
  const {
    params: {username},
  } = useRoute();

  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');

  const onChangeEmail = e => {
    setEmail(e);
  };

  const onChangePhoneNumber = e => {
    setPhoneNumber(e);
  };

  const onNext = () => {
    navigate('CreatePasswordScreen');
  };

  return (
    <View style={styles.container}>
      <View style={styles.oneField}>
        <Sys.Input label={'Tên đăng nhập'} value={username} editable={false} />
      </View>
      <View style={styles.oneField}>
        <Sys.Input
          placeholder={'Nhập Email'}
          label={'Email'}
          onChangeText={onChangeEmail}
          value={email}
        />
      </View>
      <View style={styles.oneField}>
        <Sys.Input
          placeholder={'Nhập số điện thoại'}
          label={'Số điện thoại'}
          onChangeText={onChangePhoneNumber}
          value={phoneNumber}
        />
      </View>
      <View style={styles.btnArea}>
        <Sys.Button
          onPress={onNext}
          disabled={isEmpty(email) || isEmpty(phoneNumber)}>
          Tiếp tục
        </Sys.Button>
      </View>
    </View>
  );
};

export default ConformInformationForgotPassword;

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  oneField: {
    marginBottom: 15,
  },
  btnArea: {
    marginTop: 25,
  },
});
