import {View, Text, StyleSheet} from 'react-native';
import React, {useState} from 'react';
import {useNavigation, useRoute} from '@react-navigation/native';
import Sys from '../../../components/Sys';
import {isEmpty} from 'lodash';
import THEME from '../../../components/theme/theme';

const CreatePasswordForgotPassword = () => {
  const {navigate} = useNavigation();

  const [password, setPassword] = useState('');
  const [rePassword, setRePassword] = useState('');

  const onChangePassword = e => {
    setPassword(e);
  };

  const onChangeRePassword = e => {
    setRePassword(e);
  };

  const onNext = () => {
    navigate('CreatePasswordScreen');
  };

  return (
    <View style={styles.container}>
      <View style={styles.oneField}>
        <Sys.Input
          placeholder={'Nhập mật khẩu mới'}
          label={'Mật khẩu mới'}
          onChangeText={onChangePassword}
          secureTextEntry
          value={password}
        />
      </View>
      <View style={styles.oneField}>
        <Sys.Input
          placeholder={'Nhập lại mật khẩu mới'}
          label={'Nhập lại mật khẩu mới'}
          secureTextEntry
          onChangeText={onChangeRePassword}
          value={rePassword}
        />
      </View>
      <View style={styles.btnArea}>
        <Sys.Button
          onPress={onNext}
          disabled={isEmpty(password) || isEmpty(rePassword)}>
          Tiếp tục
        </Sys.Button>
      </View>
      <View style={styles.noteArea}>
        <Sys.Text style={styles.titleNote}>
          Mật khẩu phải thỏa mãn các điều kiện sau:
        </Sys.Text>
        <Sys.Text style={styles.contentNote}>
          Có độ dài từ 8 đến 20 kí tự.
        </Sys.Text>
        <Sys.Text style={styles.contentNote}>
          Chứa ít nhất 01 ký tự số, 01 ký tự chữ Hoa, 01 ký tự đặc biệt
        </Sys.Text>
        <Sys.Text style={styles.contentNote}>
          Ví dụ: V@123456; 123456@V; 123456V@
        </Sys.Text>
      </View>
    </View>
  );
};

export default CreatePasswordForgotPassword;

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  oneField: {
    marginBottom: 15,
  },
  btnArea: {
    marginTop: 25,
  },
  noteArea: {
    marginTop: 50,
  },
  titleNote: {
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    color: '#848484',
    lineHeight: 24,
  },
  contentNote: {
    marginLeft: 12,
    lineHeight: 24,
    color: '#848484',
  },
});
