import {View, Text, FlatList, StyleSheet, TouchableOpacity} from 'react-native';
import React from 'react';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import {useNavigation} from '@react-navigation/native';

const data = [
  {
    code: 'Đại lý số 1',
    name: 'Diem ban hang So 1',
  },
  {
    code: 'Đại lý số 1',
    name: 'Diem ban hang So 1',
  },
  {
    code: 'Đại lý số 1',
    name: 'Diem ban hang So 1',
  },
  {
    code: 'Đại lý số 1',
    name: 'Diem ban hang So 1',
  },
  {
    code: 'Đại lý số 1',
    name: 'Diem ban hang So 1',
  },
  {
    code: 'Đại lý số 1',
    name: 'Diem ban hang So 1',
  },
  {
    code: 'Đại lý số 1',
    name: 'Diem ban hang So 1',
  },
];

const ItemsForStoreList = () => {
  return (
    <View>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={data}
        keyExtractor={(item, index) => index.toString}
        renderItem={({item, index}) => <OneItem item={item} />}
      />
    </View>
  );
};

export default ItemsForStoreList;

const OneItem = ({item, index}) => {
  const {navigate} = useNavigation();

  const styles = StyleSheet.create({
    container: {
      paddingBottom: 5,
      paddingTop: 15,
      backgroundColor: 'white',
      borderBottomColor: '#E1E1E1',
      borderBottomWidth: 1,
      marginHorizontal: 20,
    },
    content: {
      paddingBottom: 15,
      flex: 1,
    },
    code: {
      color: '#001451',
      lineHeight: 24,
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
    },
    name: {
      color: '#001451',
      lineHeight: 24,
    },
    title: {
      color: '#848484',
      lineHeight: 24,
    },
    indexContainer: {
      marginRight: 15,
      borderRadius: 5,
      backgroundColor: '#001451',
      height: 20,
      width: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 5,
    },
    index: {
      color: 'white',
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
    },
    contentData: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      flex: 1,
    },
  });

  const goDetail = () => {
    navigate('SaleLocationDetailScreen', {id: 'chai'});
  };

  return (
    <View style={styles.container}>
      <View
        style={{
          flexDirection: 'row',
        }}>
        <View style={styles.indexContainer}>
          <Sys.Text style={styles.index}>1</Sys.Text>
        </View>
        <View
          style={{
            flex: 1,
          }}>
          <TouchableOpacity style={styles.content} onPress={goDetail}>
            <Sys.Text style={styles.code}>{item.code}</Sys.Text>
            <Sys.Text style={styles.name}>{item.name}</Sys.Text>
            <View style={styles.contentData}>
              <View>
                <Sys.Text style={styles.title}>Vật phẩm MKT đã nhận: </Sys.Text>
              </View>
              <View>
                <Sys.Text>10</Sys.Text>
              </View>
            </View>
            <View style={styles.contentData}>
              <View>
                <Sys.Text style={styles.title}>Vật phẩm MKT tồn:</Sys.Text>
              </View>
              <View>
                <Sys.Text>5</Sys.Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};
