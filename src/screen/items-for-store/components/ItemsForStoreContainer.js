import React from 'react';
import {StyleSheet, View} from 'react-native';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import FilterIcon from '../icons/FilterIcon';
import MapIcon from '../icons/MapIcon';
import ItemsForStoreList from './ItemsForStoreList';

const ItemsForStoreContainer = () => {
  const styles = StyleSheet.create({
    header: {
      paddingHorizontal: 20,
      paddingVertical: 15,
      flexDirection: 'row',
    },
    headerText: {
      flex: 1,
      lineHeight: 28,
      fontSize: 18,
      fontFamily: THEME.FrontFamily['Roboto-Bold'],
    },
    content: {
      flex: 1,
      backgroundColor: 'white',
    },
    icon: {
      width: 32,
      height: 32,
      backgroundColor: 'white',
      borderRadius: 7,
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: 10,
    },
    container: {
      flex: 1,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Sys.Text style={styles.headerText}>150 điểm bán</Sys.Text>
        <View style={styles.icon}>
          <FilterIcon />
        </View>
      </View>
      <View style={styles.content}>
        <ItemsForStoreList />
      </View>
    </View>
  );
};

export default ItemsForStoreContainer;
