import React, {useState} from 'react';
import {ScrollView, StyleSheet, TouchableOpacity, View, Dimensions} from 'react-native';
import Sys from '../../components/Sys';
import SysFetch from '../../services/fetch';
import Toast from 'react-native-toast-message';
import {AppActions} from '../../app.slice';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {Front} from '../../components/theme/FontFamily';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import THEME from '../../components/theme/theme';

const {width} = Dimensions.get('window');

const MarketingItemDetailsScreen = ({route}) => {
  const dispatch = useDispatch();
  const {navigate, ...navigation} = useNavigation();

  const {item, statusDetails} = route.params;
  const [itemData, setItemData] = useState(
    item.items.map(subItem => ({
      ...subItem,
      actual_qty: subItem.actual_qty || 0,
    })),
  );
  const {view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);

  const handleQuantityChange = (text, index) => {
    const newData = [...itemData];
    newData[index].actual_qty = text;
    setItemData(newData);
  };

  const handleSubmit = async () => {
    dispatch(AppActions.setLoading(true));

    const payload = itemData.map(subItem => ({
      id: subItem.id,
      actual_qty: parseInt(subItem.actual_qty, 10),
    }));
    try {
      await SysFetch.post(`marketing/${item.id}/update-qty`, {
        items: payload,
        pos_id: item.pos_id,
      });
      dispatch(AppActions.setLoading(false));
      Toast.show({
        type: 'success',
        text1: 'Thông báo',
        text2: 'Xác nhận thành công',
      });
      navigation.goBack();
    } catch (error) {
      dispatch(AppActions.setLoading(false));
      console.error('API Error:', error.message);
      Toast.show({
        type: 'error',
        text1: 'Thông báo',
        text2: 'Có lỗi vui lòng thử lại',
      });
    }
  };

  return (
    <Sys.Container hasHeader backgroundColor={THEME.Color.backgroundColor}>
      <Sys.Header
        title={'Danh sách VP Marketing'}
        color={THEME.Color.second}
      />

      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}>

        {/* Header Card */}
        <View style={styles.headerCard}>
          <View style={styles.cardHeader}>
            <MaterialCommunityIcons
              name="package-variant"
              size={24}
              color={THEME.Color.primary}
            />
            <Sys.Text style={styles.cardTitle}>Thông tin chung</Sys.Text>
          </View>

          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <Sys.Text style={styles.infoLabel}>Tên sản phẩm</Sys.Text>
              <Sys.Text style={styles.infoValue}>{item?.name}</Sys.Text>
            </View>
          </View>

          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <Sys.Text style={styles.infoLabel}>Trạng thái</Sys.Text>
              <View style={[styles.statusBadge, {backgroundColor: statusDetails.color}]}>
                <Sys.Text style={styles.statusText}>{item.status_label}</Sys.Text>
              </View>
            </View>
          </View>
        </View>

        {/* Items Section */}
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons
            name="format-list-bulleted"
            size={20}
            color={THEME.Color.second}
          />
          <Sys.Text style={styles.sectionTitle}>Danh sách vật phẩm</Sys.Text>
          <View style={styles.itemCount}>
            <Sys.Text style={styles.itemCountText}>{itemData.length}</Sys.Text>
          </View>
        </View>

        {itemData.map((subItem, index) => (
          <View key={subItem.id} style={styles.modernItemCard}>
            <View style={styles.itemHeader}>
              <View style={styles.itemTitleContainer}>
                <Sys.Text style={styles.itemName}>{subItem.name}</Sys.Text>
                <Sys.Text style={styles.itemCode}>#{subItem.code}</Sys.Text>
              </View>
              <View style={styles.quantityBadge}>
                <Sys.Text style={styles.quantityBadgeText}>
                  {subItem.actual_qty}/{subItem.qty}
                </Sys.Text>
              </View>
            </View>

            <View style={styles.quantitySection}>
              <View style={styles.quantityInfo}>
                <View style={styles.quantityItem}>
                  <MaterialCommunityIcons
                    name="book-open-variant"
                    size={16}
                    color={THEME.Color.gray}
                  />
                  <Sys.Text style={styles.quantityLabel}>Sổ sách</Sys.Text>
                  <Sys.Text style={styles.quantityValue}>{subItem.qty}</Sys.Text>
                </View>
              </View>

              {item.status !== 3 ? (
                <View style={styles.inputContainer}>
                  <Sys.Input
                    label={'Số lượng thực tế'}
                    keyboardType="numeric"
                    style={[
                      styles.modernInput,
                      subItem.actual_qty >= subItem.qty ? styles.inputSuccess : styles.inputWarning,
                    ]}
                    value={String(subItem.actual_qty)}
                    onChangeText={text => handleQuantityChange(text, index)}
                    editable={item.status === 2 && isPos}
                  />
                  {subItem.actual_qty < subItem.qty && (
                    <View style={styles.warningContainer}>
                      <MaterialCommunityIcons
                        name="alert-circle"
                        size={14}
                        color={THEME.Color.orange}
                      />
                      <Sys.Text style={styles.warningText}>
                        Thiếu {subItem.qty - subItem.actual_qty} sản phẩm
                      </Sys.Text>
                    </View>
                  )}
                </View>
              ) : (
                <View style={styles.lockedContainer}>
                  <MaterialCommunityIcons
                    name="lock"
                    size={16}
                    color={THEME.Color.gray}
                  />
                  <Sys.Text style={styles.lockedLabel}>Số lượng thực tế</Sys.Text>
                  <Sys.Text style={styles.lockedValue}>{subItem.actual_qty}</Sys.Text>
                </View>
              )}
            </View>
          </View>
        ))}

        {item.status === 2 && isPos && (
          <View style={styles.submitContainer}>
            <TouchableOpacity style={styles.modernSubmitButton} onPress={handleSubmit}>
              <MaterialCommunityIcons
                name="check-circle"
                size={20}
                color="white"
              />
              <Sys.Text style={styles.submitButtonText}>Xác nhận hoàn thành</Sys.Text>
            </TouchableOpacity>
          </View>
        )}

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </Sys.Container>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },

  // Header Card Styles
  headerCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.Color.text,
    marginLeft: 8,
  },
  infoRow: {
    marginBottom: 12,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 14,
    color: THEME.Color.gray,
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 16,
    color: THEME.Color.text,
    fontWeight: '600',
    flex: 1,
    textAlign: 'right',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },

  // Section Header Styles
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.Color.text,
    marginLeft: 8,
    flex: 1,
  },
  itemCount: {
    backgroundColor: THEME.Color.second,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 24,
    alignItems: 'center',
  },
  itemCountText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },

  // Modern Item Card Styles
  modernItemCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
    borderLeftWidth: 4,
    borderLeftColor: THEME.Color.primary,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  itemTitleContainer: {
    flex: 1,
    marginRight: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.Color.text,
    marginBottom: 4,
  },
  itemCode: {
    fontSize: 12,
    color: THEME.Color.gray,
    fontWeight: '500',
  },
  quantityBadge: {
    backgroundColor: THEME.Color.backgroundColor,
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: THEME.Color.primary,
  },
  quantityBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: THEME.Color.primary,
  },

  // Quantity Section Styles
  quantitySection: {
    gap: 12,
  },
  quantityInfo: {
    backgroundColor: THEME.Color.backgroundColor,
    borderRadius: 8,
    padding: 12,
  },
  quantityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  quantityLabel: {
    fontSize: 14,
    color: THEME.Color.gray,
    flex: 1,
  },
  quantityValue: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.Color.text,
  },

  // Input Styles
  inputContainer: {
    gap: 8,
  },
  modernInput: {
    borderRadius: 8,
    borderWidth: 2,
    backgroundColor: 'white',
  },
  inputSuccess: {
    borderColor: THEME.Color.green,
    backgroundColor: '#f0f9f0',
  },
  inputWarning: {
    borderColor: THEME.Color.orange,
    backgroundColor: '#fff8f0',
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 4,
  },
  warningText: {
    fontSize: 12,
    color: THEME.Color.orange,
    fontWeight: '500',
  },

  // Locked State Styles
  lockedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.Color.backgroundColor,
    borderRadius: 8,
    padding: 12,
    gap: 8,
  },
  lockedLabel: {
    fontSize: 14,
    color: THEME.Color.gray,
    flex: 1,
  },
  lockedValue: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.Color.text,
  },

  // Submit Button Styles
  submitContainer: {
    marginTop: 20,
    paddingHorizontal: 4,
  },
  modernSubmitButton: {
    backgroundColor: THEME.Color.blue,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    shadowColor: THEME.Color.blue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

  bottomSpacing: {
    height: 20,
  },
});

export default MarketingItemDetailsScreen;
