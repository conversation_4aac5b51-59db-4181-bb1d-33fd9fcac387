import React from 'react';
import {ActivityIndicator, useWindowDimensions, View} from 'react-native';
import {WebView} from 'react-native-webview';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Sys from '../../components/Sys';

const WebViewScreen = ({route, navigation}) => {
  const {item, accessToken} = route.params;
  const insets = useSafeAreaInsets();
  const {width, height} = useWindowDimensions();
  console.log('WebViewScreen', `${item?.url}?token=${accessToken}`);
  return (
    <Sys.Container hasHeader backgroundColor="white">
      <Sys.Header title={item?.name} />
      <View
        style={{
          top: insets.top,
          height: height - insets.top,
          width,
          backgroundColor: 'white',
        }}>
        <WebView
          source={{
            uri: `${item?.url}?token=${accessToken}`,
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          }}
          sharedCookiesEnabled={true}
          incognito={false}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          thirdPartyCookiesEnabled={true}
          renderLoading={() => (
            <ActivityIndicator
              color="blue"
              size="large"
              style={{flex: 1, justifyContent: 'center'}}
            />
          )}
          onError={syntheticEvent => {
            const {nativeEvent} = syntheticEvent;
            console.warn('WebView error: ', nativeEvent);
          }}
        />
      </View>
    </Sys.Container>
  );
};

export default WebViewScreen;
