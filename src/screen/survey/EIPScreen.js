import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  TouchableOpacity,
  View,
} from 'react-native';
import Sys from '../../components/Sys';
import SysStorage from '../../services/storage';
import CONST from '../../services/const';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import SysFetch from '../../services/fetch';
import RightIcon from './icons/RightIcon';
import moment from 'moment/moment';
import {useDispatch, useSelector} from 'react-redux';
import {updatePlanInventory} from '../device-list/services/inventory.slice';

const EIPScreen = () => {
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialDataLoaded, setIsInitialDataLoaded] = useState(false); // Biến mới để kiểm tra
  const navigation = useNavigation();
  const {pos} = useSelector(state => state.inventory);
  const dispatch = useDispatch();
  const isFocused = useIsFocused();

  const loadProductAudits = async page => {
    if (isLoading || page > lastPage || (page === 1 && isInitialDataLoaded)) {
      return;
    }
    setIsLoading(true);
    try {
      const response = await SysFetch.get('inventory-plan/getActive', {
        page,
        pos: pos?.posCode || null,
      });
      if (response.data && response.meta) {
        if (page === 1) {
          setIsInitialDataLoaded(true); // Đánh dấu là dữ liệu trang đầu tiên đã được tải
          setData(response.data);
        } else {
          setData(prevData => [...prevData, ...(response.data || [])]);
        }
        setCurrentPage(response.meta.current_page);
        setLastPage(response.meta.last_page);
      } else {
        console.log('Unexpected API response structure:', response);
      }
    } catch (error) {
      console.error('Error fetching product audits:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isFocused) {
      setIsInitialDataLoaded(false);
      loadProductAudits(1);
      // checkLocationAgain();
    }
  }, [isFocused]);

  // const checkLocationAgain = useCallback(() => {
  //   getCurrentLocation({
  //     highAccuracy: false,
  //     setLocation: location =>
  //       dispatch(
  //         setLocationCheckinData({
  //           lon: location?.longitude,
  //           lat: location?.latitude,
  //         }),
  //       ),
  //   });
  // }, []);

  return (
    <Sys.Container hasHeader backgroundColor="white">
      <Sys.Header title={'Danh sách kiểm kê TTB'} />
      <View style={{margin: 20}}>
        {data.length === 0 && !isLoading ? (
          <Sys.Text style={{textAlign: 'center', marginTop: 20}}>
            Không có dữ liệu
          </Sys.Text>
        ) : (
          <FlatList
            data={data}
            renderItem={({item}) => (
              <OneItem
                item={item}
                navigation={navigation}
                dispatch={dispatch}
              />
            )}
            keyExtractor={(item, index) => `${index}`}
            onEndReached={() => loadProductAudits(currentPage + 1)}
            onEndReachedThreshold={0.5}
            ListFooterComponent={
              isLoading && <ActivityIndicator size="large" color="blue" />
            }
          />
        )}
      </View>
    </Sys.Container>
  );
};

const OneItem = ({item, navigation, dispatch}) => {
  const [accessToken, setAccessToken] = useState('');

  useEffect(() => {
    const checkToken = async () => {
      const at = new SysStorage(CONST.STORAGE.ACCESS_TOKEN);
      const token = await at.get();
      setAccessToken(token);
    };
    checkToken();
  }, []);

  return (
    <TouchableOpacity
      onPress={() => {
        dispatch(updatePlanInventory({plan_id: item.id}));
        navigation.navigate('DeviceListScreen');
      }}
      style={{
        marginBottom: 10,
        borderRadius: 15,
        backgroundColor: '#90BB3B05',
        paddingVertical: 20,
        flexDirection: 'row',
        alignItems: 'center',
      }}>
      <View style={{marginHorizontal: 20, alignItems: 'center'}}>
        <View>
          <View
            style={{
              borderRadius: 50,
              height: 48,
              width: 48,
              backgroundColor: item?.completedAudit ? '#90BB3B' : '#0062FF',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            {!item?.completedAudit ? (
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Sys.Text
                  style={{
                    fontWeight: '700',
                    color: 'white',
                    lineHeight: 12,
                    fontSize: 12,
                  }}>
                  {moment(item?.start_date, 'YYYY-MM-DD').format('DD')}
                </Sys.Text>
                <Sys.Text
                  style={{
                    fontSize: 8,
                    fontWeight: '700',
                    color: 'white',
                    lineHeight: 12,
                  }}>
                  {moment(item.end_date, 'YYYY-MM-DD').format('MM/YYYY')}
                </Sys.Text>
              </View>
            ) : (
              <Sys.Text
                style={{
                  fontSize: 8,
                  color: '#ffffff',
                  marginTop: 2,
                }}>
                Đã làm
              </Sys.Text>
            )}
          </View>
        </View>
      </View>
      <View style={{flex: 1}}>
        <Sys.Text style={{color: '#0062FF'}}>{item.name}</Sys.Text>
      </View>
      <View style={{paddingRight: 15}}>
        <RightIcon />
      </View>
    </TouchableOpacity>
  );
};

export default EIPScreen;
