import React, {useEffect, useState} from 'react';
import {
  Al<PERSON>,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  View,
} from 'react-native';
import Sys from '../../components/Sys';
import SysStorage from '../../services/storage';
import CONST from '../../services/const';
import {useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import SysFetch from '../../services/fetch';
import RightIcon from './icons/RightIcon';
import moment from 'moment/moment';
import Color from '../../components/theme/Color';

const SurveyScreen = () => {
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false); // New state for refresh control
  const navigation = useNavigation();
  const {
    params: {pos_id},
  } = useRoute();

  const isFocused = useIsFocused();

  const loadSurveys = async page => {
    if (isLoading || page > lastPage) {
      return;
    }
    setIsLoading(true);
    try {
      const response = await SysFetch.get('survey/getActive', {
        page,
        pos: pos_id,
      });
      if (response.data && response.meta) {
        if (page === 1) {
          setData(response.data);
        } else {
          setData(prevData => [...prevData, ...(response.data || [])]);
        }
        setCurrentPage(response.meta.current_page);
        setLastPage(response.meta.last_page);
      } else {
        console.log('Unexpected API response structure:', response);
      }
    } catch (error) {
      console.error('Error fetching surveys:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isFocused) {
      handleRefresh();
    }
  }, [navigation, isFocused]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadSurveys(1);
    setIsRefreshing(false);
  };

  return (
    <Sys.Container hasHeader backgroundColor="white">
      <Sys.Header title={'Chương trình khảo sát'} />
      <View style={{margin: 20}}>
        {data.length === 0 && !isLoading ? (
          <Sys.Text style={{textAlign: 'center', marginTop: 20}}>
            Không có dữ liệu
          </Sys.Text>
        ) : (
          <FlatList
            data={data}
            renderItem={({item}) => (
              <OneItem item={item} navigation={navigation} />
            )}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) => `${index}`}
            onEndReached={() => loadSurveys(currentPage + 1)}
            onEndReachedThreshold={0.5}
            ListFooterComponent={
              <View style={{marginBottom: 80}} />
              // isLoading ? (
              //   <ActivityIndicator size="large" color="blue" />
              // ) : (
              //   <View style={{marginBottom: 80}} />
              // )
            }
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={handleRefresh}
              />
            }
          />
        )}
      </View>
    </Sys.Container>
  );
};
const renderStatus = item => {
  const currentDate = moment();

  if (item.user_result) {
    return (
      <View
        style={{
          borderRadius: 50,
          height: 48,
          width: 48,
          backgroundColor: Color.green,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Sys.Text
          style={{
            fontSize: 8,
            color: '#ffffff',
            marginTop: 2,
          }}>
          Đã làm
        </Sys.Text>
      </View>
    );
  } else if (currentDate.isAfter(moment(item.end_date))) {
    return (
      <View
        style={{
          borderRadius: 50,
          height: 48,
          width: 48,
          backgroundColor: Color.red,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Sys.Text
          style={{
            fontSize: 8,
            color: '#ffffff',
            marginTop: 2,
          }}>
          Đã kết thúc
        </Sys.Text>
      </View>
    );
  } else {
    return (
      <View
        style={{
          borderRadius: 50,
          height: 48,
          width: 48,
          backgroundColor: Color.blue,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Sys.Text
            style={{
              fontWeight: '700',
              color: 'white',
              lineHeight: 12,
              fontSize: 12,
            }}>
            {moment(item?.end_date, 'YYYY-MM-DD').format('DD')}
          </Sys.Text>
          <Sys.Text
            style={{
              fontSize: 8,
              fontWeight: '700',
              color: 'white',
              lineHeight: 12,
            }}>
            {moment(item.end_date, 'YYYY-MM-DD').format('MM/YYYY')}
          </Sys.Text>
        </View>
      </View>
    );
  }
};

const OneItem = ({item, navigation}) => {
  const [accessToken, setAccessToken] = useState('');

  useEffect(() => {
    const checkToken = async () => {
      const at = new SysStorage(CONST.STORAGE.ACCESS_TOKEN);
      const token = await at.get();
      setAccessToken(token);
    };
    checkToken();
  }, []);

  const handleStart = () => {
    if (!item.isTest) {
      Alert.alert('Bạn không có quyền làm khảo sát này.');
      return;
    }

    navigation.navigate('WebViewScreen', {
      item,
      accessToken,
    });
  };

  return (
    <TouchableOpacity
      onPress={handleStart}
      style={{
        marginBottom: 10,
        borderRadius: 15,
        backgroundColor: '#90BB3B05',
        paddingVertical: 20,
        flexDirection: 'row',
        alignItems: 'center',
      }}>
      <View style={{marginHorizontal: 20, alignItems: 'center'}}>
        {renderStatus(item)}
      </View>
      <View style={{flex: 1}}>
        <Sys.Text style={{color: '#0062FF'}}>{item.name}</Sys.Text>
      </View>
      <View style={{paddingRight: 15}}>
        <RightIcon />
      </View>
    </TouchableOpacity>
  );
};

export default SurveyScreen;
