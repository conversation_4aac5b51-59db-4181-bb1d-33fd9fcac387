import {takeEvery} from 'redux-saga/effects';
import SysFetch from '../../../services/fetch';
import {SurveyActions} from './survey.slice';

function* SurveySaga() {
  yield takeEvery(SurveyActions.getList, getList);
}

export default SurveySaga;

function* getList({payload}) {
  try {
    const {onSuccess} = payload;

    const rs = yield SysFetch.get(`survey/getActive`);
    onSuccess && onSuccess(rs.data);
  } catch (error) {
    console.log({error});
  }
}
