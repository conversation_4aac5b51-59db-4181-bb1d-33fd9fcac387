import React, {useEffect, useState} from 'react';
import {FlatList, RefreshControl, TouchableOpacity, View} from 'react-native';
import Sys from '../../components/Sys';
import SysStorage from '../../services/storage';
import CONST from '../../services/const';
import {useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import SysFetch from '../../services/fetch';
import RightIcon from './icons/RightIcon';
import Color from '../../components/theme/Color';

const MarketingScreen = () => {
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const navigation = useNavigation();
  const {
    params: {pos_id},
  } = useRoute();
  const isFocused = useIsFocused();
  const [isRefreshing, setIsRefreshing] = useState(false); // New state for refresh control

  const loadMarketingItems = async page => {
    if (isLoading || page > lastPage) {
      return;
    }
    setIsLoading(true);
    try {
      const response = await SysFetch.get('marketing/getMarketing', {
        page,
        pos: pos_id,
      });
      if (response.data && response.meta) {
        if (page === 1) {
          setData(response.data);
        } else {
          setData(prevData => [...prevData, ...(response.data || [])]);
        }
        setCurrentPage(response.meta.current_page);
        setLastPage(response.meta.last_page);
      } else {
        console.log('Unexpected API response structure:', response);
      }
    } catch (error) {
      console.error('Error fetching marketing items:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadMarketingItems(1);
  }, [navigation, isFocused]);

  useEffect(() => {
    if (isFocused) {
      handleRefresh();
    }
  }, [navigation, isFocused]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadMarketingItems(1);
    setIsRefreshing(false);
  };

  return (
    <Sys.Container hasHeader backgroundColor="white">
      <Sys.Header title={'Danh sách vật phẩm marketing'} />
      <View style={{margin: 20}}>
        {data.length === 0 && !isLoading ? (
          <Sys.Text style={{textAlign: 'center', marginTop: 20}}>
            Không có dữ liệu
          </Sys.Text>
        ) : (
          <FlatList
            data={data}
            renderItem={({item}) => (
              <OneItem item={item} navigation={navigation} />
            )}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) => `${index}`}
            onEndReached={() => loadMarketingItems(currentPage + 1)}
            onEndReachedThreshold={0.5}
            ListFooterComponent={<View style={{marginBottom: 80}} />}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={handleRefresh}
              />
            }
          />
        )}
      </View>
    </Sys.Container>
  );
};

const OneItem = ({item, navigation}) => {
  const [accessToken, setAccessToken] = useState('');

  useEffect(() => {
    const checkToken = async () => {
      const at = new SysStorage(CONST.STORAGE.ACCESS_TOKEN);
      const token = await at.get();
      setAccessToken(token);
    };
    checkToken();
  }, []);

  const getStatusDetails = status => {
    switch (status) {
      case 1:
        return {label: 'Chưa duyệt', color: Color.orange};
      case 2:
        return {label: 'Đã duyệt', color: Color.blue};
      case 3:
        return {label: 'Đã xác nhận', color: Color.green};
      default:
        return {label: 'Không xác định', color: '#808080'};
    }
  };

  const statusDetails = getStatusDetails(item.status);

  return (
    <TouchableOpacity
      onPress={() => {
        navigation.navigate('MarketingItemDetailsScreen', {
          item,
          statusDetails,
        });
      }}
      style={{
        marginBottom: 10,
        borderRadius: 15,
        backgroundColor: '#90BB3B05',
        paddingVertical: 20,
        flexDirection: 'row',
        alignItems: 'center',
      }}>
      <View style={{marginHorizontal: 20, alignItems: 'center'}}>
        <View>
          <View
            style={{
              borderRadius: 50,
              height: 48,
              width: 48,
              backgroundColor: statusDetails.color,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Sys.Text
              style={{
                fontSize: 8,
                color: '#ffffff',
                marginTop: 2,
              }}>
              {statusDetails.label}
            </Sys.Text>
          </View>
        </View>
      </View>
      <View style={{flex: 1}}>
        <Sys.Text style={{color: '#0062FF'}}>
          {item?.name || 'vật phẩm'} ({item?.items?.length})
        </Sys.Text>
      </View>
      <View style={{paddingRight: 15}}>
        <RightIcon />
      </View>
    </TouchableOpacity>
  );
};

export default MarketingScreen;
