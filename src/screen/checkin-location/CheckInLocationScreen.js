import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useCallback, useEffect, useState} from 'react';
import {
  Alert,
  Dimensions,
  Image,
  Linking,
  Platform,
  RefreshControl,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import MapView, {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE} from 'react-native-maps';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useDispatch, useSelector} from 'react-redux';
import Sys from '../../components/Sys';
import {formatVND, isAndroid} from '../../services/util';
import RightIcon from '../checkin-location-camera/icons/RightIcon';
import BackIcon from './icons/BackIcon';
import CloseDoorIcon from './icons/CloseDoorIcon';
import {PosActions} from '../sale-location/services/pos.slice';
import moment from 'moment';
import {
  getCurrentLocation,
  haversineDistance,
  stopWatchingLocation,
} from './locationUtils';
import _ from 'lodash';
import {check, PERMISSIONS, request, RESULTS} from 'react-native-permissions';
import CheckInLocationSkeleton from './components/CheckInLocationSkeleton';
import {
  setCheckinAt,
  setLocationCheckinData,
  setUpdateLocationData,
} from '../device-list/services/inventory.slice';
import {AppActions, AppSelectors} from '../../app.slice';
import Toast from 'react-native-toast-message';
import Divider from '../device-list/components/Divider';
import {AccountSelectors} from '../account/services/account.slice';
import SysFetch from '../../services/fetch';

const {width, height} = Dimensions.get('window');

const CheckInLocationScreen = () => {
  const {pos, allowCheckin, allowCheckout, request_update} = useSelector(
    state => state.inventory,
  );
  const profile = useSelector(AccountSelectors.profile);
  const {location, extras} = useSelector(AppSelectors.current_location);

  console.log({location, extras});
  const [isCheckin, setIsCheckin] = useState(allowCheckin);
  const {store_close_config, checkin_config} = useSelector(
    AppSelectors.settings,
  );

  const dispatch = useDispatch();
  const minimumDistanceCheckin = _.get(
    _.find(checkin_config, {key: 'coordinates'}),
    'minimum_distance',
    0,
  );
  const minimumDistanceCloseStore = _.get(
    _.find(store_close_config, {key: 'coordinates'}),
    'minimum_distance',
    0,
  );

  const isFocused = useIsFocused();
  const insets = useSafeAreaInsets();
  const {navigate, goBack} = useNavigation();

  const [isLoading, setIsLoading] = useState(false);
  const [isShowUpdate, setIsShowUpdate] = useState(false);
  const [cameraPermissionGranted, setCameraPermissionGranted] = useState(false);
  const [m, setM] = useState(0);
  const [mPos, setMPos] = useState(0);
  const [title, setTitle] = useState('Xác nhận vị trí');
  const [checkLocationTime, setCheckLocationTime] = useState(
    moment().format('HH:mm:ss DD/MM/YYYY'),
  );
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const [highAccuracy, setHighAccuracy] = useState(true);
  const [watchPosition, setWatchPosition] = useState(true);

  useEffect(() => {
    if (!pos) {
      goBack();
      return;
    }

    const updatedLon = request_update?.new_lon || pos.lon;
    const updatedLat = request_update?.new_lat || pos.lat;

    // Calculate the distance based on updated or existing coordinates
    const calculateDistance = (lon, lat) =>
      haversineDistance(
        {lon, lat},
        {lon: location?.longitude, lat: location?.latitude},
      );

    const newCalculatedDistance = calculateDistance(updatedLon, updatedLat);
    // console.log(
    //   'update',
    //   newCalculatedDistance,
    //   location,
    //   updatedLon,
    //   updatedLat,
    // );
    const newCalculatedDistanceActual = calculateDistance(pos.lon, pos.lat);
    // console.log('pos', newCalculatedDistanceActual, location, pos.lon, pos.lat);

    if (newCalculatedDistance !== m) {
      setM(newCalculatedDistance);
    }
    if (newCalculatedDistanceActual !== mPos) {
      setMPos(newCalculatedDistanceActual);
    }

    // if (newCalculatedDistanceActual !== mPos) {
    //   console.log({calculatedDistanceActual: newCalculatedDistanceActual});
    // }
  }, [location, pos, request_update, allowCheckin]);

  const getCameraPermission = () => {
    return Platform.select({
      ios: PERMISSIONS.IOS.CAMERA,
      android: PERMISSIONS.ANDROID.CAMERA,
    });
  };

  const checkPermissionCamera = useCallback(async () => {
    const cameraPermission = getCameraPermission();
    const permissionStatus = await check(cameraPermission);

    if (permissionStatus === RESULTS.GRANTED) {
      setCameraPermissionGranted(true);
    } else {
      setCameraPermissionGranted(false);
    }
  }, []);

  useEffect(() => {
    checkPermissionCamera();
  }, [checkPermissionCamera]);

  useEffect(() => {
    setIsCheckin(
      allowCheckin || (!allowCheckout && m < minimumDistanceCheckin),
    );
  }, [isCheckin, allowCheckin, allowCheckout, m]);

  const handlePermissionRequest = async () => {
    const cameraPermission = getCameraPermission();
    const requestedPermission = await request(cameraPermission);
    if (requestedPermission === RESULTS.GRANTED) {
      setCameraPermissionGranted(true);
    } else {
      showPermissionAlert();
    }
  };

  const showPermissionAlert = () => {
    Alert.alert('Thông báo', 'Hãy cấp quyền camera cho ứng dụng để tiếp tục.', [
      {
        text: 'Đóng',
        onPress: () => {},
      },
      {
        text: 'Tới cài đặt',
        onPress: () => {
          Linking.openSettings();
        },
      },
    ]);
  };

  const checkLocationAgain = useCallback(() => {
    getCurrentLocation({
      highAccuracy,
      setCallback: ({data}) => {
        setIsLoading(false);
        setTitle('Xác nhận vị trí');
        if (data) {
          if (!watchPosition) {
            setWatchPosition(true);
            setHighAccuracy(true);
          }
          dispatch(AppActions.setLocation(data));
        }
      },
      setCheckLocationTime,
      watchPosition,
    });
  }, [highAccuracy, watchPosition]);

  useEffect(() => {
    if (isFocused) {
      checkLocationAgain();
    }
    return () => stopWatchingLocation();
  }, [isFocused, highAccuracy, watchPosition]);

  const onClose = () => {
    if (!cameraPermissionGranted) {
      handlePermissionRequest();
      return;
    }
    navigate('CheckCloseScreen', {
      lon: location?.longitude,
      lat: location?.latitude,
      pos,
      m,
      store_close_config,
    });
  };

  const onNext = async (checkin = true) => {
    dispatch(
      setLocationCheckinData({
        lon: location?.longitude,
        lat: location?.latitude,
      }),
    );
    if (!cameraPermissionGranted) {
      handlePermissionRequest();
      return;
    }
    if (checkin) {
      dispatch(AppActions.setLoading(true));

      try {
        const {datetime} = await SysFetch.get('server-datetime');
        const checkin_time_at =
          datetime || moment().format('YYYY-MM-DD HH:mm:ss');
        console.log('checkin_time_at start', checkin_time_at);
        dispatch(AppActions.setLoading(false));
        dispatch(setCheckinAt(checkin_time_at));
        navigate('CheckInLocationCameraScreen');
      } catch (e) {
        dispatch(AppActions.setLoading(false));
        Toast.show({
          type: 'error',
          text1: 'Thông báo',
          text2: 'Không lấy được thời gian của server để check-in',
        });
        console.log(e);
      }
    }
  };
  const handleOpenDetailsModal = () => {
    setShowDetailsModal(true);
  };
  const onRefresh = () => {
    setRefreshing(true);
    stopWatchingLocation();
    checkLocationAgain();
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  return (
    <Sys.Container hasHeader hasFooter footerColor="white">
      <Sys.Header title={title} />
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={{flexGrow: 1}}>
        {isLoading ? (
          <CheckInLocationSkeleton />
        ) : (
          <View style={styles.container}>
            <View style={styles.mapContainer}>
              <MapView
                provider={PROVIDER_GOOGLE}
                style={styles.map}
                region={{
                  latitude: location?.latitude,
                  longitude: location?.longitude,
                  latitudeDelta: 0.01, // Adjust delta values according to your zoom needs
                  longitudeDelta: 0.01,
                }}>
                <Marker
                  coordinate={{
                    latitude: pos.lat,
                    longitude: pos.lon,
                  }}
                />
                <Marker
                  coordinate={{
                    latitude: location?.latitude,
                    longitude: location?.longitude,
                  }}>
                  <Image
                    style={styles.avatar}
                    source={{uri: profile?.avatar}}
                  />
                </Marker>
                <Polyline
                  strokeColor="#f00"
                  strokeWidth={1}
                  coordinates={[
                    {
                      latitude: location?.latitude,
                      longitude: location?.longitude,
                    },
                    {
                      latitude: pos.lat,
                      longitude: pos.lon,
                    },
                  ]}
                />
              </MapView>
            </View>
            <View style={styles.updatePosition}>
              <View style={styles.row}>
                <View style={styles.flex1}>
                  <TouchableOpacity
                    onPress={() => (isCheckin ? setIsShowUpdate(true) : {})}
                    style={[
                      styles.updateButton,
                      !isCheckin && styles.disabledButton,
                    ]}
                    disabled={!isCheckin}>
                    <Sys.Text style={styles.updateButtonText}>
                      Yêu cầu cập nhật vị trí
                    </Sys.Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.distanceInfo}>
                  <Sys.Text style={styles.distanceText}>
                    {formatVND(Math.round(mPos))}
                    <Sys.Text style={styles.distanceUnit}> mét</Sys.Text>
                  </Sys.Text>
                  <Sys.Text style={styles.checkLocationTime}>
                    {checkLocationTime}
                  </Sys.Text>
                </View>
              </View>
            </View>
            <ScrollView
              style={[
                styles.infoContainer,
                {flexBasis: pos?.banner ? height / 4 : 'auto'},
              ]}>
              {!isCheckin && request_update && (
                <View style={styles.reportStatus}>
                  <Sys.Text style={styles.reportStatusText}>
                    Có một yêu cầu cập nhật vị trí đang chờ duyệt và Bạn đã ghé
                    thăm trước đó. Vui lòng duyệt yêu cầu cập nhật vị trí cho
                    ĐBH này để tiếp tục ghé thăm
                  </Sys.Text>
                </View>
              )}
              {m > minimumDistanceCheckin && (
                <View style={styles.reportStatus}>
                  <Sys.Text style={styles.reportStatusText}>
                    Khoảng cách hiện tại của bạn quá xa so với yêu cầu tối thiểu{' '}
                    {minimumDistanceCheckin}(m) để đủ điều kiện ghé thăm ĐBH
                  </Sys.Text>
                </View>
              )}

              {pos?.banner && (
                <View style={{marginVertical: 15}}>
                  <Image style={styles.banner} source={{uri: pos.banner}} />
                </View>
              )}
            </ScrollView>
            <View style={styles.footer}>
              <View style={styles.posInfo}>
                <Sys.Text style={styles.posCodeText}>
                  Khoảng cách:{' '}
                  <Sys.Text
                    style={styles.modalDistance}
                    onPress={handleOpenDetailsModal}>
                    {formatVND(m)}m (
                    {request_update
                      ? 'So với vị trí yêu cầu cập nhật gần đây'
                      : 'so với điểm bán hàng'}
                    )
                  </Sys.Text>
                </Sys.Text>
                <Sys.Text style={styles.posCodeText}>
                  Mã điểm bán: {pos.posCode}
                </Sys.Text>
                <Sys.Text style={styles.addressText}>
                  Địa chỉ: {pos.address}
                </Sys.Text>
              </View>
              <View style={styles.actionRow}>
                <View style={styles.flex1}>
                  <TouchableOpacity
                    onPress={
                      minimumDistanceCloseStore < m || !isCheckin
                        ? () => {}
                        : onClose
                    }
                    style={[
                      styles.closeButton,
                      (minimumDistanceCheckin < m || !isCheckin) &&
                        styles.disabledButton,
                    ]}>
                    <CloseDoorIcon />
                    <Sys.Text style={styles.closeButtonText}>Đóng cửa</Sys.Text>
                  </TouchableOpacity>
                </View>
                <View style={[styles.flex2, {marginLeft: 20}]}>
                  <TouchableOpacity
                    onPress={
                      minimumDistanceCheckin < m ||
                      !isCheckin ||
                      !checkin_config
                        ? () => {}
                        : onNext
                    }
                    style={[
                      styles.confirmButton,
                      (minimumDistanceCheckin < m || !isCheckin) &&
                        styles.disabledButton,
                    ]}>
                    <Sys.Text style={styles.confirmButtonText}>
                      GHÉ THĂM
                    </Sys.Text>
                    <BackIcon />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
      <Modal
        animationIn="fadeInUp"
        animationOut="fadeOutDown"
        style={styles.modal}
        isVisible={isShowUpdate}
        onBackdropPress={() => setIsShowUpdate(false)}
        onSwipeComplete={() => setIsShowUpdate(false)}
        swipeDirection="down">
        <View style={styles.flex1} />
        <View
          style={[styles.modalContent, {paddingBottom: insets.bottom + 20}]}>
          <Sys.Text style={styles.modalTitle}>
            Xác nhận cập nhật lại vị trí
          </Sys.Text>
          <Sys.Text style={styles.modalSubTitle}>
            Mã điểm bán: {pos.posCode}
          </Sys.Text>
          <Sys.Text style={styles.modalText}>
            Thời gian: {moment().format('DD/MM/YYYY HH:mm:ss')}
          </Sys.Text>
          <Sys.Text style={styles.modalText}>
            Khoảng cách:{' '}
            <Sys.Text style={styles.modalDistance}>
              {formatVND(m)}m (
              {request_update
                ? 'So với vị trí yêu cầu cập nhật gần đây'
                : 'so với điểm bán hàng'}
              )
            </Sys.Text>
          </Sys.Text>
          <Sys.Text style={styles.modalText}>Địa chỉ: {pos.address}</Sys.Text>
          <View style={styles.modalButtonContainer}>
            <TouchableOpacity
              onPress={() => {
                setIsShowUpdate(false);
                dispatch(AppActions.setLoading(true));
                dispatch(
                  PosActions.proposeLatLon({
                    onSuccess: rs => {
                      dispatch(AppActions.setLoading(false));
                      Toast.show({
                        type: 'success',
                        text1: 'Thông báo',
                        text2: 'Gửi yêu cầu cập nhật thành công',
                      });
                      dispatch(setUpdateLocationData(rs));
                      onNext(false);
                    },
                    onFail: rs => {
                      dispatch(AppActions.setLoading(false));
                      Toast.show({
                        type: 'error',
                        text1: 'Lỗi',
                        text2: rs,
                      });
                    },
                    body: {
                      pos_code: pos.posCode,
                      new_lat: location?.latitude,
                      new_lon: location?.longitude,
                    },
                  }),
                );
              }}
              style={styles.modalConfirmButton}>
              <Sys.Text style={styles.modalConfirmButtonText}>
                XÁC NHẬN
              </Sys.Text>
              <RightIcon />
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <Modal
        isVisible={showDetailsModal}
        onBackdropPress={() => setShowDetailsModal(false)}
        onSwipeComplete={() => setShowDetailsModal(false)}
        swipeDirection="down"
        style={[styles.modal, {justifyContent: 'flex-end'}]}>
        <View style={styles.modalContent}>
          <Sys.Text style={styles.modalTitle}>
            Thông tin vị trí chi tiết
          </Sys.Text>
          <Divider />
          <Sys.Text style={styles.modalText}>
            Tốc độ: {location.speed?.toFixed(2) || 0} m/s
          </Sys.Text>
          <Sys.Text style={{fontSize: 10}}>
            Đo tốc độ thiết bị đang di chuyển, tính bằng mét trên giây
          </Sys.Text>
          <Divider />
          <Sys.Text style={styles.modalText}>
            Max C/N0: {extras?.maxCn0 || 0} dB-Hz
          </Sys.Text>
          <Sys.Text style={{fontSize: 10}}>
            Tỷ lệ sóng mang trên nhiễu tối đa quan sát được từ tất cả các vệ
            tinh có thể nhìn thấy
          </Sys.Text>
          <Divider />

          <Sys.Text style={styles.modalText}>
            Mean C/N0: {extras?.meanCn0 || 0} dB-Hz
          </Sys.Text>
          <Sys.Text style={{fontSize: 10}}>
            Tỷ lệ sóng mang trên nhiễu trung bình từ tất cả các vệ tinh có thể
            nhìn thấy
          </Sys.Text>
          <Divider />

          <Sys.Text style={styles.modalText}>
            Vệ tinh: {extras?.satellites || 0}
          </Sys.Text>
          <Sys.Text style={{fontSize: 10}}>
            Số lượng vệ tinh mà thiết bị hiện đang nhận tín hiệu
          </Sys.Text>
          <Divider />

          <TouchableOpacity
            style={styles.modalConfirmButton}
            onPress={() => setShowDetailsModal(false)}>
            <Sys.Text style={styles.modalConfirmButtonText}>Đóng</Sys.Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </Sys.Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mapContainer: {
    height: height / 3,
    flexGrow: 1,
    flexShrink: 0,
  },
  map: {
    width: '100%',
    height: '100%',
  },
  avatar: {
    height: 30,
    width: 30,
    borderRadius: 50,
  },
  updatePosition: {
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 1,
    backgroundColor: 'white',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: -20,
  },
  infoContainer: {
    flexBasis: height / 4,
    flexGrow: 0,
    flexShrink: 1,
    backgroundColor: 'white',
  },
  footer: {
    flexBasis: isAndroid ? 170 : 150,
    flexShrink: 1,
    flexGrow: 0,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#ccc',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flex1: {
    flex: 1,
  },
  flex2: {
    flex: 2,
  },
  updateButton: {
    borderRadius: 50,
    backgroundColor: '#D30B0D',
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    marginHorizontal: 10,
    paddingHorizontal: 10,
  },
  updateButtonText: {
    color: 'white',
    fontSize: 13,
    marginLeft: 10,
    textAlign: 'center',
  },
  distanceInfo: {
    flex: 0,
    paddingVertical: 20,
    paddingHorizontal: 10,
  },
  distanceText: {
    fontSize: 28,
    color: '#F5000D',
    textAlign: 'right',
  },
  distanceUnit: {
    color: '#848484',
    fontSize: 12,
  },
  checkLocationTime: {
    color: '#848484',
    fontSize: 12,
    textAlign: 'right',
  },
  reportStatus: {
    backgroundColor: '#EF7721',
    padding: 10,
    marginVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 10,
    borderRadius: 5,
    paddingVertical: 20,
  },
  reportStatusText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 12,
  },
  banner: {
    height: 200,
    width: '100%',
  },
  posInfo: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  posCodeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#001451',
    lineHeight: 16,
  },
  addressText: {
    color: '#001451',
    lineHeight: 18,
    fontSize: 12,
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
  },
  closeButton: {
    backgroundColor: '#EF7721',
    height: 36,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
  },
  closeButtonText: {
    color: 'white',
    fontWeight: '600',
    lineHeight: 24,
    marginLeft: 10,
  },
  confirmButton: {
    backgroundColor: '#0062FF',
    height: 50,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  confirmButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 18,
    lineHeight: 28,
    flex: 1,
    textAlign: 'center',
  },
  disabledButton: {
    backgroundColor: '#CCC',
  },
  modal: {
    padding: 0,
    margin: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    width: '100%',
    borderTopStartRadius: 20,
    borderTopEndRadius: 20,
    padding: 20,
  },
  modalTitle: {
    fontWeight: '600',
    fontSize: 18,
    color: '#001451',
  },
  modalSubTitle: {
    marginTop: 15,
    fontWeight: '600',
    color: '#001451',
  },
  modalText: {
    marginTop: 5,
    color: '#001451',
  },
  modalDistance: {
    fontWeight: '600',
  },
  modalButtonContainer: {
    flexDirection: 'row',
    marginTop: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalConfirmButton: {
    height: 50,
    backgroundColor: '#F5000D',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  modalConfirmButtonText: {
    color: 'white',
    fontWeight: '700',
    fontSize: 18,
    marginRight: 20,
  },
});

export default CheckInLocationScreen;
