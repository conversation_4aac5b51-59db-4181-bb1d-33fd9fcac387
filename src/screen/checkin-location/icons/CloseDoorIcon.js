import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const CloseDoorIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={18}
    height={19}
    fill="none"
    {...props}>
    <Path
      fill="#fff"
      fillRule="evenodd"
      d="M12.432 2.497v2.2l1.378-1.396V.705L13.121 0H.717l-.69.705v.686L0 1.41v14.514l.496.649L7.387 19l.883-.663v-1.41h4.851l.69-.706v-2.553l-1.38-1.41v3.258H8.27V3.823l-.455-.65-5.009-1.762h9.626v1.086Zm-5.54 14.825-5.514-1.89V2.425l5.513 1.89v13.006Zm8.985-8.182h-6.85V7.73h6.795l-2.205-2.257.979-.987L18 7.956v1l-3.432 3.5-.965-.988 2.274-2.328Z"
      clipRule="evenodd"
    />
  </Svg>
);
export default CloseDoorIcon;
