import Geolocation from '@react-native-community/geolocation';
import moment from 'moment';
import {Alert} from 'react-native';
import {
  check,
  openSettings,
  PERMISSIONS,
  RESULTS,
} from 'react-native-permissions';

let isRequestingLocation = false;
let watchID = null;

let isCheckingPermission = false;

const checkLocationPermission = async () => {
  if (isCheckingPermission) {
    return;
  } // Prevent multiple permission checks at once
  isCheckingPermission = true;

  const result = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);

  switch (result) {
    case RESULTS.LIMITED:
      console.log('Quyền vị trí bị giới hạn');
      break;
    case RESULTS.GRANTED:
      console.log('Quyền vị trí đã được cấp');
      break;
    case RESULTS.UNAVAILABLE:
    case RESULTS.DENIED:
    case RESULTS.BLOCKED:
      console.log('Quyền vị trí bị chặn. Mở cài đặt...');
      Alert.alert(
        'Quyền vị trí bị từ chối',
        'Ứng dụng cần quyền truy cập vị trí để hoạt động. Vui lòng cấp quyền trong cài đặt.',
        [
          {text: 'Hủy', style: 'cancel'},
          {text: 'Mở Cài Đặt', onPress: () => openSettings()},
        ],
      );
      break;
  }
  isCheckingPermission = false;
};

/**
 * Khi  muốn lấy vị trí hiện tại:
 * getCurrentLocation(true, false, setLocation, setCheckLocationTime);
 * Khi muốn theo dõi vị trí liên tục:
 * getCurrentLocation(true, false, setLocation, setCheckLocationTime, 0, true, { distanceFilter: 10, interval: 5000 });
 *
 * */
export const getCurrentLocation = ({
  highAccuracy,
  setLocation = () => {},
  setCallback = () => {},
  setCheckLocationTime = () => {},
  retryCount = 0,
  watchPosition = false,
  options = {
    distanceFilter: 10,
    fastestInterval: 10000,
  },
}) => {
  if (isRequestingLocation) {
    console.log({isRequestingLocation});
    return;
  }
  isRequestingLocation = true;

  const successCallback = position => {
    isRequestingLocation = false;
    // console.log('getCurrentPosition Location success', position);
    setLocation(position.coords);
    setCallback({data: position});
    setCheckLocationTime(
      moment(position.timestamp).format('DD/MM/YYYY HH:mm:ss'),
    );
  };

  const errorCallback = error => {
    isRequestingLocation = false;
    if (retryCount < 3) {
      console.log(`Retry getting location - Attempt ${retryCount + 1}`);
      getCurrentLocation({
        highAccuracy,
        setLocation,
        setCheckLocationTime,
        retryCount: retryCount + 1,
        watchPosition,
        options,
      });
    } else {
      console.log('getCurrentLocation', error);
      handleLocationError(error);
      setCallback({error});
    }
  };

  const config = {
    enableHighAccuracy: highAccuracy,
    maximumAge: 1000,
  };

  /*  console.log('Location request is already in progress', {
    config,
    watchPosition,
    options,
  });*/

  if (watchPosition) {
    watchID = Geolocation.watchPosition(successCallback, errorCallback, {
      ...config,
      ...options,
    });
    console.log('watchPosition....', {watchID});
  } else {
    console.log('getCurrentPosition ....');

    Geolocation.getCurrentPosition(successCallback, errorCallback, config);
  }
};

export const handleLocationError = error => {
  if (error.code === 1) {
    console.log('Yêu cầu quyền vị trí lại...');
    checkLocationPermission();
    return;
  }

  const errorMessages = {
    1: {
      // PERMISSION_DENIED
      title: 'Quyền truy cập bị từ chối',
      message:
        'Ứng dụng cần quyền truy cập vào vị trí để sử dụng tính năng này.',
    },
    2: {
      // POSITION_UNAVAILABLE
      title: 'Dịch vụ không khả dụng',
      message: 'Dịch vụ vị trí đã bị tắt hoặc không khả dụng.',
    },
    3: {
      // TIMEOUT
      title: 'Yêu cầu tọa độ không phản hồi',
      message: 'Yêu cầu vị trí bị hết thời gian. Vui lòng thử lại.',
    },
    DEFAULT: {
      // Other errors
      title: 'Cảnh báo',
      message: 'Không xác định được vị trí vui lòng thử lại.',
    },
  };

  const {title, message} = errorMessages[error.code] || errorMessages.DEFAULT;
  Alert.alert(title, message);
};

export const stopWatchingLocation = () => {
  console.log('Attempting to stop watching location with ID:', watchID);

  if (watchID !== null) {
    console.log('stopWatchingLocation', watchID);
    Geolocation.clearWatch(watchID);
    watchID = null;
  }

  if (isRequestingLocation) {
    isRequestingLocation = false;
  }
};

export function haversineDistance(coords1, coords2, isMiles = false) {
  function toRad(x) {
    return (x * Math.PI) / 180;
  }

  var lon1 = coords1.lon;
  var lat1 = coords1.lat;

  var lon2 = coords2.lon;
  var lat2 = coords2.lat;

  var R = 6371000;
  var x1 = lat2 - lat1;
  var dLat = toRad(x1);
  var x2 = lon2 - lon1;
  var dLon = toRad(x2);
  var a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(lat1)) *
      Math.cos(toRad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  var d = R * c;

  if (isMiles) {
    d *= 0.000621371;
  }
  return d;
}
