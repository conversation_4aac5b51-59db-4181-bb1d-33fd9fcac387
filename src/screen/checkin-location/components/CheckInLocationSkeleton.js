import React from 'react';
import {StyleSheet, View} from 'react-native';
import ContentLoader, {Rect} from 'react-content-loader/native';

const CheckInLocationSkeleton = () => {
  return (
    <View style={styles.container}>
      <ContentLoader
        speed={2}
        width={'100%'}
        height={600}
        viewBox="0 0 400 600"
        backgroundColor="#f3f3f3"
        foregroundColor="#ecebeb">
        <Rect x="0" y="0" rx="10" ry="10" width="100%" height="200" />
        <Rect x="20" y="220" rx="10" ry="10" width="360" height="20" />
        <Rect x="20" y="260" rx="10" ry="10" width="300" height="20" />
        <Rect x="20" y="300" rx="10" ry="10" width="200" height="20" />
        <Rect x="20" y="340" rx="10" ry="10" width="250" height="20" />
        <Rect x="20" y="380" rx="10" ry="10" width="150" height="20" />
        <Rect x="20" y="420" rx="10" ry="10" width="100%" height="150" />
      </ContentLoader>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    marginBottom: 20,
  },
});

export default CheckInLocationSkeleton;
