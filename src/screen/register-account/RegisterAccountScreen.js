import React, {useState} from 'react';
import {Platform, TouchableOpacity, View} from 'react-native';
import Toast from 'react-native-toast-message';
import {useDispatch} from 'react-redux';
import {AppActions} from '../../app.slice';
import Sys from '../../components/Sys';
import {useNavigation} from '@react-navigation/native';
import {LoginActions} from '../login/services/login.slice';
import {getDeviceInfo} from '../../services/util';

const RegisterAccountScreen = () => {
  const {navigate} = useNavigation();
  const dispatch = useDispatch();

  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rePassword, setRePassword] = useState('');

  const onAccept = () => {
    if (!validateVietnamesePhoneNumber(username)) {
      Toast.show({
        type: 'error',
        text1: 'Thông báo',
        text2: '<PERSON>ố điện thoại không đúng định dạng',
      });
      return;
    }
    if (rePassword != password) {
      Toast.show({
        type: 'error',
        text1: 'Thông báo',
        text2: 'Nhập lại mật khẩu không đúng',
      });
      return;
    }
    if (!validatePassword(password)) {
      Toast.show({
        type: 'error',
        text1: 'Thông báo',
        text2: 'Mật khẩu không đúng định dạng',
      });
      return;
    }
    onRequest();
  };

  const onRequest = async () => {
    try {
      dispatch(AppActions.setLoading(true));
      const {device_id, device_token, device_name, deviceUniqueId} =
        await getDeviceInfo();
      const body = {
        username,
        password,
        device_id,
        device_name,
        deviceUniqueId,
        device_token,
        platform: Platform.OS,
      };
      dispatch(
        LoginActions.handleRegistration({
          body,
          onSuccess: token => {
            dispatch(AppActions.setLoading(false));
          },
          onFail: message => {
            dispatch(AppActions.setLoading(false));

            Toast.show({
              type: 'error',
              text1: 'Thông báo',
              text2: message,
            });
          },
        }),
      );
    } catch (error) {
      console.log(error);
    }
  };

  function validatePassword(password) {
    // Kiểm tra độ dài trên 8 ký tự, ít nhất 1 ký tự số, 1 ký tự chữ hoa và 1 ký tự đặc biệt
    var regex = /^(?=.*\d)(?=.*[A-Z])(?=.*\W).{8,}$/;

    // Trả về kết quả kiểm tra
    return regex.test(password);
  }

  function validateVietnamesePhoneNumber(phoneNumber) {
    console.log(phoneNumber);
    // Mẫu regex để kiểm tra số điện thoại Việt Nam
    var regex = /^(0|\+84)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])[0-9]{7}$/;

    // Kiểm tra số điện thoại dựa trên mẫu regex
    return regex.test(phoneNumber);
  }

  return (
    <Sys.Container hasHeader headerColor="red">
      <Sys.Header title={'Đăng ký tài khoản'} color="red" />
      <View
        style={{
          padding: 20,
        }}>
        <View
          style={{
            marginBottom: 10,
          }}>
          <Sys.Text
            style={{
              marginBottom: 10,
            }}>
            Số điện thoại
          </Sys.Text>
          <Sys.Input
            value={username}
            onChangeText={e => {
              setUsername(e);
            }}
            placeholder={'Nhập số điện thoại'}
          />
        </View>
        <View
          style={{
            marginBottom: 10,
          }}>
          <Sys.Text
            style={{
              marginBottom: 10,
            }}>
            Mật khẩu
          </Sys.Text>
          <Sys.Input
            value={password}
            onChangeText={e => {
              setPassword(e);
            }}
            secureTextEntry={true}
            placeholder={'Nhập mật khẩu'}
          />
        </View>
        <View
          style={{
            marginBottom: 10,
          }}>
          <Sys.Text
            style={{
              marginBottom: 10,
            }}>
            Nhập lại mật khẩu
          </Sys.Text>
          <Sys.Input
            value={rePassword}
            onChangeText={e => {
              setRePassword(e);
            }}
            secureTextEntry={true}
            placeholder={'Nhập lại mật khẩu'}
          />
        </View>
        <View
          style={{
            marginTop: 20,
          }}>
          <TouchableOpacity
            onPress={onAccept}
            style={{
              backgroundColor: 'red',
              padding: 10,
              borderRadius: 10,
            }}>
            <Sys.Text
              style={{
                color: 'white',
                textAlign: 'center',
                fontWeight: '600',
              }}>
              Xác nhận
            </Sys.Text>
          </TouchableOpacity>
        </View>
        <View
          style={{
            marginTop: 30,
          }}>
          <Sys.Text
            style={{
              color: '#666',
              fontWeight: '500',
            }}>
            Mật khẩu phải thỏa mãn các điều kiện sau:
          </Sys.Text>
          <Sys.Text
            style={{
              marginLeft: 10,
              color: '#666',
              marginTop: 5,
            }}>
            Có độ dài từ 8 đến 20 kí tự.
          </Sys.Text>
          <Sys.Text
            style={{
              color: '#666',
              marginLeft: 10,
              marginTop: 5,
            }}>
            Chứa ít nhất 01 ký tự số, 01 ký tự chữ Hoa, 01 ký tự đặc biệt
          </Sys.Text>
          <Sys.Text
            style={{
              color: '#666',
              marginLeft: 10,
              marginTop: 5,
            }}>
            Ví dụ: V@123456; 123456@V; 123456V@
          </Sys.Text>
        </View>
      </View>
    </Sys.Container>
  );
};

export default RegisterAccountScreen;
