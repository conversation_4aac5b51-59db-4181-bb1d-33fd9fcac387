import React, {useCallback, useRef, useState} from 'react';
import {
  Image,
  Platform,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {
  Camera,
  useCameraDevice,
  useCameraFormat,
} from 'react-native-vision-camera';
import Sys from '../../components/Sys';
import {useNavigation, useRoute} from '@react-navigation/native';
import * as ANT from 'react-native-animatable';
import CameraButtonIcon from '../checkin-location-camera/icons/CameraButtonIcon';
import RightIcon from '../checkin-location-camera/icons/RightIcon';
import ReCapturetIcon from './icons/ReCapturetIcon';
import {isIOS, resizeAndCompressImage} from '../../services/util';
import {useSelector} from 'react-redux';
import {AppSelectors} from '../../app.slice';

const CameraScreen = () => {
  const route = useRoute();
  const {
    nextScreen = false,
    onCompleteCamera,
    title = '<PERSON><PERSON><PERSON> ảnh trang thiết bị',
    description = '<PERSON><PERSON><PERSON><PERSON> viên cần chụp ảnh trang thiết bị sao cho có thể nhìn rõ mã thiết bị (nếu có)',
  } = route.params;
  const device = useCameraDevice('back', {
    physicalDevices: ['wide-angle-camera'],
  });

  const {image_rules} = useSelector(AppSelectors.settings);

  const resolutionSettings = Platform.select({
    ios: {
      videoResolution: {
        width: image_rules?.max_width || 1920,
        height: image_rules?.max_height || 1080,
      },
    },
    android: {
      photoResolution: {
        width: image_rules?.max_width || 1920,
        height: image_rules?.max_height || 1080,
      },
    },
  });

  const cameraProps = Platform.select({
    ios: {video: true},
    android: {photo: true},
  });

  const format = useCameraFormat(device, [resolutionSettings]);

  const {navigate, goBack} = useNavigation();
  const [step, setStep] = useState(1);
  const [photo, setPhoto] = useState(null);
  const camera = useRef(null);
  const {width, height} = useWindowDimensions();

  const onTakePhoto = useCallback(async () => {
    try {
      const photo = await camera.current.takeSnapshot({
        quality: image_rules.quality || 60,
      });
      if (photo && photo.path) {
        const uri = `file://${photo.path}`;
        let result = {uri};
        const response = await fetch(uri);
        const blob = await response.blob();
        const fileSizeBytes = blob.size;
        const fileSizeKilobytes = fileSizeBytes / 1024;

        console.log(`Original file size: ${fileSizeKilobytes.toFixed(2)} KB`);

        // Check if the image needs to be resized or compressed
        if (
          (image_rules && image_rules.max < fileSizeKilobytes) ||
          image_rules.isWebp ||
          isIOS
        ) {
          result = await resizeAndCompressImage(
            uri,
            image_rules?.max_width || 1920,
            image_rules?.max_height || 1080,
            image_rules.quality || 60,
            image_rules.isWebp,
            photo.orientation,
          );

          // Fetch the new file size after resize
          const newUri = result.uri;
          const newResponse = await fetch(newUri);
          const newBlob = await newResponse.blob();
          const newFileSizeBytes = newBlob.size;
          const newFileSizeKilobytes = newFileSizeBytes / 1024;

          console.log(
            `Resized file size: ${newFileSizeKilobytes.toFixed(2)} KB`,
          );
        }

        setPhoto(result);
        setStep(2);
      }
    } catch (error) {
      console.error('Failed to take photo', error);
    }
  }, []);

  const handleContinue = () => {
    const onCompleteWithPhoto = onCompleteCamera(photo);
    if (nextScreen) {
      navigate(nextScreen, {
        onComplete: onCompleteWithPhoto,
      });
    } else {
      if (typeof onCompleteWithPhoto === 'function') {
        onCompleteWithPhoto({});
      } else {
        onCompleteCamera(photo);
      }
    }
  };

  return (
    <Sys.Container
      hasHeader
      headerColor={step === 1 ? '#848484' : step === 2 ? '#001451' : '#001451'}
      backgroundColor={
        step === 1 ? '#848484' : step === 2 ? '#001451' : '#001451'
      }
      footerColor={step === 1 ? '#848484' : step === 2 ? '#001451' : '#001451'}
      hasFooter>
      <Sys.Header
        onBack={() => {
          const onCompleteWithPhoto = onCompleteCamera(null);
          if (typeof onCompleteWithPhoto === 'function') {
            onCompleteWithPhoto(null);
          } else {
            onCompleteCamera(null);
          }
        }}
        color={'#00000000'}
        title={step === 2 ? title : 'Quay lại'}
      />
      {step === 1 && (
        <ANT.View
          animation={'fadeInRight'}
          style={{
            flex: 1,
            backgroundColor: '#848484',
          }}>
          <View style={{flex: 1, paddingHorizontal: 20}}>
            <View
              style={{
                marginTop: 5,
                paddingHorizontal: 10,
                paddingVertical: 10,
                borderRadius: 20,
                flexDirection: 'row',
              }}>
              <View style={{flex: 1}}>
                <Sys.Text
                  style={{
                    lineHeight: 20,
                    marginVertical: 30,
                    marginBottom: 10,
                    color: '#FFFFFF',
                  }}>
                  {/*{description}*/}
                </Sys.Text>
              </View>
            </View>
            <View
              style={{
                marginTop: 15,
                alignItems: 'center',
                backgroundColor: 'black',
              }}>
              <Camera
                photoQualityBalance="speed"
                style={{width: width - 40, height: height / 2}}
                isActive={step === 1}
                ref={camera}
                device={device}
                format={format}
                {...cameraProps}
                enableZoomGesture={true}
              />
            </View>
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              padding: 20,
              marginBottom: 20,
            }}>
            <TouchableOpacity onPress={onTakePhoto}>
              <CameraButtonIcon />
            </TouchableOpacity>
          </View>
        </ANT.View>
      )}
      {step === 2 && (
        <ANT.View
          animation={'fadeInRight'}
          style={{flex: 1, backgroundColor: '#001451'}}>
          <View style={{flex: 1, paddingHorizontal: 20}}>
            <View
              style={{
                marginTop: 5,
                paddingHorizontal: 10,
                paddingVertical: 10,
                borderRadius: 20,
                flexDirection: 'row',
              }}>
              <View style={{flex: 1}}>
                <Sys.Text
                  style={{
                    lineHeight: 20,
                    marginVertical: 30,
                    marginBottom: 10,
                    color: '#FFFFFF',
                  }}>
                  {description}
                </Sys.Text>
              </View>
            </View>
            <View
              style={{
                marginTop: 15,
                alignItems: 'center',
                backgroundColor: 'black',
              }}>
              <Image
                source={{uri: photo.uri}}
                style={{width: '100%', height: height / (isIOS ? 2.4 : 2)}}
              />
            </View>
            <View
              style={{
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: 20,
              }}>
              <TouchableOpacity
                onPress={() => setStep(1)}
                style={{
                  marginTop: 20,
                  paddingHorizontal: 40,
                  paddingVertical: 10,
                  backgroundColor: '#FFFFFF',
                  borderRadius: 20,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <ReCapturetIcon />
                <Sys.Text
                  style={{
                    marginLeft: 10,
                    textTransform: 'uppercase',
                    color: '#001451',
                    fontWeight: '700',
                  }}>
                  Chụp lại
                </Sys.Text>
              </TouchableOpacity>
            </View>
          </View>
          <View
            style={{
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: 20,
            }}>
            <TouchableOpacity
              onPress={handleContinue}
              style={{
                flexDirection: 'row',
                backgroundColor: '#0062FF',
                height: 50,
                alignItems: 'center',
                borderRadius: 50,
                width: 200,
                justifyContent: 'space-between',
                paddingRight: 20,
                paddingLeft: 50,
                marginBottom: 10,
                paddingVertical: 10,
              }}>
              <Sys.Text
                style={{
                  color: 'white',
                  fontWeight: '600',
                  fontSize: 20,
                  lineHeight: 28,
                  marginRight: 10,
                }}>
                Tiếp theo
              </Sys.Text>
              <RightIcon />
            </TouchableOpacity>
          </View>
        </ANT.View>
      )}
    </Sys.Container>
  );
};

export default CameraScreen;
