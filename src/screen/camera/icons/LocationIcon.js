import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const LocationIcon = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={23}
    height={22}
    fill="none"
    {...props}>
    <Path
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="M3.93 15.379c0 .812.331 1.543.993 2.112.58.568 1.407.893 2.235.974m-5.794-3.899c-.331 1.706.166 3.574 1.49 4.874 1.324 1.3 3.228 1.787 4.966 1.462m1.656-4.467-3.56-3.493c-.496-.488-.496-1.219 0-1.706l6.705-6.58a1.216 1.216 0 0 1 1.738 0l3.56 3.493c.496.487.496 1.218 0 1.706l-6.705 6.58c-.497.487-1.325.487-1.738 0Zm12.167-.244-5.05-4.955-4.055 3.98 5.05 4.955 4.055-3.98ZM2.192 4.98 7.24 9.935l4.056-3.98L6.247 1 2.192 4.98Z"
    />
  </Svg>
);
export default LocationIcon;
