import React, {useCallback, useState} from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';
import {
  Camera,
  useCameraDevice,
  useCodeScanner,
} from 'react-native-vision-camera';
import SysFetch from '../../services/fetch';
import {useSelector} from 'react-redux';
import {AppSelectors} from '../../app.slice';
import Sys from '../../components/Sys';
import * as ANT from 'react-native-animatable';
import {useNavigation, useRoute} from '@react-navigation/native';
import Modal from 'react-native-modal';

const {width, height} = Dimensions.get('window');
const overlayWidth = width * 0.1;
const overlayHeight = (height - width * 0.8) / 2;
const scanAreaHeight = width * 0.8;

/**
 *
 * // Khi mở modal
 * navigate('ScanCodeScreen', {
 *   itemId: item.id,
 *   onScanComplete: ()=>{},
 * });
 * */

const ScanCodeScreen = () => {
  const device = useCameraDevice('back');
  const route = useRoute();
  const {onComplete} = route.params;
  const navigation = useNavigation();
  const {barcode_prefix: prefixes} = useSelector(AppSelectors.settings);
  const [isActive, setIsActive] = useState(true);
  const [showErrorModal, setShowErrorModal] = useState(false);

  const [isProcessing, setIsProcessing] = useState(false);
  const [scanAttempts, setScanAttempts] = useState(0);
  const callBarcodeAPI = async id => {
    try {
      return await SysFetch.get(`/inventory/item/tem/${id}`);
    } catch (error) {
      console.error(
        `Error fetching barcode data Số lần - ${scanAttempts} - code ${id} :`,
        error,
      );
      throw error;
    }
  };
  const prefixPattern = Object.values(prefixes).join('|');

  const extractIdFromBarcode = rawBarcode => {
    const regex = new RegExp(`^0*(${prefixPattern})(\\d+)`);
    const matches = rawBarcode.match(regex);
    if (matches && matches.length > 2) {
      const id = matches[1] + matches[2];
      return id.replace(/\d$/, '');
    }
    return null;
  };

  const handleCodeScanned = useCallback(
    async codes => {
      if (isProcessing || codes.length === 0) {
        return;
      }
      setIsProcessing(true);

      const foundCode = codes.find(code =>
        ['ean-13', 'qr', 'upc-a'].includes(code.type),
      );

      if (foundCode) {
        let id;
        if (foundCode.type === 'qr') {
          const matches = foundCode.value.match(/\?id=([^&]+)/);
          id = matches ? decodeURIComponent(matches[1]) : null;
        } else {
          const rawBarcode = foundCode.value;
          id = extractIdFromBarcode(rawBarcode);
        }

        if (!id) {
          return;
        }
        console.log('barcode', id);

        const retryFetchData = async () => {
          try {
            const temData = await callBarcodeAPI(id).then(res => res.data);
            console.log('temData', temData);
            setIsActive(false);
            onComplete(temData);
          } catch (error) {
            if (scanAttempts < 2) {
              setScanAttempts(scanAttempts + 1);
              setIsProcessing(false);
            } else {
              setShowErrorModal(true);
            }
          }
        };

        await retryFetchData();
      } else {
        setIsProcessing(false);
      }
    },
    [onComplete, prefixes, isProcessing, scanAttempts],
  );
  const resetScanner = () => {
    setScanAttempts(0);
    setIsProcessing(false);
    setShowErrorModal(false);
    setIsActive(true);
  };
  const codeScanner = useCodeScanner({
    codeTypes: ['qr', 'ean-13', 'upc-a'],
    onCodeScanned: handleCodeScanned,
  });

  const closeModal = () => {
    setShowErrorModal(false);
    setIsProcessing(false);
    setIsActive(false);
    onComplete(null);
  };

  const handleCameraError = error => {
    console.error('Camera Error:', error);
    // Alert.alert('Camera Error', error.message);
  };

  return (
    <Sys.Container hasHeader hasFooter>
      <Sys.Header style={styles.header} title="Quay lại" onBack={closeModal} />
      <View style={styles.overlayText}>
        <Sys.Text style={styles.headerText}>Quét mã</Sys.Text>
        <Sys.Text style={styles.subHeaderText}>
          Hướng khung camera vào mã trang thiết bị để quét
        </Sys.Text>
      </View>
      <View style={styles.container}>
        <Camera
          device={device}
          isActive={isActive}
          codeScanner={codeScanner}
          style={styles.camera}
          onError={handleCameraError}
        />
        <View style={styles.containerOverlay}>
          <View style={[styles.overlay, {height: overlayHeight}]} />
          <View style={styles.horizontalContainer}>
            <View
              style={[styles.overlay, {width: overlayWidth, height: '100%'}]}
            />
            <View style={[styles.scanArea, {height: scanAreaHeight}]}>
              <ANT.View
                animation="slideInDown"
                iterationCount="infinite"
                direction="alternate"
                duration={2500}
                easing="ease-in-out"
                style={styles.scannerBar}
              />
            </View>
            <View
              style={[styles.overlay, {width: overlayWidth, height: '100%'}]}
            />
          </View>
          <View style={[styles.overlay, {height: overlayHeight}]} />
        </View>
      </View>

      <Modal
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        coverScreen={false}
        isVisible={showErrorModal}
        backButtonClose={true}
        swipeDirection="down">
        <View style={styles.modalContent}>
          <Sys.Text style={styles.modalText}>
            Không thể tìm thấy thông tin mã TTB phù hợp.
          </Sys.Text>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-evenly',
              width: '100%',
            }}>
            <Sys.Button onPress={resetScanner} style={{width: 100}}>
              Quét lại
            </Sys.Button>
            <Sys.Button
              onPress={closeModal}
              style={{width: 100, backgroundColor: '#817878'}}>
              Quay lại
            </Sys.Button>
          </View>
        </View>
      </Modal>
    </Sys.Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
  },
  header: {
    position: 'absolute',
    width: '100%',
    zIndex: 1,
    backgroundColor: 'transparent',
  },
  horizontalContainer: {
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
  },
  camera: {
    flex: 1,
    flexDirection: 'column',
  },
  containerOverlay: {
    ...StyleSheet.absoluteFillObject,
  },
  overlay: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  scanArea: {
    flex: 1,
    backgroundColor: 'transparent',
    borderColor: 'rgba(236,80,80,0.7)',
    borderWidth: 2,
    position: 'relative', // Cho phép định vị các thành phần bên trong
  },
  scannerBar: {
    position: 'absolute',
    top: overlayHeight - scanAreaHeight / 10,
    left: '10%',
    width: '80%',
    height: 4,
    backgroundColor: 'rgba(236,40,40,0.7)',
    opacity: 0.8,
  },
  overlayText: {
    position: 'absolute',
    top: overlayHeight - 120,
    left: overlayWidth,
    right: overlayWidth,
    alignItems: 'flex-start',
    zIndex: 1,
  },
  headerText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
  },
  subHeaderText: {
    marginTop: 10,
    color: '#fff',
    fontSize: 12,
  },
  modalContent: {
    height: scanAreaHeight,
    backgroundColor: 'white',
    paddingVertical: 50,
    paddingHorizontal: 20,
    flexDirection: 'column',
    justifyContent: 'space-around',
    alignItems: 'center',
    borderRadius: 4,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalText: {
    fontSize: 16,
    marginBottom: 10,
  },
});

export default ScanCodeScreen;
