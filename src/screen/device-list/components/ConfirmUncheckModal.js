// components/ConfirmUncheckModal.js
import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {useSelector} from 'react-redux';
import Modal from 'react-native-modal';
import Sys from '../../../components/Sys';
import CloseIcon from '../../report/icons/CloseIcon';
import Divider from './Divider';

const ConfirmUncheckModal = ({isVisible, onConfirm, onClose}) => {
  const currentItem = useSelector(state => state.inventory.currentItem);

  const getMessage = () => {
    if (currentItem.isNew) {
      return 'Bạn có chắc chắn muốn xóa bản ghi mới này không?';
    }
    if (currentItem.statusData) {
      return 'Bạn có chắc chắn muốn bỏ chọn mục này và reset báo cáo trạng thái không?';
    }
    if (currentItem.isMissing) {
      return 'Bạn có chắc chắn muốn bỏ chọn mục này và reset tình trạng thiếu không?';
    }
    if (currentItem.tagChanged) {
      return 'Bạn có chắc chắn muốn bỏ chọn mục này và reset đổi tem không?';
    }
    return 'Bạn có chắc chắn muốn bỏ chọn mục này không?';
  };

  return currentItem ? (
    <Modal
      animationIn={'fadeInUp'}
      animationOut={'fadeOutDown'}
      style={styles.modalStyle}
      isVisible={isVisible}
      onSwipeComplete={onClose}
      swipeDirection="down">
      <View style={styles.modalContent}>
        <View style={styles.header}>
          <Sys.Text style={styles.modalText}>Xác nhận hành động</Sys.Text>
          <TouchableOpacity onPress={onClose}>
            <CloseIcon />
          </TouchableOpacity>
        </View>
        <Divider />
        <Sys.Text style={styles.contentText}>{getMessage()}</Sys.Text>
        <View style={styles.modalButtonContainer}>
          <Sys.Button onPress={onConfirm} style={styles.modalButton}>
            Đồng ý
          </Sys.Button>
          <Sys.Button
            onPress={onClose}
            style={[styles.modalButton, styles.cancelButton]}>
            Hủy
          </Sys.Button>
        </View>
      </View>
    </Modal>
  ) : null;
};

const styles = StyleSheet.create({
  modalStyle: {
    padding: 0,
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalText: {
    marginBottom: 10,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
  },
  contentText: {
    paddingVertical: 30,
    textAlign: 'center',
    fontSize: 14,
    marginBottom: 20,
  },
  modalButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    width: '100%',
  },
  modalButton: {
    width: 100,
  },
  cancelButton: {
    backgroundColor: 'gray',
  },
});

export default ConfirmUncheckModal;
