import React, {useEffect, useState} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Modal from 'react-native-modal';
import Sys from '../../../components/Sys';
import CloseIcon from '../../report/icons/CloseIcon';
import {useDispatch, useSelector} from 'react-redux';
import SelectIcon from '../../sale-location/icons/SelectIcon';
import Divider from './Divider';
import CameraIcon from '../../conform-checkin/icons/CameraIcon';
import {useNavigation} from '@react-navigation/native';
import {
  fetchInitPosDetailStart,
  handleModalHide,
  setIsStatusModalVisible,
  setIsTypeModalVisible,
  setStatusUpdateModalVisible,
} from '../services/inventory.slice';
import {AccountSelectors} from '../../account/services/account.slice';
import {isIOS} from '../../../services/util';

const {width, height} = Dimensions.get('window');

const StatusUpdateModal = React.memo(({item, onAction}) => {
  const {
    equipmentType,
    equipmentStatus,
    isTypeModalVisible,
    isStatusModalVisible,
    isStatusUpdateModalVisible,
    screenParent,
    mode,
  } = useSelector(state => state.inventory);
  console.log(111, item);
  const isPos = useSelector(AccountSelectors.isPos);
  const posCode = useSelector(AccountSelectors.profile)?.owner_pos?.posCode;

  const dispatch = useDispatch();
  const [selectedType, setSelectedType] = useState(
    item?.statusData?.status_type || '',
  );
  const [selectedStatus, setSelectedStatus] = useState(
    item?.statusData?.status_id || '',
  );
  const [note, setNote] = useState(item?.statusData?.note || '');
  const [photo, setPhoto] = useState(item?.statusData?.photo || '');
  const [quantity, setQuantity] = useState(
    item?.statusData?.quantity || item?.quantity || '1',
  );
  const {navigate} = useNavigation();

  useEffect(() => {
    setNote(item?.statusData?.note);
    setPhoto(item?.statusData?.photo);
    setSelectedStatus(item?.statusData?.status_id);
    setSelectedType(item?.statusData?.status_type);
    setQuantity(item?.statusData?.quantity || item?.quantity || '1');
  }, [item]);

  const callbackSave = data => {
    if (data.success) {
      setSelectedType(null);
      setSelectedType(null);
      setNote(null);
      setPhoto(null);
      setQuantity('1');

      if (posCode) {
        dispatch(fetchInitPosDetailStart({posCode, mode: 2}));
      }
    }
    /*else {
      dispatch(
        setDetailedViewModalVisible({
          show: false,
          nextModal: 'isStatusUpdateModalVisible',
        }),
      );
    }*/
  };

  const handleUpdateStatus = (action = true) => {
    const data = {
      itemId: item.id,
      warehouse_id: item.warehouse_id,
      product_id: item.product_id,
      tem_id: item?.tem?.id || null,
      item_id: item?.item_id || null,
      status_id: selectedStatus,
      note: note,
      quantity: shouldShowQuantityField() ? parseInt(quantity, 10) || 1 : undefined,
      statusData: {
        status_id: selectedStatus,
        status_type: selectedType,
        photo,
        note,
        quantity: shouldShowQuantityField() ? quantity : undefined,
      },
    };
    onAction({type: 'REPORT', action, data}, callbackSave);
    dispatch(
      setStatusUpdateModalVisible({
        show: false,
        nextModal: 'isDetailedViewModalVisible',
      }),
    );
  };

  const openCamera = () => {
    dispatch(
      setStatusUpdateModalVisible({
        show: false,
      }),
    );
    navigate('CameraScreen', {
      onCompleteCamera: _photo => {
        setPhoto(_photo);
        navigate(screenParent);
        dispatch(
          setStatusUpdateModalVisible({
            show: true,
          }),
        );
      },
    });
  };

  const shouldShowQuantityField = () => {
    // Show quantity field for Vietlott devices (supplier_type_id: 1) without stamps (hasTem: false)
    return item?.supplier_type_id === 1 && item?.product?.hasTem === false;
  };

  const isQuantityValid = () => {
    if (!shouldShowQuantityField()) return true;
    const numQuantity = parseInt(quantity, 10);
    return quantity && numQuantity > 0 && numQuantity <= (item.qty || 0);
  };

  const isFormValid = (state = false) => {
    const baseValid = state ? item.isStatus : selectedType && selectedStatus;
    // If quantity field is shown, also validate quantity
    if (shouldShowQuantityField() && !state) {
      return baseValid && isQuantityValid();
    }
    return baseValid;
  };
  const isDisabled = mode === 3;

  return (
    <View>
      <Modal
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={styles.modalStyle}
        isVisible={isStatusUpdateModalVisible}
        avoidKeyboard={true}
        onSwipeComplete={() => {
          dispatch(
            setStatusUpdateModalVisible({
              show: false,
              nextModal: 'isDetailedViewModalVisible',
            }),
          );
        }}
        onModalHide={() => dispatch(handleModalHide())}
        swipeDirection="down">
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Sys.Text style={styles.modalText}>
              Cập nhật trạng thái thiết bị
            </Sys.Text>
            <TouchableOpacity
              onPress={() => {
                dispatch(
                  setStatusUpdateModalVisible({
                    show: false,
                    nextModal: 'isDetailedViewModalVisible',
                  }),
                );
              }}>
              <CloseIcon />
            </TouchableOpacity>
          </View>
          <Divider />
          <View style={{paddingVertical: 5}}>
            <Sys.Text style={styles.detailText}>Tên trang thiết bị </Sys.Text>
            <Sys.Text style={styles.titleText}>{item.name} </Sys.Text>
          </View>
          <Divider />
          <View style={{paddingVertical: 5}}>
            <View style={{flexDirection: 'row', marginBottom: 10}}>
              <Sys.Text style={styles.detailText}>
                Trạng thái trang thiết bị
              </Sys.Text>
              <Sys.Text style={{color: 'red'}}> (*)</Sys.Text>
            </View>
            <TouchableOpacity
              onPress={() =>
                dispatch(
                  setStatusUpdateModalVisible({
                    show: false,
                    nextModal: 'isTypeModalVisible',
                  }),
                )
              }
              disabled={isDisabled}
              style={[styles.selector, isDisabled && styles.buttonDisabled]}>
              <Sys.Text style={{color: selectedType ? 'black' : '#D1D1D1'}}>
                {equipmentType[selectedType] || 'Chọn trạng thái'}
              </Sys.Text>
              <View style={{position: 'absolute', right: 10, top: 10}}>
                <SelectIcon />
              </View>
            </TouchableOpacity>
          </View>
          <Divider />

          <View style={{paddingVertical: 5}}>
            <View style={{flexDirection: 'row', marginBottom: 10}}>
              <Sys.Text style={styles.detailText}>Tình trạng</Sys.Text>
              <Sys.Text style={{color: 'red'}}> (*)</Sys.Text>
            </View>
            <TouchableOpacity
              onPress={() =>
                dispatch(
                  setStatusUpdateModalVisible({
                    show: false,
                    nextModal: 'isStatusModalVisible',
                  }),
                )
              }
              disabled={isDisabled || !selectedType}
              style={[styles.selector, isDisabled && styles.buttonDisabled]}>
              <Sys.Text style={{color: selectedStatus ? 'black' : '#D1D1D1'}}>
                {equipmentStatus[selectedType]?.find(
                  status => status.id === selectedStatus,
                )?.name || 'Chọn tình trạng'}
              </Sys.Text>
              <View style={{position: 'absolute', right: 10, top: 10}}>
                <SelectIcon />
              </View>
            </TouchableOpacity>
          </View>
          <Divider />

          {/* Quantity field for Vietlott devices without stamps */}
          {shouldShowQuantityField() && (
            <>
              <View style={{paddingVertical: 5}}>
                <View style={{flexDirection: 'row', marginBottom: 10, justifyContent: 'space-between', alignItems: 'center'}}>
                  <View style={{flexDirection: 'row'}}>
                    <Sys.Text style={styles.detailText}>Số lượng</Sys.Text>
                    <Sys.Text style={{color: 'red'}}> (*)</Sys.Text>
                  </View>
                  <Sys.Text style={styles.availableQuantityText}>
                    Có sẵn: {item.qty || 0}
                  </Sys.Text>
                </View>
                <Sys.Input
                  style={[
                    styles.quantityInput,
                    !isQuantityValid() && quantity && styles.quantityInputError
                  ]}
                  placeholder="Nhập số lượng"
                  value={quantity}
                  onChangeText={isDisabled ? null : setQuantity}
                  editable={!isDisabled}
                  keyboardType="numeric"
                />
                {!isQuantityValid() && quantity && (
                  <Sys.Text style={styles.errorText}>
                    Số lượng không được vượt quá {item.qty || 0}
                  </Sys.Text>
                )}
              </View>
              <Divider />
            </>
          )}

          <Sys.Input
            style={styles.input}
            placeholder="Ghi chú"
            value={note}
            onChangeText={isDisabled ? null : setNote}
            editable={!isDisabled}
          />
          <Divider />
          <View
            style={{
              paddingVertical: 5,
              flexDirection: 'row',
              justifyContent: mode === 3 ? 'flex-start' : 'space-evenly',
              marginVertical: 5,
              alignItems: 'center',
            }}>
            {mode === 3 && photo && (
              <Image
                style={{width: 50, height: 50, borderRadius: 10}}
                source={{uri: photo}}
              />
            )}
            {!isDisabled && (
              <TouchableOpacity
                style={{flexBasis: 'auto', flexGrow: 1}}
                onPress={openCamera}>
                {photo ? (
                  <Image
                    style={{width: 50, height: 50, borderRadius: 10}}
                    source={{uri: photo.uri}}
                  />
                ) : (
                  <CameraIcon />
                )}
              </TouchableOpacity>
            )}

            {!isDisabled && (
              <View
                style={{
                  flexBasis: 'auto',
                  flexShrink: 0,
                  flexGrow: 1,
                  paddingVertical: 5,
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                  marginVertical: 5,
                }}>
                {!isPos && mode !== 2 && (
                  <TouchableOpacity
                    style={[
                      styles.buttonDisabled,
                      isFormValid(true) && styles.buttonDefault,
                    ]}
                    onPress={() => handleUpdateStatus(false)}
                    disabled={!isFormValid(true)}>
                    <Sys.Text style={styles.buttonText}>Hủy</Sys.Text>
                  </TouchableOpacity>
                )}

                <TouchableOpacity
                  style={[
                    styles.button,
                    !isFormValid() && styles.buttonDisabled,
                    {marginLeft: 10},
                  ]}
                  onPress={() => handleUpdateStatus(true)}
                  disabled={!isFormValid()}>
                  <Sys.Text style={styles.buttonText}>Cập nhật</Sys.Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </Modal>
      {/*Modal type*/}
      <Modal
        propagateSwipe={true}
        isVisible={isTypeModalVisible}
        onSwipeComplete={() =>
          dispatch(
            setIsTypeModalVisible({
              show: false,
              nextModal: 'isStatusUpdateModalVisible',
            }),
          )
        }
        onBackdropPress={() =>
          dispatch(
            setIsTypeModalVisible({
              show: false,
              nextModal: 'isStatusUpdateModalVisible',
            }),
          )
        }
        onModalHide={() => dispatch(handleModalHide())}
        swipeDirection="down"
        style={styles.modalStyle}>
        <View style={[styles.modalContent, {paddingVertical: isIOS ? 60 : 20}]}>
          <FlatList
            data={Object.entries(equipmentType)}
            keyExtractor={([key, value]) => key}
            renderItem={({item: [key, value]}) => (
              <TouchableOpacity
                onPress={() => {
                  setSelectedType(key);
                  dispatch(
                    setIsTypeModalVisible({
                      show: false,
                      nextModal: 'isStatusUpdateModalVisible',
                    }),
                  );
                }}
                style={styles.modalItem}>
                <Sys.Text>{value}</Sys.Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </Modal>

      {/* Modal for condition status selection */}
      <Modal
        isVisible={isStatusModalVisible}
        onSwipeComplete={() =>
          dispatch(
            setIsStatusModalVisible({
              show: false,
              nextModal: 'isStatusUpdateModalVisible',
            }),
          )
        }
        onBackdropPress={() =>
          dispatch(
            setIsStatusModalVisible({
              show: false,
              nextModal: 'isStatusUpdateModalVisible',
            }),
          )
        }
        onModalHide={() => dispatch(handleModalHide())}
        propagateSwipe={true}
        swipeDirection="down"
        style={styles.modalStyle}>
        <View style={[styles.modalContent, {paddingVertical: isIOS ? 60 : 20}]}>
          <FlatList
            data={equipmentStatus[selectedType] || []}
            keyExtractor={item => item.id.toString()}
            renderItem={({item}) => (
              <TouchableOpacity
                onPress={() => {
                  setSelectedStatus(item.id);
                  dispatch(
                    setIsStatusModalVisible({
                      show: false,
                      nextModal: 'isStatusUpdateModalVisible',
                    }),
                  );
                }}
                style={styles.modalItem}>
                <Sys.Text>{item.name}</Sys.Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </Modal>
    </View>
  );
});

export default StatusUpdateModal;

const styles = StyleSheet.create({
  titleText: {
    fontWeight: 'bold',
    marginTop: 5,
  },
  detailText: {
    fontSize: 12,
  },
  modalStyle: {
    padding: 0,
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalText: {
    marginBottom: 10,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
  },
  input: {
    marginBottom: 10,
    height: height * 0.1,
  },
  quantityInput: {
    marginBottom: 10,
    height: 50,
    borderWidth: 1,
    borderColor: '#D1D1D1',
    borderRadius: 8,
    paddingHorizontal: 15,
    backgroundColor: 'white',
  },
  quantityInputError: {
    borderColor: '#FF4444',
    backgroundColor: '#FFF5F5',
  },
  availableQuantityText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  errorText: {
    fontSize: 12,
    color: '#FF4444',
    marginTop: -5,
    marginBottom: 5,
    fontWeight: '500',
  },
  divider: {
    width: '100%',
    borderBottomWidth: 1,
    borderColor: 'rgba(129,120,120,0.17)',
    marginVertical: 5,
  },

  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  label: {
    marginBottom: 5,
    textTransform: 'uppercase',
    fontWeight: '600',
    color: '#44444F',
  },
  selector: {
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D1D1',
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  modalItem: {
    padding: 10,
    marginBottom: 5,
    backgroundColor: '#d1d1d110',
    borderRadius: 10,
  },
  button: {
    backgroundColor: '#0062FF',
    padding: 10,
    paddingHorizontal: 12,
    borderRadius: 5,
    marginBottom: 10,
  },
  buttonDefault: {
    backgroundColor: 'red',
    padding: 10,
    paddingHorizontal: 12,
    borderRadius: 5,
    marginBottom: 10,
  },
  buttonDisabled: {
    padding: 10,
    paddingHorizontal: 12,
    borderRadius: 5,
    marginBottom: 10,
    backgroundColor: '#dad8d8',
  },
});
