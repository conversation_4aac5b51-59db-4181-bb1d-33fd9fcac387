import React from 'react';
import {Dimensions, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import Sys from '../../../components/Sys';
import CloseIcon from '../../report/icons/CloseIcon';
import {useDispatch, useSelector} from 'react-redux';
import {setCurrentTem, setTemModalVisible} from '../services/inventory.slice';

const {width} = Dimensions.get('window');

const TemInfoModal = () => {
  const dispatch = useDispatch();
  const {currentTem, isTemModalVisible} = useSelector(state => state.inventory);
  const handleClose = () => {
    dispatch(setTemModalVisible(false));
    dispatch(setCurrentTem(null));
  };

  return currentTem ? (
    <Modal
      isVisible={isTemModalVisible}
      animationIn="fadeInUp"
      animationOut="fadeOutDown"
      style={styles.modalStyle}
      onSwipeComplete={handleClose}
      swipeDirection={['down']}
      backdropTransitionOutTiming={0}>
      <View style={styles.modalContent}>
        <View style={styles.header}>
          <Sys.Text style={styles.modalText}>Thông tin Tem</Sys.Text>
          <TouchableOpacity onPress={handleClose}>
            <CloseIcon />
          </TouchableOpacity>
        </View>
        <View style={styles.infoContainer}>
          <Sys.Text style={styles.label}>Mã Tem:</Sys.Text>
          <Sys.Text style={styles.detailText}>{currentTem.tem_code}</Sys.Text>
        </View>
        <View style={styles.infoContainer}>
          <Sys.Text style={styles.label}>Trạng Thái:</Sys.Text>
          <Sys.Text style={styles.detailText}>
            {currentTem.status_label}
          </Sys.Text>
        </View>
        {currentTem.item && (
          <>
            <View style={styles.infoContainer}>
              <Sys.Text style={styles.label}>Tên Trang Thiết Bị:</Sys.Text>
              <Sys.Text style={styles.detailText}>
                {currentTem.icurrentTem.name}
              </Sys.Text>
            </View>
            <View style={styles.infoContainer}>
              <Sys.Text style={styles.label}>Mã Trang Thiết Bị:</Sys.Text>
              <Sys.Text style={styles.detailText}>
                {currentTem.item.tem_code}
              </Sys.Text>
            </View>
          </>
        )}
      </View>
    </Modal>
  ) : null;
};

const styles = StyleSheet.create({
  modalStyle: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  infoContainer: {
    paddingVertical: 10,
  },
  label: {
    fontWeight: 'bold',
  },
  detailText: {
    fontSize: 16,
    color: 'gray',
  },
});

export default TemInfoModal;
