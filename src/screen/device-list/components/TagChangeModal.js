import React, {useEffect, useState} from 'react';
import {
  Alert,
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Modal from 'react-native-modal';
import Sys from '../../../components/Sys';
import CloseIcon from '../../report/icons/CloseIcon';
import {useNavigation} from '@react-navigation/native';
import {Calendar} from 'react-native-calendars';
import BarcodeIcon2 from '../icons/BarcodeIcon2';
import CalenderIcon2 from '../icons/CalenderIcon2';
import Divider from './Divider';
import moment from 'moment';
import {
  handleModalHide,
  setTagChangeModalVisible,
  setTagDateModalVisible,
  updateItemInSectionsFull,
} from '../services/inventory.slice';
import {useDispatch, useSelector} from 'react-redux';
import {AccountSelectors} from '../../account/services/account.slice';

const {width, height} = Dimensions.get('window');

const TagChangeModal = React.memo(({item, onAction}) => {
  const [oldTagData, setOldTagData] = useState();
  const [newTagData, setNewTagData] = useState();
  const [date, setDate] = useState(item?.tagData?.date || '');
  const [note, setNote] = useState(item?.tagData?.note || '');
  const dispatch = useDispatch();
  const {
    isTagChangeModalVisible,
    isTagDateModalVisible,
    screenParent,
    mode,
    sectionsFull,
  } = useSelector(state => state.inventory);
  const {navigate} = useNavigation();
  const isPos = useSelector(AccountSelectors.isPos);

  useEffect(() => {
    setDate(item?.tagData?.date);
    setNewTagData(item?.tagData?.newTagData);
    setOldTagData(item?.tagData?.oldTagData || item?.tem);
    setNote(item?.tagData?.note);
  }, [item]);

  const callbackSave = data => {
    if (data.success) {
      setNewTagData(null);
      setDate(null);
      setNote(null);
      if (data?.data) {
        dispatch(updateItemInSectionsFull(data?.data));
      }
    }
    /*else {
      dispatch(
        setDetailedViewModalVisible({
          show: false,
          nextModal: 'isTagChangeModalVisible',
        }),
      );
    }*/
  };

  const handleChangeTag = (action = true) => {
    const data = {
      itemId: item.id,
      item_id: item?.item_id || null,
      warehouse_id: item.warehouse_id,
      product_id: item.product_id,
      old_tem_id: oldTagData?.id,
      new_tem_id: newTagData?.id,
      date_assigned: date,
      note,
      tagData: {
        oldTag: oldTagData?.tem_code,
        oldTagData,
        newTag: newTagData?.tem_code,
        newTagData,
        date,
        note,
      },
    };
    onAction({type: 'CHANGE_TAG', action, data}, callbackSave);
    dispatch(
      setTagChangeModalVisible({
        show: false,
        nextModal: 'isDetailedViewModalVisible',
      }),
    );
  };

  const openScanCodeScreen = setTag => {
    dispatch(
      setTagChangeModalVisible({
        show: false,
      }),
    );
    navigate('ScanCodeScreen', {
      onComplete: tem => {
        navigate(screenParent);
        if (!tem) {
          dispatch(
            setTagChangeModalVisible({
              show: true,
            }),
          );
          return;
        }
        if (tem?.status_id === 1 && tem?.tem_code) {
          setTag(tem);
          dispatch(
            setTagChangeModalVisible({
              show: true,
            }),
          );
        } else {
          Alert.alert(
            'Thông báo không hợp lệ',
            `Tem bạn vừa quét hiện ${tem.status_label}.`,
            [
              {
                text: 'Đóng',
                onPress: () =>
                  dispatch(
                    setTagChangeModalVisible({
                      show: true,
                    }),
                  ),
              },
            ],
          );
        }
      },
    });
  };

  const handleDatePicked = day => {
    setDate(day.dateString);
    dispatch(
      setTagDateModalVisible({
        show: false,
        nextModal: 'isTagChangeModalVisible',
      }),
    );
  };

  const isFormValid = (state = false) => {
    return state ? item.tagChanged : newTagData?.tem_code && date;
  };
  const isDisabled = mode === 3;
  return (
    <View>
      <Modal
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={styles.modalStyle}
        avoidKeyboard={true}
        isVisible={isTagChangeModalVisible}
        onSwipeComplete={() => {
          dispatch(
            setTagChangeModalVisible({
              show: false,
              nextModal: 'isDetailedViewModalVisible',
            }),
          );
        }}
        onModalHide={() => dispatch(handleModalHide())}
        swipeDirection="down">
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Sys.Text style={styles.modalText}>
              {oldTagData?.tem_code ? 'Đổi tem TTB' : 'Dán tem mới cho TTB'}
            </Sys.Text>
            <TouchableOpacity
              onPress={() => {
                dispatch(
                  setTagChangeModalVisible({
                    show: false,
                    nextModal: 'isDetailedViewModalVisible',
                  }),
                );
              }}>
              <CloseIcon />
            </TouchableOpacity>
          </View>
          <Divider />
          <View style={{paddingVertical: 5}}>
            <Sys.Text style={styles.detailText}>Tên trang thiết bị </Sys.Text>
            <Sys.Text style={styles.titleText}>{item.name} </Sys.Text>
          </View>
          <Divider />
          {oldTagData?.tem_code && (
            <>
              <View style={{paddingVertical: 10, flexDirection: 'row'}}>
                <View
                  style={{
                    flexDirection: 'row',
                    flexBasis: 200,
                    alignItems: 'center',
                  }}>
                  <Sys.Text style={styles.detailText}>Mã cũ</Sys.Text>
                  <Sys.Text style={{color: 'red'}}> (*) </Sys.Text>
                  <Sys.Text style={styles.titleText}>
                    {oldTagData?.tem_code || null}
                  </Sys.Text>
                </View>
                {/*    <TouchableOpacity
                  onPress={() => openScanCodeScreen(setOldTagData)}
                  disabled={true}
                  style={styles.iconButton}>
                  <BarcodeIcon2 />
                </TouchableOpacity>*/}
              </View>
              <Divider />
            </>
          )}

          <View style={{paddingVertical: 10, flexDirection: 'row'}}>
            <View
              style={{
                flexDirection: 'row',
                flexBasis: 200,
                alignItems: 'center',
              }}>
              <Sys.Text style={styles.detailText}>Mã mới</Sys.Text>
              <Sys.Text style={{color: 'red'}}> (*) </Sys.Text>
              <Sys.Text style={styles.titleText}>
                {' '}
                {newTagData?.tem_code || null}
              </Sys.Text>
            </View>
            <TouchableOpacity
              onPress={() => openScanCodeScreen(setNewTagData)}
              disabled={isDisabled}
              style={styles.iconButton}>
              <BarcodeIcon2 />
            </TouchableOpacity>
          </View>
          <Divider />

          <View style={{paddingVertical: 10, flexDirection: 'row'}}>
            <View
              style={{
                flexDirection: 'row',
                flexBasis: 200,
                alignItems: 'center',
              }}>
              <Sys.Text style={styles.detailText}>Thời điểm dán</Sys.Text>
              <Sys.Text style={{color: 'red'}}> (*) </Sys.Text>
              <Sys.Text style={styles.titleText}>
                {date ? moment(date).format('DD/MM/YYYY') : ''}
              </Sys.Text>
            </View>
            <TouchableOpacity
              onPress={() =>
                dispatch(
                  setTagChangeModalVisible({
                    show: false,
                    nextModal: 'isTagDateModalVisible',
                  }),
                )
              }
              disabled={isDisabled}
              style={styles.iconButton}>
              <CalenderIcon2 />
            </TouchableOpacity>
          </View>
          <Divider />
          <Sys.Input
            style={styles.input}
            placeholder="Nhập nội dung cần ghi chú"
            value={note}
            onChangeText={isDisabled ? null : setNote}
            editable={!isDisabled}
          />
          <Divider />
          {!isDisabled && (
            <View
              style={{
                flexBasis: 'auto',
                flexShrink: 0,
                flexGrow: 1,
                paddingVertical: 5,
                flexDirection: 'row',
                justifyContent: 'flex-end',
                marginVertical: 5,
              }}>
              {!isPos && mode !== 2 && (
                <TouchableOpacity
                  style={[
                    styles.buttonDisabled,
                    isFormValid(true) && styles.buttonDefault,
                  ]}
                  onPress={() => handleChangeTag(false)}
                  disabled={!isFormValid(true)}>
                  <Sys.Text style={styles.buttonText}>Hủy</Sys.Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={[
                  styles.button,
                  !isFormValid() && styles.buttonDisabled,
                  {marginLeft: 10},
                ]}
                onPress={() => handleChangeTag(true)}
                disabled={!isFormValid()}>
                <Sys.Text style={styles.buttonText}>Cập nhật</Sys.Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </Modal>
      <Modal
        style={styles.modalStyle}
        isVisible={isTagDateModalVisible}
        onSwipeComplete={() =>
          dispatch(
            setTagDateModalVisible({
              show: false,
              nextModal: 'isTagChangeModalVisible',
            }),
          )
        }
        onBackdropPress={() =>
          dispatch(
            setTagDateModalVisible({
              show: false,
              nextModal: 'isTagChangeModalVisible',
            }),
          )
        }
        onModalHide={() => dispatch(handleModalHide())}
        swipeDirection="down">
        <View style={styles.datePickerContent}>
          <Sys.Text style={styles.datePickerTitle}>Chọn ngày kết thúc</Sys.Text>
          <Calendar
            markingType={'custom'}
            markedDates={{
              [date]: {
                customStyles: {
                  container: {
                    backgroundColor: 'blue',
                  },
                  text: {
                    color: 'white',
                  },
                },
              },
            }}
            onDayPress={handleDatePicked}
          />
        </View>
      </Modal>
    </View>
  );
});

export default TagChangeModal;

const styles = StyleSheet.create({
  titleText: {
    fontWeight: 'bold',
    marginTop: 5,
  },
  detailText: {
    fontSize: 12,
  },
  modalStyle: {
    padding: 0,
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalText: {
    marginBottom: 10,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
  },
  input: {
    marginBottom: 10,
    width: '100%',
    borderWidth: 1,
    borderColor: '#D1D1D1',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 5,
    height: height * 0.1,
  },
  divider: {
    width: '100%',
    borderBottomWidth: 1,
    borderColor: 'rgba(129,120,120,0.17)',
    marginVertical: 5,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  label: {
    marginBottom: 5,
    textTransform: 'uppercase',
    fontWeight: '600',
    color: '#44444F',
  },
  required: {
    color: 'red',
  },
  row: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 'auto',
  },
  datePickerModal: {
    padding: 0,
    margin: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  datePickerContent: {
    backgroundColor: 'white',
    width: width,
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
  },
  datePickerTitle: {
    fontWeight: '600',
    marginBottom: 10,
  },
  button: {
    backgroundColor: '#0062FF',
    padding: 10,
    paddingHorizontal: 12,
    borderRadius: 5,
    marginBottom: 10,
  },
  buttonDefault: {
    backgroundColor: 'red',
    padding: 10,
    paddingHorizontal: 12,
    borderRadius: 5,
    marginBottom: 10,
  },
  buttonDisabled: {
    padding: 10,
    paddingHorizontal: 12,
    borderRadius: 5,
    marginBottom: 10,
    backgroundColor: '#dad8d8',
  },
});
