// components/ConfirmMismatchModal.js
import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import Sys from '../../../components/Sys';
import CloseIcon from '../../report/icons/CloseIcon';
import {isIOS} from '../../../services/util';

const ConfirmMismatchModal = ({
  isVisible,
  onClose,
  onUpdate,
  currentItem,
  scannedData,
}) => {
  return (
    <Modal
      animationIn={'fadeInUp'}
      animationOut={'fadeOutDown'}
      style={styles.modalStyle}
      isVisible={isVisible}
      onSwipeComplete={onClose}
      swipeDirection="down">
      <View style={styles.modalContent}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            width: '100%',
          }}>
          <Sys.Text style={styles.modalText}><PERSON><PERSON> <PERSON>h<PERSON>n lệch mã TTB</Sys.Text>
          <TouchableOpacity onPress={onClose}>
            <CloseIcon />
          </TouchableOpacity>
        </View>
        <View style={styles.divider} />
        <Sys.Text
          style={{
            fontSize: 10,
            width: '100%',
            textAlign: 'left',
            marginBottom: 10,
          }}>
          Tên trang thiết bị
        </Sys.Text>
        <Sys.Text
          style={{
            fontSize: 14,
            fontWeight: 'bold',
            width: '100%',
            textAlign: 'left',
            marginBottom: 5,
          }}>
          {currentItem.name}
        </Sys.Text>
        <View style={styles.divider} />

        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            width: '100%',
            paddingVertical: 5,
          }}>
          <Sys.Text>Mã TTB sổ sách:</Sys.Text>
          <Sys.Text style={styles.modalText}>{currentItem.tem_code}</Sys.Text>
        </View>
        <View style={styles.divider} />

        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            width: '100%',
            marginBottom: 15,
          }}>
          <Sys.Text>Mã TTB thực tế:</Sys.Text>
          <Sys.Text style={styles.modalText}>{scannedData?.tem_code}</Sys.Text>
        </View>
        <View style={styles.divider} />

        <View style={styles.modalButtonContainer}>
          <Sys.Button onPress={onUpdate} style={styles.modalButton}>
            Cập nhật
          </Sys.Button>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalStyle: {
    padding: 0,
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: isIOS ? 20 : 10,
  },
  modalText: {
    marginBottom: 10,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
  },
  divider: {
    width: '100%',
    borderBottomWidth: 1,
    borderColor: 'rgba(129,120,120,0.17)',
    marginVertical: 5,
  },
  modalButtonContainer: {
    marginTop: 20,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    width: '100%',
  },
  modalButton: {
    width: 100,
  },
});

export default ConfirmMismatchModal;
