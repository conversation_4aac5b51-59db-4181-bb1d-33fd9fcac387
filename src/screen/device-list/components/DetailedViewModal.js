import React, {useState} from 'react';
import {
  Animated,
  Dimensions,
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Modal from 'react-native-modal';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import moment from 'moment/moment';
import StatusUpdateModal from './StatusUpdateModal';
import ReportMissingModal from './ReportMissingModal';
import TagChangeModal from './TagChangeModal';
import {useDispatch, useSelector} from 'react-redux';
import {
  handleModalHide,
  setDetailedViewModalVisible,
} from '../services/inventory.slice';
import TemInfoModal from './TemInfoModal';
import BlinkingBadge from './BlinkingBadge';
import {AccountSelectors} from '../../account/services/account.slice';
import {SUPPLIER_TYPE_VIETLOTT_ID} from '../services/const';
import {useNavigation} from '@react-navigation/native';

const {width, height} = Dimensions.get('window');

const DetailedViewModal = React.memo(({onAction, item, page = undefined}) => {
  const dispatch = useDispatch();
  const {isDetailedViewModalVisible, mode} = useSelector(
    state => state.inventory,
  );
  const isPos = useSelector(AccountSelectors.isPos);

  // Animation states
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const [activeTab, setActiveTab] = useState('details'); // 'details', 'history', 'tags'

  // Initialize animations when modal opens
  React.useEffect(() => {
    if (isDetailedViewModalVisible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(0);
      slideAnim.setValue(50);
    }
  }, [isDetailedViewModalVisible]);

  const canChangeTag =
    mode !== 3
      ? (item?.product?.hasTem || item?.tem_code) && !item?.isMissing
      : item?.tagChanged;

  const canReportMissing = mode !== 3 ? !item?.tagData : item?.isMissing;

  const canStatus = mode !== 3 || item?.isStatus;

  const canSeeChangeTag =
    !isPos &&
    item?.supplier_type_id === SUPPLIER_TYPE_VIETLOTT_ID &&
    (item?.product?.hasTem || item?.tem_code);

  const navigation = useNavigation();

  const isPosWarehouse = item?.warehouse?.type === 'App\\Pos';
  const handleNavigation = () => {
    if (isPosWarehouse && page) {
      dispatch(setDetailedViewModalVisible({show: false}));
      navigation.navigate(page, {
        posCode: item?.pos_id,
      });
    }
  };
  return (
    <>
      {item && (
        <View>
          <Modal
            animationIn={'fadeInUp'}
            animationOut={'fadeOutDown'}
            style={styles.modalStyle}
            isVisible={isDetailedViewModalVisible}
            // onSwipeComplete={() =>
            //   dispatch(setDetailedViewModalVisible({show: false}))
            // }
            propagateSwipe={true}
            // swipeDirection="down"
            onModalHide={() => {
              dispatch(handleModalHide());
            }}>
            <View style={modernStyles.modalContent}>
              {/* Modern Header */}
              <LinearGradient
                colors={['#6366F1', '#4F46E5']}
                style={modernStyles.header}
                start={{x: 0, y: 0}}
                end={{x: 1, y: 0}}>
                <View style={modernStyles.headerLeft}>
                  <MaterialCommunityIcons
                    name="information"
                    size={24}
                    color="white"
                  />
                  <View style={modernStyles.headerTextContainer}>
                    <Sys.Text style={modernStyles.headerTitle}>
                      Chi tiết trang thiết bị
                    </Sys.Text>
                    <Sys.Text style={modernStyles.headerSubtitle}>
                      {item?.name}
                    </Sys.Text>
                  </View>
                </View>
                <TouchableOpacity
                  onPress={() =>
                    dispatch(setDetailedViewModalVisible({show: false}))
                  }
                  style={modernStyles.closeButton}>
                  <MaterialCommunityIcons
                    name="close"
                    size={24}
                    color="white"
                  />
                </TouchableOpacity>
              </LinearGradient>

              {/* Main Info Card */}
              <Animated.View
                style={[
                  modernStyles.mainCard,
                  {
                    opacity: fadeAnim,
                    transform: [{translateY: slideAnim}],
                  },
                ]}>
                <LinearGradient
                  colors={['#ffffff', '#f8f9fa']}
                  style={modernStyles.mainCardGradient}>
                  <View style={modernStyles.imageContainer}>
                    <Image
                      style={modernStyles.deviceImage}
                      source={{uri: item.image}}
                    />
                    <View style={modernStyles.statusBadge}>
                      <MaterialCommunityIcons
                        name={
                          item.status_id === 1 ? 'check-circle' : 'alert-circle'
                        }
                        size={16}
                        color="white"
                      />
                      <Sys.Text style={modernStyles.statusText}>
                        {item.status}
                      </Sys.Text>
                    </View>
                  </View>

                  <View style={modernStyles.infoContainer}>
                    <ModernInfoRow
                      icon="tag"
                      label="Tên thiết bị"
                      value={item.name}
                    />
                    <ModernInfoRow
                      icon="barcode"
                      label="Mã TTB"
                      value={item.tem_code}
                    />
                    <ModernInfoRow
                      icon="code-tags"
                      label="Mã loại TTB"
                      value={item.product.code}
                    />
                  </View>
                </LinearGradient>
              </Animated.View>
              {/* Warning Card */}
              {item.notExit && (
                <Animated.View
                  style={[
                    modernStyles.warningCard,
                    {
                      opacity: fadeAnim,
                      transform: [{translateY: slideAnim}],
                    },
                  ]}>
                  <LinearGradient
                    colors={['#F59E0B', '#D97706']}
                    style={modernStyles.warningGradient}>
                    <MaterialCommunityIcons
                      name="alert-circle"
                      size={24}
                      color="white"
                    />
                    <View style={modernStyles.warningTextContainer}>
                      <Sys.Text style={modernStyles.warningTitle}>
                        Cảnh báo vị trí
                      </Sys.Text>
                      <Sys.Text style={modernStyles.warningText}>
                        Theo sổ sách sản phẩm hiện đang thuộc ĐBH khác có mã là:{' '}
                        {item?.pos_id}
                      </Sys.Text>
                    </View>
                  </LinearGradient>
                </Animated.View>
              )}
              {/* Action Buttons */}
              {!item.notExit && (
                <Animated.View
                  style={[
                    modernStyles.actionsCard,
                    {
                      opacity: fadeAnim,
                      transform: [{translateY: slideAnim}],
                    },
                  ]}>
                  <LinearGradient
                    colors={['#ffffff', '#f8f9fa']}
                    style={modernStyles.actionsGradient}>
                    <Sys.Text style={modernStyles.actionsTitle}>
                      Thao tác
                    </Sys.Text>
                    <View style={modernStyles.actionsContainer}>
                      <ModernActionButton
                        icon="clipboard-check"
                        title="Tình trạng TTB"
                        enabled={canStatus}
                        hasNotification={item.isStatus}
                        gradient={['#10B981', '#059669']}
                        onPress={() =>
                          dispatch(
                            setDetailedViewModalVisible({
                              show: false,
                              nextModal: 'isStatusUpdateModalVisible',
                            }),
                          )
                        }
                      />
                      <ModernActionButton
                        icon="alert-circle"
                        title="Báo thiếu"
                        enabled={canReportMissing}
                        hasNotification={item.isMissing}
                        gradient={['#EF4444', '#DC2626']}
                        onPress={() =>
                          dispatch(
                            setDetailedViewModalVisible({
                              show: false,
                              nextModal: 'isReportMissingModalVisible',
                            }),
                          )
                        }
                      />
                      {canSeeChangeTag && (
                        <ModernActionButton
                          icon="tag"
                          title={item?.tem_code ? 'Đổi tem' : 'Dán tem mới'}
                          enabled={canChangeTag}
                          hasNotification={item.tagChanged}
                          gradient={['#8B5CF6', '#7C3AED']}
                          onPress={() =>
                            dispatch(
                              setDetailedViewModalVisible({
                                show: false,
                                nextModal: 'isTagChangeModalVisible',
                              }),
                            )
                          }
                        />
                      )}
                    </View>
                  </LinearGradient>
                </Animated.View>
              )}
              {/* Tab Navigation */}
              <View style={modernStyles.tabContainer}>
                <TouchableOpacity
                  style={[
                    modernStyles.tab,
                    activeTab === 'details' && modernStyles.activeTab,
                  ]}
                  onPress={() => setActiveTab('details')}>
                  <MaterialCommunityIcons
                    name="information"
                    size={20}
                    color={activeTab === 'details' ? 'white' : THEME.Color.gray}
                  />
                  <Sys.Text
                    style={[
                      modernStyles.tabText,
                      activeTab === 'details' && modernStyles.activeTabText,
                    ]}>
                    Chi tiết
                  </Sys.Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    modernStyles.tab,
                    activeTab === 'history' && modernStyles.activeTab,
                  ]}
                  onPress={() => setActiveTab('history')}>
                  <MaterialCommunityIcons
                    name="history"
                    size={20}
                    color={activeTab === 'history' ? 'white' : THEME.Color.gray}
                  />
                  <Sys.Text
                    style={[
                      modernStyles.tabText,
                      activeTab === 'history' && modernStyles.activeTabText,
                    ]}>
                    Lịch sử
                  </Sys.Text>
                </TouchableOpacity>
                {item?.tem_code && (
                  <TouchableOpacity
                    style={[
                      modernStyles.tab,
                      activeTab === 'tags' && modernStyles.activeTab,
                    ]}
                    onPress={() => setActiveTab('tags')}>
                    <MaterialCommunityIcons
                      name="tag"
                      size={20}
                      color={activeTab === 'tags' ? 'white' : THEME.Color.gray}
                    />
                    <Sys.Text
                      style={[
                        modernStyles.tabText,
                        activeTab === 'tags' && modernStyles.activeTabText,
                      ]}>
                      Tem
                    </Sys.Text>
                  </TouchableOpacity>
                )}
              </View>

              {/* Tab Content */}
              <ScrollView style={modernStyles.contentScrollView}>
                {activeTab === 'details' && (
                  <Animated.View
                    style={[
                      modernStyles.tabContent,
                      {
                        opacity: fadeAnim,
                        transform: [{translateY: slideAnim}],
                      },
                    ]}>
                    <LinearGradient
                      colors={['#ffffff', '#f8f9fa']}
                      style={modernStyles.detailsCard}>
                      <Sys.Text style={modernStyles.sectionTitle}>
                        Thông tin kho
                      </Sys.Text>
                      <ModernInfoRow
                        icon="warehouse"
                        label="Tên kho"
                        value={item?.warehouse?.name}
                        isClickable={isPosWarehouse}
                        onPress={handleNavigation}
                      />
                      <ModernInfoRow
                        icon="tag-outline"
                        label="Loại kho"
                        value={item?.warehouse?.type_label}
                      />
                      {isPosWarehouse && (
                        <ModernInfoRow
                          icon="calendar-check"
                          label="Ngày bàn giao cho chủ điểm"
                          value={moment(item?.handover_date).format(
                            'DD/MM/YYYY',
                          )}
                        />
                      )}
                      <ModernInfoRow
                        icon="calendar-start"
                        label="Ngày đưa vào sử dụng"
                        value={moment(item?.start_use_date).format(
                          'DD/MM/YYYY',
                        )}
                      />
                      {item?.warranty_end_date && (
                        <ModernInfoRow
                          icon="shield-check"
                          label="Ngày hết bảo hành"
                          value={moment(item?.warranty_end_date).format(
                            'DD/MM/YYYY',
                          )}
                        />
                      )}
                    </LinearGradient>
                  </Animated.View>
                )}
                {activeTab === 'history' && (
                  <Animated.View
                    style={[
                      modernStyles.tabContent,
                      {
                        opacity: fadeAnim,
                        transform: [{translateY: slideAnim}],
                      },
                    ]}>
                    <LinearGradient
                      colors={['#ffffff', '#f8f9fa']}
                      style={modernStyles.historyCard}>
                      <View style={modernStyles.sectionHeader}>
                        <MaterialCommunityIcons
                          name="history"
                          size={20}
                          color={THEME.Color.primary}
                        />
                        <Sys.Text style={modernStyles.sectionTitle}>
                          Lịch sử giao dịch
                        </Sys.Text>
                      </View>

                      {item.itemHistory && item.itemHistory.length > 0 ? (
                        item.itemHistory.map((history, index) => (
                          <View key={index} style={modernStyles.historyItem}>
                            <View style={modernStyles.historyItemHeader}>
                              <View style={modernStyles.historyIndex}>
                                <Sys.Text style={modernStyles.historyIndexText}>
                                  {index + 1}
                                </Sys.Text>
                              </View>
                              <View style={modernStyles.historyItemContent}>
                                <Sys.Text style={modernStyles.historyItemTitle}>
                                  {history.name}
                                </Sys.Text>
                                <Sys.Text
                                  style={modernStyles.historyItemSubtitle}>
                                  {history.action_label}
                                </Sys.Text>
                              </View>
                              <View style={modernStyles.historyItemRight}>
                                <Sys.Text style={modernStyles.historyDate}>
                                  {moment(history.actual_date).format(
                                    'DD/MM/YYYY',
                                  )}
                                </Sys.Text>
                                <Sys.Text style={modernStyles.historyCode}>
                                  {history.tem_code}
                                </Sys.Text>
                              </View>
                            </View>
                          </View>
                        ))
                      ) : (
                        <View style={modernStyles.emptyState}>
                          <MaterialCommunityIcons
                            name="history"
                            size={48}
                            color={THEME.Color.grayLight}
                          />
                          <Sys.Text style={modernStyles.emptyStateText}>
                            Chưa có lịch sử giao dịch
                          </Sys.Text>
                        </View>
                      )}
                    </LinearGradient>
                  </Animated.View>
                )}

                {activeTab === 'tags' && item?.tem_code && (
                  <Animated.View
                    style={[
                      modernStyles.tabContent,
                      {
                        opacity: fadeAnim,
                        transform: [{translateY: slideAnim}],
                      },
                    ]}>
                    <LinearGradient
                      colors={['#ffffff', '#f8f9fa']}
                      style={modernStyles.tagsCard}>
                      <View style={modernStyles.sectionHeader}>
                        <MaterialCommunityIcons
                          name="tag"
                          size={20}
                          color={THEME.Color.primary}
                        />
                        <Sys.Text style={modernStyles.sectionTitle}>
                          Lịch sử thay tem
                        </Sys.Text>
                      </View>

                      {item.temHistory && item.temHistory.length > 0 ? (
                        item.temHistory.map((history, index) => (
                          <View key={index} style={modernStyles.tagItem}>
                            <View style={modernStyles.tagItemHeader}>
                              <View style={modernStyles.tagIndex}>
                                <Sys.Text style={modernStyles.tagIndexText}>
                                  {index + 1}
                                </Sys.Text>
                              </View>
                              <View style={modernStyles.tagItemContent}>
                                <View style={modernStyles.tagCodes}>
                                  <View style={modernStyles.tagCodeContainer}>
                                    <Sys.Text style={modernStyles.tagCodeLabel}>
                                      Mã cũ:
                                    </Sys.Text>
                                    <Sys.Text style={modernStyles.tagCodeValue}>
                                      {history?.previous_tem_code || 'N/A'}
                                    </Sys.Text>
                                  </View>
                                  <MaterialCommunityIcons
                                    name="arrow-right"
                                    size={16}
                                    color={THEME.Color.gray}
                                  />
                                  <View style={modernStyles.tagCodeContainer}>
                                    <Sys.Text style={modernStyles.tagCodeLabel}>
                                      Mã mới:
                                    </Sys.Text>
                                    <Sys.Text style={modernStyles.tagCodeValue}>
                                      {history.tem_code}
                                    </Sys.Text>
                                  </View>
                                </View>
                                <View style={modernStyles.tagMeta}>
                                  <Sys.Text style={modernStyles.tagDate}>
                                    {moment(history.actual_date).format(
                                      'DD/MM/YYYY HH:mm:ss',
                                    )}
                                  </Sys.Text>
                                  <View style={modernStyles.tagStatus}>
                                    <Sys.Text
                                      style={modernStyles.tagStatusText}>
                                      {history.tem_status_label}
                                    </Sys.Text>
                                  </View>
                                </View>
                              </View>
                            </View>
                          </View>
                        ))
                      ) : (
                        <View style={modernStyles.emptyState}>
                          <MaterialCommunityIcons
                            name="tag-off"
                            size={48}
                            color={THEME.Color.grayLight}
                          />
                          <Sys.Text style={modernStyles.emptyStateText}>
                            Chưa có lịch sử thay tem
                          </Sys.Text>
                        </View>
                      )}
                    </LinearGradient>
                  </Animated.View>
                )}
              </ScrollView>
            </View>
          </Modal>
          <StatusUpdateModal item={item} onAction={onAction} />
          <ReportMissingModal item={item} onAction={onAction} />
          <TagChangeModal item={item} onAction={onAction} />
        </View>
      )}
      <TemInfoModal />
    </>
  );
});

// Helper Components
const ModernInfoRow = ({icon, label, value, isClickable = false, onPress}) => (
  <View style={modernStyles.infoRow}>
    <MaterialCommunityIcons name={icon} size={16} color={THEME.Color.primary} />
    <Sys.Text style={modernStyles.infoLabel}>{label}:</Sys.Text>
    <TouchableOpacity
      onPress={isClickable ? onPress : undefined}
      disabled={!isClickable}
      style={modernStyles.infoValueContainer}>
      <Sys.Text
        style={[
          modernStyles.infoValue,
          isClickable && modernStyles.infoValueClickable,
        ]}>
        {value}
      </Sys.Text>
    </TouchableOpacity>
  </View>
);

const ModernActionButton = ({
  icon,
  title,
  enabled,
  hasNotification,
  gradient,
  onPress,
}) => {
  const [buttonScale] = useState(new Animated.Value(1));

  const handlePressIn = () => {
    Animated.spring(buttonScale, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(buttonScale, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View
      style={[
        modernStyles.actionButton,
        {
          transform: [{scale: buttonScale}],
          opacity: enabled ? 1 : 0.5,
        },
      ]}>
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={!enabled}
        style={modernStyles.actionButtonTouchable}>
        <LinearGradient
          colors={enabled ? gradient : ['#9CA3AF', '#6B7280']}
          style={modernStyles.actionButtonGradient}>
          <MaterialCommunityIcons name={icon} size={24} color="white" />
          {hasNotification && (
            <View style={modernStyles.notificationBadge}>
              <BlinkingBadge />
            </View>
          )}
        </LinearGradient>
        <Sys.Text style={modernStyles.actionButtonText}>{title}</Sys.Text>
      </TouchableOpacity>
    </Animated.View>
  );
};

export default DetailedViewModal;

// Modern Styles
const modernStyles = StyleSheet.create({
  modalContent: {
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingBottom: 20,
    maxHeight: height * 0.9,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginTop: 2,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Main Card Styles
  mainCard: {
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  mainCardGradient: {
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  imageContainer: {
    alignItems: 'center',
    marginBottom: 20,
    position: 'relative',
  },
  deviceImage: {
    width: 120,
    height: 120,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
  },
  statusBadge: {
    position: 'absolute',
    bottom: -8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#10B981',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginLeft: 4,
  },
  infoContainer: {
    gap: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    borderRadius: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginLeft: 8,
    minWidth: 100,
  },
  infoValueContainer: {
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    textAlign: 'right',
  },
  infoValueClickable: {
    color: THEME.Color.primary,
    textDecorationLine: 'underline',
  },

  // Warning Card Styles
  warningCard: {
    marginHorizontal: 16,
    marginTop: 12,
    borderRadius: 12,
    overflow: 'hidden',
  },
  warningGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  warningTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginBottom: 4,
  },
  warningText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    lineHeight: 20,
  },

  // Actions Card Styles
  actionsCard: {
    marginHorizontal: 16,
    marginTop: 12,
    borderRadius: 16,
    overflow: 'hidden',
  },
  actionsGradient: {
    padding: 20,
  },
  actionsTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginBottom: 16,
    textAlign: 'center',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 8,
  },
  actionButtonTouchable: {
    alignItems: 'center',
    width: '100%',
  },
  actionButtonGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
    marginBottom: 8,
    position: 'relative',
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    textAlign: 'center',
    lineHeight: 16,
  },
  notificationBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
  },

  // Tab Styles
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginTop: 12,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: THEME.Color.primary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginLeft: 6,
  },
  activeTabText: {
    color: 'white',
  },

  // Content Styles
  contentScrollView: {
    marginTop: 12,
    height: height * 0.4, // Fixed height for proper scrolling
  },
  tabContent: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginLeft: 8,
  },
  detailsCard: {
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },

  // History Styles
  historyCard: {
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  historyItem: {
    marginBottom: 12,
    padding: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: THEME.Color.primary,
  },
  historyItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  historyIndex: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: THEME.Color.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  historyIndexText: {
    fontSize: 14,
    fontWeight: '700',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  historyItemContent: {
    flex: 1,
  },
  historyItemTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginBottom: 4,
  },
  historyItemSubtitle: {
    fontSize: 13,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
  },
  historyItemRight: {
    alignItems: 'flex-end',
  },
  historyDate: {
    fontSize: 12,
    fontWeight: '600',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginBottom: 2,
  },
  historyCode: {
    fontSize: 11,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
  },

  // Tags Styles
  tagsCard: {
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  tagItem: {
    marginBottom: 12,
    padding: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#8B5CF6',
  },
  tagItemHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  tagIndex: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#8B5CF6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  tagIndexText: {
    fontSize: 14,
    fontWeight: '700',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  tagItemContent: {
    flex: 1,
  },
  tagCodes: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tagCodeContainer: {
    flex: 1,
  },
  tagCodeLabel: {
    fontSize: 12,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginBottom: 2,
  },
  tagCodeValue: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  tagMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  tagDate: {
    fontSize: 12,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
  },
  tagStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: '#10B981',
    borderRadius: 6,
  },
  tagStatusText: {
    fontSize: 11,
    fontWeight: '600',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },

  // Empty State Styles
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginTop: 12,
    textAlign: 'center',
  },
});

// Legacy styles (keeping for compatibility)
const styles = StyleSheet.create({
  modalStyle: {
    padding: 0,
    margin: 0,
    justifyContent: 'flex-end',
  },
  row: {
    flexDirection: 'row',
  },
  card: {
    marginTop: 10,
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 10,
  },
  col: {
    flexGrow: 1,
    flexBasis: 0,
  },
  col2p3: {
    flexGrow: 2,
    flexBasis: 0,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalText: {
    marginBottom: 10,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
  },
  titleText: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  titleTextDate: {
    marginBottom: 5,
  },
  detailText: {
    marginBottom: 5,
    fontSize: 14,
    fontWeight: 600,
  },
  titleHeadText: {
    color: '#001451',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },

  historyHeadText: {
    color: '#FFFFFF',
    fontSize: 12,
    flex: 1,
    textAlign: 'left',
    minWidth: 50,
  },
  historyText: {
    fontSize: 10,
    flex: 1,
    textAlign: 'left',
    minWidth: 50,
  },

  historyRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 5,
  },
  history: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    backgroundColor: '#0062FF',
    padding: 5,
  },
});
