import React, {useState} from 'react';
import {TouchableOpacity, View} from 'react-native';
import ReactNativeModal from 'react-native-modal';
import Sys from '../../../components/Sys';

const DetailItemModal = ({isVisible, onClose, onSave}) => {
  const [temCode, setTemCode] = useState('');
  const [status, setStatus] = useState('');
  const [quantity, setQuantity] = useState(1);

  const handleSave = () => {
    onSave({
      id: Date.now(),
      typeName: 'New Item', // Update based on input
      tem_code: temCode,
      status,
      quantity,
      realQuantity: quantity,
      isNew: true,
    });
  };

  return (
    <ReactNativeModal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onBackButtonPress={onClose}
      style={{justifyContent: 'center', alignItems: 'center'}}>
      <View
        style={{
          backgroundColor: 'white',
          padding: 20,
          borderRadius: 10,
          width: '80%',
        }}>
        <Sys.Text style={{marginBottom: 10}}>Thêm thiết bị mới</Sys.Text>
        <Sys.Input
          placeholder="Mã TTB"
          value={temCode}
          onChangeText={setTemCode}
          style={{borderBottomWidth: 1, marginBottom: 10}}
        />
        <Sys.Input
          placeholder="Tình trạng"
          value={status}
          onChangeText={setStatus}
          style={{borderBottomWidth: 1, marginBottom: 10}}
        />
        <Sys.Input
          placeholder="Số lượng"
          value={quantity.toString()}
          onChangeText={text => setQuantity(parseInt(text))}
          keyboardType="numeric"
          style={{borderBottomWidth: 1, marginBottom: 20}}
        />
        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          <TouchableOpacity onPress={onClose} style={{padding: 10}}>
            <Sys.Text>Hủy</Sys.Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleSave} style={{padding: 10}}>
            <Sys.Text>Lưu</Sys.Text>
          </TouchableOpacity>
        </View>
      </View>
    </ReactNativeModal>
  );
};

export default DetailItemModal;
