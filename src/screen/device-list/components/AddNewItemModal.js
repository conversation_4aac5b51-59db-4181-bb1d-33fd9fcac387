import React, {useEffect, useState} from 'react';
import {
  Alert,
  Dimensions,
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Modal from 'react-native-modal';
import Sys from '../../../components/Sys';
import CloseIcon from '../../report/icons/CloseIcon';
import {useNavigation} from '@react-navigation/native';
import BarcodeIcon2 from '../icons/BarcodeIcon2';
import Divider from './Divider';
import SelectIcon from '../../sale-location/icons/SelectIcon';
import {useDispatch, useSelector} from 'react-redux';
import {
  handleModalHide,
  setAddNewItemModalVisible,
  setIsProductTypeModalVisible,
  setIsTypeAddNewModalVisible,
} from '../services/inventory.slice';
import CameraIcon from '../../conform-checkin/icons/CameraIcon';

const {width, height} = Dimensions.get('window');

const isTemCodeUnique = (sections, tem_code) => {
  for (const section of sections) {
    for (const group of section.data) {
      if (group.items.some(item => item.tem_code === tem_code)) {
        return false; // `tem_code` already exists
      }
    }
  }
  return true; // `tem_code` is unique
};

const AddNewItemModal = ({onSave, renderKey}) => {
  const dispatch = useDispatch();
  const {sections} = useSelector(state => state.inventory);
  const [temCode, setTemCode] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [note, setNote] = useState('');
  const [product, setProduct] = useState(null);
  const [selectedType, setSelectedType] = useState('');
  const [id, setId] = useState(Date.now());
  const {navigate} = useNavigation();

  const {
    equipmentType,
    typeProducts,
    isAddNewItemModalVisible,
    currentItem,
    isProductTypeModalVisible,
    addNewTypeId,
    isTypeAddNewModalVisible,
    mode,
  } = useSelector(state => state.inventory);
  const [photo, setPhoto] = useState(currentItem?.photo || '');

  const handleSave = () => {
    onSave({
      id,
      name: product.name,
      image: product.image,
      product: product,
      product_id: product.id,
      tem_code: temCode.tem_code,
      tem: temCode,
      status_type: selectedType,
      status_label: equipmentType[selectedType],
      statusData: null,
      qty: 0,
      realQuantity: quantity,
      isNew: true,
      note,
      photo,
    });
    handleClose();
  };

  useEffect(() => {
    if (currentItem?.isNew) {
      setId(currentItem.id);
      setTemCode(currentItem.tem);
      setNote(currentItem?.note);
      setSelectedType(currentItem.status_type);
      setProduct(currentItem.product);
    } else {
      resetState();
    }
  }, [currentItem]);
  const resetState = () => {
    setId(Date.now());
    setProduct(null);
    setTemCode('');
    setNote('');
    setSelectedType('');
    setProduct('');
  };

  const handleClose = () => {
    resetState();
    dispatch(
      setAddNewItemModalVisible({
        show: false,
      }),
    );
  };

  const openCamera = () => {
    dispatch(
      setAddNewItemModalVisible({
        show: false,
      }),
    );
    navigate('CameraScreen', {
      onCompleteCamera: _photo => {
        navigate('DeviceListScreen');
        setPhoto(_photo);
        dispatch(
          setAddNewItemModalVisible({
            show: true,
          }),
        );
      },
    });
  };

  const openScanCodeScreen = setTag => {
    // close modal
    dispatch(
      setAddNewItemModalVisible({
        show: false,
      }),
    );
    navigate('ScanCodeScreen', {
      onComplete: tem => {
        if (tem?.tem_code) {
          const isUnique = isTemCodeUnique(sections, tem.tem_code);
          if (!isUnique) {
            Alert.alert(
              'Thông báo',
              `Mã ${tem.tem_code} đã tồn tại. Vui lòng sử dụng mã duy nhất.`,
              [
                {
                  text: 'Xác nhận',
                  onPress: () => {
                    navigate('DeviceListScreen');
                    dispatch(
                      setAddNewItemModalVisible({
                        show: true,
                      }),
                    );
                  },
                },
              ],
            );
          } else {
            setTag(tem);
            navigate('DeviceListScreen');
            dispatch(
              setAddNewItemModalVisible({
                show: true,
              }),
            );
          }
        } else {
          navigate('DeviceListScreen');
          dispatch(
            setAddNewItemModalVisible({
              show: true,
            }),
          );
        }
      },
    });
  };
  const isDisabled = mode === 3;

  const isFormValid = () => {
    return (
      product?.id &&
      (product?.hasTem ? temCode?.tem_code : true) &&
      selectedType &&
      quantity > 0
    );
  };
  return (
    <View>
      <Modal
        key={renderKey}
        animationIn={'fadeInUp'}
        animationOut={'fadeOutDown'}
        style={styles.modalStyle}
        isVisible={isAddNewItemModalVisible}
        avoidKeyboard={true}
        onSwipeComplete={handleClose}
        onModalHide={() => dispatch(handleModalHide())}
        swipeDirection="down">
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Sys.Text style={styles.modalText}>Thêm thiết bị mới</Sys.Text>
            <TouchableOpacity onPress={handleClose}>
              <CloseIcon />
            </TouchableOpacity>
          </View>
          <Divider />
          <View style={{paddingVertical: 5}}>
            <View style={{flexDirection: 'row', marginBottom: 10}}>
              <Sys.Text style={styles.detailText}>Tên trang thiết bị</Sys.Text>
              <Sys.Text style={{color: 'red'}}> (*)</Sys.Text>
            </View>
            <TouchableOpacity
              onPress={() =>
                dispatch(
                  setAddNewItemModalVisible({
                    show: false,
                    nextModal: 'isProductTypeModalVisible',
                  }),
                )
              }
              disabled={isDisabled}
              style={[styles.selector, isDisabled && styles.buttonDisabled]}>
              <Sys.Text style={{color: product ? 'black' : '#D1D1D1'}}>
                {product?.name || 'Chọn loại TTB'}
              </Sys.Text>
              <View style={{position: 'absolute', right: 10, top: 10}}>
                <SelectIcon />
              </View>
            </TouchableOpacity>
          </View>
          <Divider />
          {product?.hasTem && (
            <>
              <View style={{paddingVertical: 10, flexDirection: 'row'}}>
                <View
                  style={{
                    flexDirection: 'row',
                    flexBasis: 200,
                    alignItems: 'center',
                  }}>
                  <Sys.Text style={styles.detailText}>Mã TTB</Sys.Text>
                  <Sys.Text style={{color: 'red'}}> (*) </Sys.Text>
                  <Sys.Text style={styles.titleText}>
                    {temCode?.tem_code
                      ? `${temCode?.tem_code} - ${temCode?.status_label}`
                      : ''}
                  </Sys.Text>
                </View>
                <TouchableOpacity
                  onPress={() => openScanCodeScreen(setTemCode)}
                  style={styles.iconButton}
                  disabled={isDisabled}>
                  <BarcodeIcon2 />
                </TouchableOpacity>
              </View>
              <Divider />
            </>
          )}

          <View style={{paddingVertical: 5}}>
            <View style={{flexDirection: 'row', marginBottom: 10}}>
              <Sys.Text style={styles.detailText}>
                Trạng thái trang thiết bị
              </Sys.Text>
              <Sys.Text style={{color: 'red'}}> (*)</Sys.Text>
            </View>
            <TouchableOpacity
              onPress={() =>
                dispatch(
                  setAddNewItemModalVisible({
                    show: false,
                    nextModal: 'isTypeAddNewModalVisible',
                  }),
                )
              }
              disabled={isDisabled}
              style={[styles.selector, isDisabled && styles.buttonDisabled]}>
              <Sys.Text style={{color: selectedType ? 'black' : '#D1D1D1'}}>
                {equipmentType[selectedType] || 'Chọn trạng thái'}
              </Sys.Text>
              <View style={{position: 'absolute', right: 10, top: 10}}>
                <SelectIcon />
              </View>
            </TouchableOpacity>
          </View>
          <Divider />
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'flex-end',
            }}>
            <View style={{flexDirection: 'row', paddingVertical: 5}}>
              <Sys.Text style={styles.detailText}>Số lượng</Sys.Text>
              <Sys.Text style={{color: 'red'}}> (*) </Sys.Text>
            </View>
            <Sys.Input
              placeholder="Số lượng"
              disabled={product?.hasTem}
              value={quantity.toString()}
              onChangeText={text => {
                const qty = parseInt(text, 10);
                if (!isNaN(qty) && qty >= 1) {
                  setQuantity(qty);
                } else {
                  setQuantity(0);
                }
              }}
              keyboardType="numeric"
              style={{
                textAlign: 'right',
                borderWidth: 0,
                borderColor: 'transparent',
                paddingBottom: 0,
                paddingRight: 15,
                marginRight: 0,
              }}
            />
          </View>
          <Divider style={{margin: 0}} />
          <Sys.Input
            style={[styles.input, {height: height * 0.1}]}
            placeholder="Nhập nội dung cần ghi chú"
            value={note}
            onChangeText={isDisabled ? null : setNote}
            editable={!isDisabled}
          />
          <Divider />
          {!isDisabled && (
            <View
              style={{
                paddingVertical: 5,
                flexDirection: 'row',
                justifyContent: 'space-evenly',
                marginVertical: 5,
                alignItems: 'center',
              }}>
              <TouchableOpacity
                style={{flexBasis: 'auto', flexGrow: 1}}
                onPress={openCamera}>
                {photo ? (
                  <Image
                    style={{width: 50, height: 50, borderRadius: 10}}
                    source={{uri: photo.uri}}
                  />
                ) : (
                  <CameraIcon />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, !isFormValid() && styles.buttonDisabled]}
                disabled={!isFormValid()}
                onPress={handleSave}>
                <Sys.Text style={styles.buttonText}>
                  {currentItem?.isNew ? 'Cập nhật' : 'Lưu'}
                </Sys.Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </Modal>

      <Modal
        isVisible={isProductTypeModalVisible}
        propagateSwipe={true}
        onSwipeComplete={() =>
          dispatch(
            setIsProductTypeModalVisible({
              show: false,
              nextModal: 'isAddNewItemModalVisible',
            }),
          )
        }
        onBackdropPress={() =>
          dispatch(
            setIsProductTypeModalVisible({
              show: false,
              nextModal: 'isAddNewItemModalVisible',
            }),
          )
        }
        onModalHide={() => dispatch(handleModalHide())}
        swipeDirection="down"
        style={styles.modalStyle}>
        <View style={styles.modalContent}>
          <FlatList
            data={
              addNewTypeId && typeProducts
                ? typeProducts[addNewTypeId] || []
                : []
            }
            keyExtractor={item => item.id.toString()} // Assuming each product has a unique 'id'.
            renderItem={({item}) => {
              return (
                <TouchableOpacity
                  onPress={() => {
                    setProduct(item);
                    dispatch(
                      setIsProductTypeModalVisible({
                        show: false,
                        nextModal: 'isAddNewItemModalVisible',
                      }),
                    );
                  }}
                  style={styles.modalItem}>
                  <Sys.Text>{item?.name}</Sys.Text>
                </TouchableOpacity>
              );
            }}
          />
        </View>
      </Modal>

      <Modal
        isVisible={isTypeAddNewModalVisible}
        onSwipeComplete={() =>
          dispatch(
            setIsTypeAddNewModalVisible({
              show: false,
              nextModal: 'isAddNewItemModalVisible',
            }),
          )
        }
        onBackdropPress={() =>
          dispatch(
            setIsTypeAddNewModalVisible({
              show: false,
              nextModal: 'isAddNewItemModalVisible',
            }),
          )
        }
        onModalHide={() => dispatch(handleModalHide())}
        swipeDirection="down"
        style={styles.modalStyle}>
        <View style={styles.modalContent}>
          <FlatList
            data={Object.entries(equipmentType)}
            keyExtractor={([key, value]) => key}
            renderItem={({item: [key, value]}) => (
              <TouchableOpacity
                onPress={() => {
                  setSelectedType(key);
                  dispatch(
                    setIsTypeAddNewModalVisible({
                      show: false,
                      nextModal: 'isAddNewItemModalVisible',
                    }),
                  );
                }}
                style={styles.modalItem}>
                <Sys.Text>{value}</Sys.Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  titleText: {
    fontWeight: 'bold',
    marginTop: 5,
  },
  detailText: {
    fontSize: 12,
  },
  modalStyle: {
    padding: 0,
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    paddingVertical: 50,
    borderRadius: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalText: {
    marginBottom: 10,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
  },
  input: {
    marginBottom: 10,
    width: '100%',
    borderBottomWidth: 1,
    borderBottomColor: '#D1D1D1',
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  divider: {
    width: '100%',
    borderBottomWidth: 1,
    borderColor: 'rgba(129,120,120,0.17)',
    marginVertical: 5,
  },
  button: {
    backgroundColor: '#0062FF',
    padding: 10,
    paddingHorizontal: 12,
    borderRadius: 5,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#D1D1D1',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  label: {
    marginBottom: 5,
    textTransform: 'uppercase',
    fontWeight: '600',
    color: '#44444F',
  },
  required: {
    color: 'red',
  },
  row: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 'auto',
  },
  datePickerModal: {
    padding: 0,
    margin: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  datePickerContent: {
    backgroundColor: 'white',
    width: width,
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
  },
  datePickerTitle: {
    fontWeight: '600',
    marginBottom: 10,
  },
  modalItem: {
    padding: 10,
    marginBottom: 5,
    backgroundColor: '#d1d1d110',
    borderRadius: 10,
  },
  selector: {
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D1D1',
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginBottom: 10,
  },
});

export default AddNewItemModal;
