import {TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import Sys from '../../../components/Sys';
import ItemRightIcon from '../icons/ItemRightIcon';

const OneItemB = ({item, navigate}) => {
  const [isShowDetail, setIsShowDetail] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  return (
    <View>
      <View
        style={{
          flexDirection: 'row',
          padding: 10,
          paddingHorizontal: 20,
          backgroundColor: 'white',
          borderBottomWidth: 1,
          borderBottomColor: '#D1D1D1',
        }}>
        <View
          style={{
            flex: 1,
          }}>
          <Sys.Text>{item.typeName}</Sys.Text>
          {item.tem_code && (
            <Sys.Text
              style={{
                textAlign: 'center',
                color: '#848484',
              }}>
              {item.tem_code} -
            </Sys.Text>
          )}
          <Sys.Text style={{color: '#90BB3B', textAlign: 'center'}}>
            {item?.status}
          </Sys.Text>
        </View>
        <View
          style={{
            width: 100,
          }}>
          <Sys.Text
            style={{
              textAlign: 'center',
              color: '#90BB3B',
              fontSize: 16,
              fontWeight: '600',
            }}>
            {item?.quantity}
          </Sys.Text>
        </View>
        <TouchableOpacity
          // onPress={() => handleCheckBoxPress(item, setIsChecked, navigate)}
          style={{
            width: 100,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Sys.Checkbox checked={isChecked} />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => setIsShowDetail(true)}>
          <ItemRightIcon />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default OneItemB;
