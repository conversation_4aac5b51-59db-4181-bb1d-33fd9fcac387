import {View} from 'react-native';
import React from 'react';

const SkeletonItem = () => (
  <View
    style={{
      flexDirection: 'row',
      padding: 10,
      paddingHorizontal: 20,
      backgroundColor: 'white',
      borderBottomWidth: 1,
      borderBottomColor: '#D1D1D1',
      height: 55,
      alignItems: 'center',
    }}>
    <View
      style={{flex: 1, backgroundColor: '#E0E0E0', height: 20, borderRadius: 4}}
    />
    <View
      style={{
        width: 100,
        backgroundColor: '#E0E0E0',
        height: 20,
        marginLeft: 10,
        borderRadius: 4,
      }}
    />
    <View
      style={{
        width: 100,
        backgroundColor: '#E0E0E0',
        height: 20,
        marginLeft: 10,
        borderRadius: 4,
      }}
    />
  </View>
);

const DeviceListSkeletonList = () => (
  <View>
    {[...Array(20)].map((_, index) => (
      <SkeletonItem key={index} />
    ))}
  </View>
);
export default DeviceListSkeletonList;
