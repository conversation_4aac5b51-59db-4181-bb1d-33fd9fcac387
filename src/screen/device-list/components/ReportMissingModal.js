import React, {useEffect, useState} from 'react';
import {Dimensions, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import Sys from '../../../components/Sys';
import CloseIcon from '../../report/icons/CloseIcon';
import Divider from './Divider';
import {useDispatch, useSelector} from 'react-redux';
import {
  handleModalHide,
  setReportMissingModalVisible,
} from '../services/inventory.slice';
import {AccountSelectors} from '../../account/services/account.slice';

const {width, height} = Dimensions.get('window');

const ReportMissingModal = React.memo(({item, onAction}) => {
  const [note, setNote] = useState(item?.missingData?.note || '');
  const [quantity, setQuantity] = useState(1);

  const dispatch = useDispatch();
  const {isReportMissingModalVisible, mode} = useSelector(
    state => state.inventory,
  );
  const isPos = useSelector(AccountSelectors.isPos);

  const callbackSave = data => {
    if (data.success) {
      setNote(null);
    }
    // else {
    //   dispatch(
    //     setDetailedViewModalVisible({
    //       show: false,
    //       nextModal: 'isReportMissingModalVisible',
    //     }),
    //   );
    // }
  };

  const handleReportMissing = (action = true) => {
    const data = {
      itemId: item.id,
      warehouse_id: item.warehouse_id,
      product_id: item.product_id,
      item_id: item?.item_id || null,
      note: note,
      missingData: {note, quantity},
    };
    onAction({type: 'MISSING', action, data}, callbackSave);
    dispatch(
      setReportMissingModalVisible({
        show: false,
        nextModal: 'isDetailedViewModalVisible',
      }),
    );
  };

  const isFormValid = (state = false) => {
    return state ? item.isMissing : Boolean(note);
  };

  useEffect(() => {
    setNote(item?.missingData?.note);
    setQuantity(item?.missingData?.quantity || 1);
  }, [item]);
  const isDisabled = mode === 3;

  return (
    <Modal
      animationIn={'fadeInUp'}
      animationOut={'fadeOutDown'}
      style={styles.modalStyle}
      isVisible={isReportMissingModalVisible}
      avoidKeyboard={true}
      onSwipeComplete={() => {
        dispatch(
          setReportMissingModalVisible({
            show: false,
            nextModal: 'isDetailedViewModalVisible',
          }),
        );
      }}
      onModalHide={() => dispatch(handleModalHide())}
      swipeDirection="down">
      <View style={styles.modalContent}>
        <View style={styles.header}>
          <Sys.Text style={styles.modalText}>Báo thiếu trang thiết bị</Sys.Text>
          <TouchableOpacity
            onPress={() => {
              dispatch(
                setReportMissingModalVisible({
                  show: false,
                  nextModal: 'isDetailedViewModalVisible',
                }),
              );
            }}>
            <CloseIcon />
          </TouchableOpacity>
        </View>
        <Sys.Text style={[styles.detailText, {marginBottom: 10}]}>
          Số lượng thiếu
        </Sys.Text>
        <Sys.Input
          keyboardType="numeric"
          placeholder="Nhập số lượng thiếu"
          value={quantity.toString()}
          onChangeText={text => {
            const qty = parseInt(text, 10);
            if (!isNaN(qty) && qty >= 1 && qty <= item.qty) {
              setQuantity(qty);
            } else if (qty < 1 || isNaN(qty)) {
              setQuantity(0);
            } else {
              setQuantity(item.qty);
            }
          }}
          editable={!isDisabled}
        />
        <Divider />

        <Sys.Input
          style={styles.input}
          placeholder="Nhập nội dung cần ghi chú"
          value={note}
          onChangeText={isDisabled ? null : setNote}
          editable={!isDisabled}
        />
        {!isDisabled && (
          <View
            style={{
              flexBasis: 'auto',
              flexShrink: 0,
              flexGrow: 1,
              paddingVertical: 5,
              flexDirection: 'row',
              justifyContent: 'flex-end',
              marginVertical: 5,
            }}>
            {!isPos && mode !== 2 && (
              <TouchableOpacity
                style={[
                  styles.buttonDisabled,
                  isFormValid(true) && styles.buttonDefault,
                ]}
                onPress={() => handleReportMissing(false)}
                disabled={!isFormValid(true)}>
                <Sys.Text style={styles.buttonText}>Hủy báo thiếu</Sys.Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[
                styles.button,
                !isFormValid() && styles.buttonDisabled,
                {marginLeft: 10},
              ]}
              onPress={() => handleReportMissing(true)}
              disabled={!isFormValid()}>
              <Sys.Text style={styles.buttonText}>Cập nhật</Sys.Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
});

export default ReportMissingModal;

const styles = StyleSheet.create({
  modalStyle: {
    padding: 0,
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalText: {
    marginBottom: 10,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
  },
  input: {
    width: width * 0.9,
    height: height * 0.2,
    padding: 10,
    marginVertical: 20,
    marginTop: 0,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  button: {
    backgroundColor: '#0062FF',
    padding: 10,
    paddingHorizontal: 12,
    borderRadius: 5,
    marginBottom: 10,
  },
  buttonDefault: {
    backgroundColor: 'red',
    padding: 10,
    paddingHorizontal: 12,
    borderRadius: 5,
    marginBottom: 10,
  },
  buttonDisabled: {
    padding: 10,
    paddingHorizontal: 12,
    borderRadius: 5,
    marginBottom: 10,
    backgroundColor: '#dad8d8',
  },
  detailText: {
    fontSize: 12,
  },
});
