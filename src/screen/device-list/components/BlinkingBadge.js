import React, {useEffect, useRef} from 'react';
import {Animated, StyleSheet} from 'react-native';

const BlinkingBadge = () => {
  const fadeAnim = useRef(new Animated.Value(1)).current; // Initial opacity: 1

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    ).start();
  }, [fadeAnim]);

  return <Animated.View style={[styles.badge, {opacity: fadeAnim}]} />;
};

const styles = StyleSheet.create({
  badge: {
    position: 'absolute',
    right: 25,
    top: -3,
    backgroundColor: 'red',
    borderRadius: 10,
    width: 15,
    height: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default BlinkingBadge;
