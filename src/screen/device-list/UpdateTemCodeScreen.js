import React, {useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import Sys from '../../components/Sys';

const UpdateTemCodeScreen = () => {
  const {navigate, goBack} = useNavigation();
  const {params} = useRoute();
  const [updatedCode, setUpdatedCode] = useState(params.scannedCode);

  const handleUpdate = () => {
    // Logic for updating the tem_code
    navigate('DeviceListScreen', {updatedCode, temCode: params.temCode});
  };

  return (
    <Sys.Container hasHeader hasFooter footerColor="white">
      <Sys.Header onBack={goBack} title="Ghi nhận lệch mã TTB" />
      <View style={styles.container}>
        <Sys.Text style={styles.label}>
          Tên trang thiết bị: {params.oneItem.name}
        </Sys.Text>
        <Sys.Text style={styles.label}>
          Mã TTB số sách: {params.temCode}
        </Sys.Text>
        <Sys.Text style={styles.label}>Mã TTB thực tế: {updatedCode}</Sys.Text>
        <TouchableOpacity style={styles.updateButton} onPress={handleUpdate}>
          <Sys.Text style={styles.buttonText}>Cập nhật</Sys.Text>
        </TouchableOpacity>
      </View>
    </Sys.Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  label: {
    marginBottom: 10,
    fontSize: 16,
  },
  updateButton: {
    marginTop: 20,
    backgroundColor: '#0062FF',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default UpdateTemCodeScreen;
