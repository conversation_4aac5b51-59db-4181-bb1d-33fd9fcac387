import Toast from 'react-native-toast-message';
import {all, call, put, takeEvery} from 'redux-saga/effects';
import SysFetch from '../../../services/fetch';
import _ from 'lodash';
import {
  fetchEquipmentStatusFailure,
  fetchEquipmentStatusStart,
  fetchEquipmentStatusSuccess,
  fetchInitPosDetailFailure,
  fetchInitPosDetailStart,
  fetchInitPosDetailSuccess,
  fetchProducts,
  fetchProductsFailure,
  fetchProductsSuccess,
  fetchRequestChecklist,
  fetchRequestChecklistSuccess,
  setDataSectionMode,
  setDataSectionModeSuccess,
} from './inventory.slice';
import {
  INVENTORY_NONE,
  SUPPLIER_TYPE_BERJAYA_ID,
  SUPPLIER_TYPE_VIETLOTT_ID,
} from './const';
import {AppActions} from '../../../app.slice';

function* InventorySaga() {
  yield takeEvery(fetchInitPosDetailStart, fetchInitPosDetailStartSaga);
  yield takeEvery(fetchRequestChecklist, fetchRequestChecklistSaga);
  yield takeEvery(fetchEquipmentStatusStart, fetchEquipmentStatusSaga);
  yield takeEvery(fetchProducts, fetchProductsSaga);
  yield takeEvery(setDataSectionMode, setDataSectionModeSaga);
}

export default InventorySaga;

function* fetchEquipmentStatusSaga() {
  try {
    const [equipmentType, statuses] = yield all([
      call(SysFetch.get, 'inventory/reflection-status-item/types'),
      call(SysFetch.get, 'inventory/reflection-status-item/statuses'),
    ]);

    const conditionStatusData = statuses.data;

    const equipmentStatus = conditionStatusData.reduce((acc, status) => {
      const typeID = status.type.id;
      if (!acc[typeID]) {
        acc[typeID] = [];
      }
      acc[typeID].push(status);
      return acc;
    }, {});
    yield put(fetchEquipmentStatusSuccess({equipmentType, equipmentStatus}));
  } catch (error) {
    console.log(error);
    yield put(fetchEquipmentStatusFailure(error.toString()));
  }
}
function* fetchProductsSaga() {
  try {
    const typeProducts = yield SysFetch.get('products');

    const groupedProducts = _.groupBy(
      typeProducts.data,
      // .filter(({hasTem}) => hasTem),
      'type',
    );
    yield put(fetchProductsSuccess(groupedProducts));
  } catch (error) {
    if (error) {
      yield put(fetchProductsFailure(error.toString()));
    }
  }
}

function* fetchRequestChecklistSaga(action) {
  try {
    const rs = yield call(SysFetch.get, 'pos/pos-readiness-checklists');
    const requestData = rs.data?.map(x => {
      return {
        ...x,
        status: true,
        note: '',
        photo: undefined,
      };
    });

    yield put(
      fetchRequestChecklistSuccess({
        checklists: requestData,
      }),
    );
  } catch (error) {
    console.error('Failed to fetch request update:', error);
    Toast.show({
      type: 'error',
      text1: 'Error',
      text2: 'Unable to fetch request updates. Please try again.',
    });
  }
}

function* fetchInitPosDetailStartSaga({payload}) {
  const {posCode} = payload;
  try {
    const res = yield SysFetch.get(`pos/${posCode}/show`);

    yield put(AppActions.getSettings());
    yield call(fetchRequestChecklistSaga);
    yield call(fetchEquipmentStatusSaga);
    yield call(fetchProductsSaga);
    const products = res.items;
    const pos = res.data;
    const allowCheckin = res.allowCheckin;
    const allowCheckout = res.allowCheckout;

    // Helper function to process items
    function processItems(data, filter = true, defaults = {}) {
      return data
        .filter(item =>
          filter ? item.inventory_requirement !== INVENTORY_NONE : true,
        )
        .map(item => ({
          ...item,
          ...defaults,
        }));
    }

    const defaults = {
      isNew: false,
      isMissing: false,
      realQuantity: 0,
      checked: false,
      statusData: null,
      tagChanged: null,
    };

    const items = processItems(products, true, defaults);
    const allItems = processItems(products, false); // No filter, no defaults

    const sections = createSections(items, 1);
    const sectionsFull = createSections(allItems, 2);

    yield put(
      fetchInitPosDetailSuccess({
        pos,
        sections,
        sectionsFull,
        total: products.length,
        allowCheckout,
        allowCheckin,
      }),
    );
  } catch (error) {
    yield put(fetchInitPosDetailFailure(error.toString()));
    Toast.show({
      type: 'error',
      text1: 'Lỗi',
      text2: error.toString(),
    });
  }
}

function* setDataSectionModeSaga({payload}) {
  const {data, mode} = payload;

  try {
    const sections = createSections(data, mode);
    yield put(
      setDataSectionModeSuccess({
        sections,
      }),
    );
  } catch (error) {
    // yield put(fetchInitPosDetailFailure(error.toString()));
    // Toast.show({
    //   type: 'error',
    //   text1: 'Lỗi',
    //   text2: error.toString(),
    // });
  }
}

function createSections(items, mode = 1) {
  const groupedBySupplier = _.groupBy(items, item =>
    item.supplier_type_id === 1 ? 'Vietlott' : 'Berjaya',
  );
  return ['Vietlott', 'Berjaya'].map(key => ({
    title: key,
    type_id:
      key === 'Vietlott' ? SUPPLIER_TYPE_VIETLOTT_ID : SUPPLIER_TYPE_BERJAYA_ID,
    data: _.map(
      _.groupBy(groupedBySupplier[key] || [], 'product.name'),
      (items, typeName) => ({
        typeName,
        product_id: items[0].product_id,
        quantity: _.sumBy(items, 'qty'),
        realQuantity:
          mode === 1 || mode === 4
            ? 0
            : mode === 2
            ? _.sumBy(items, 'latest_actual_qty')
            : _.sumBy(items, 'realQuantity'),
        items,
      }),
    ),
  }));
}
