// services/api.js
import Toast from 'react-native-toast-message';
import SysFetch from '../../../services/fetch';
import FormData from 'form-data';
import {Alert} from 'react-native';

/**
 * General API call function to handle different actions.
 * @param {Object} actionState - The state object describing the action and data.
 */
export const handleModalDetailActionMode2 = async (
  actionState,
  callBack = () => {},
) => {
  const apiMap = {
    REPORT: 'inventory/item/report-item-status',
    MISSING: 'inventory/item/report-missing-item',
    CHANGE_TAG: 'inventory/item/replace-tag',
  };
  try {
    const endpoint = apiMap[actionState.type];
    if (actionState.action && endpoint) {
      let config = {};
      let body = actionState.data;
      if (actionState.type === 'REPORT' && body?.statusData?.photo?.uri) {
        let formData = new FormData();
        Object.keys(body).forEach(key => {
          if (key !== 'statusData') {
            formData.append(key, body[key]);
          }
        });
        const {photo, ...statusData} = body.statusData;
        formData.append('statusData', JSON.stringify(statusData));

        formData.append('photo', {
          uri: photo.uri,
          type: 'image/jpeg',
          name: 'status_photo.jpg',
        });

        body = formData;
        config = {
          headers: {
            'Content-Type': 'multipart/form-data',
            Accept: 'application/json',
          },
        };
      }

      const rs = await SysFetch.post(endpoint, body, config);
      const message = rs?.message || 'Thành công';
      Toast.show({
        type: 'success',
        text1: message,
      });
      callBack(rs);
    }
  } catch (error) {
    callBack({success: false});
    console.error('API Error:', error);
    let errorMessage = 'Đã xảy ra lỗi không mong muốn';
    if (error.response) {
      errorMessage = error.response.data?.error?.message || errorMessage;
    }
    Alert.alert('Thất bại', errorMessage);

    // Toast.show({
    //   type: 'error',
    //   text1: 'Thất bại',
    //   text2: errorMessage,
    // });
  }
};
