import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  sections: [
    {title: '<PERSON><PERSON>lot<PERSON>', data: []},
    {title: '<PERSON><PERSON><PERSON><PERSON>', data: []},
  ],
  sectionsHistories: [
    {title: '<PERSON><PERSON>lot<PERSON>', data: []},
    {title: '<PERSON><PERSON><PERSON><PERSON>', data: []},
  ],
  sectionsFull: [
    {title: 'V<PERSON>lott', data: []},
    {title: '<PERSON><PERSON><PERSON><PERSON>', data: []},
  ],
  request_update: null, // cập nhật tọa độ
  allowCheckin: false,
  allowCheckout: false,
  isLoading: false,
  error: null,
  scannedData: null,
  currentItem: null,
  currentTem: null,
  plan_id: null,
  mode: 1, // 1: checkin, 2: view danh sách ,3: lịch sử, 4 : đợt kiểm kê
  checkin_time_at: null,
  pos: {
    plan_current_approved: false, // kế hoạch đã duyệt tháng hiện tại đã duyệt
    plan_id_current_approved: null,
    current_month_plan: null,
    checked_in_planned: false, // trạng thái đã checkin theo kế hoạch tháng hiện tại
    banner: null, // ảnh checkin gần nhất
    allowAddPlan: false, // Được phé thêm vào kế hoạch tháng tiếp theo
    allowDeletePlan: false, // Được phé xóa pos hiện tại đã thêm vào kế hoạch tháng tiếp theo
  },
  lon: null,
  lat: null,
  images: [],
  checklists: [],
  typeProducts: null,
  equipmentType: {},
  equipmentStatus: {},

  isConfirmMismatchModalVisible: false,
  isConfirmUncheckModalVisible: false,

  isDetailedViewModalVisible: false,

  isStatusUpdateModalVisible: false,
  isTypeModalVisible: false,
  isStatusModalVisible: false,

  isReportMissingModalVisible: false,

  isTagChangeModalVisible: false,
  isTagDateModalVisible: false,

  isAddNewItemModalVisible: false,
  addNewTypeId: undefined,
  isProductTypeModalVisible: false,
  isTypeAddNewModalVisible: false,

  isTemModalVisible: false,

  nextModal: null,
  screenParent: 'DeviceListScreen',
};
const updateItemCheckState = (
  state,
  itemId,
  checked,
  realQuantity,
  photo = null,
  tem_code_scanned = null,
) => {
  let item, group, section;

  // Find the item and its group and section
  outerLoop: for (const sec of state.sections) {
    for (const grp of sec.data) {
      const itm = grp.items.find(i => i.id === itemId);
      if (itm) {
        item = itm;
        group = grp;
        section = sec;
        break outerLoop;
      }
    }
  }

  if (item) {
    if (checked) {
      // When checking the item, update its details
      item.checked = true;
      item.realQuantity = realQuantity;
      item.photo = photo;
      item.tem_code_scanned = tem_code_scanned;
    } else {
      if (item.isNew) {
        // Remove the new item if it is unchecked
        group.items = group.items.filter(i => i.id !== itemId);
        group.quantity = group.items.reduce((sum, i) => sum + (i.qty || 0), 0);
        group.realQuantity = group.items.reduce(
          (sum, i) => sum + (i.realQuantity || 0),
          0,
        );

        if (group.items.length === 0) {
          section.data = section.data.filter(g => g !== group);
        }
      } else {
        // Reset the item's details if it is unchecked
        item.checked = false;
        item.realQuantity = 0;
        item.photo = null;
        item.tem_code_scanned = null;
      }
    }

    // Update the group's realQuantity
    if (group) {
      group.realQuantity = group.items.reduce(
        (sum, i) => sum + (i.realQuantity || 0),
        0,
      );
    }

    // Handle missing data reset
    if (item.isMissing && !checked) {
      item.missingData = null;
      item.isMissing = false;
    }
  }

  return item;
};

const inventorySlice = createSlice({
  name: 'inventory',
  initialState,
  reducers: {
    setCheckinAt(state, action) {
      state.checkin_time_at = action.payload;
    },
    setDataSectionMode(state, action) {
      state.isLoading = true;
      state.mode = action.payload.mode;
    },
    setDataSectionModeSuccess(state, action) {
      if (state.mode === 1 || state.mode === 4) {
        state.sections = action.payload.sections;
      } else if (state.mode === 2) {
        state.sectionsFull = action.payload.sections;
      } else if (state.mode === 3) {
        state.sectionsHistories = action.payload.sections;
      }
      state.isLoading = false;
    },
    updateItemInSectionsFull(state, action) {
      const newData = action.payload;
      const {id} = newData;
      state.currentItem = newData;

      let found = false;

      for (const section of state.sectionsFull) {
        for (const group of section.data) {
          const item = group.items.find(item => item.id === id);
          if (item) {
            Object.assign(item, newData);
            found = true;
            break;
          }
        }
        if (found) {
          break;
        }
      }
    },
    changeMode(state, action) {
      state.mode = action.payload || 1;
    },
    changeScreenParent(state, action) {
      state.screenParent = action.payload || 'DeviceListScreen';
    },
    fetchProducts(state) {
      state.isLoading = true;
    },
    fetchProductsSuccess(state, action) {
      state.typeProducts = action.payload;
      state.isLoading = false;
    },
    fetchProductsFailure(state, action) {
      state.isLoading = false;
      state.error = action.payload;
    },
    fetchEquipmentStatusStart(state) {
      state.isLoading = true;
    },
    fetchEquipmentStatusSuccess(state, action) {
      state.isLoading = false;
      state.equipmentType = action.payload.equipmentType;
      state.equipmentStatus = action.payload.equipmentStatus;
    },
    fetchEquipmentStatusFailure(state, action) {
      state.isLoading = false;

      state.error = action.payload;
    },
    setLocationCheckinData(state, action) {
      // Update state with new location data from action payload
      state.lon = action.payload.lon;
      state.lat = action.payload.lat;
    },
    setUpdateLocationData(state, action) {
      // Update state with new location data from action payload
      state.pos = action.payload.data;
      state.request_update = action.payload.data.latest_required_update;
      state.allowCheckin = action.payload.allowCheckin;
      state.allowCheckout = action.payload.allowCheckout;
    },
    addPhotoToCurrentIndex(state, action) {
      const {index, photo} = action.payload;
      if (state.images[index].photo.length < state.images[index].max) {
        state.images[index].photo.push(photo);
      } else {
        // Replace the last photo if the maximum limit is reached
        state.images[index].photo[state.images[index].max - 1] = photo;
      }
    },
    removePhotoFromCurrentIndex(state, action) {
      const {index, photoIndex} = action.payload;
      if (state.images[index] && state.images[index].photo.length > 0) {
        state.images[index].photo.splice(photoIndex, 1);
      }
    },
    initializePhotos(state, action) {
      state.images = action.payload.map(x => ({
        key: x.key,
        max: x.max_photos,
        size: x.rules.max,
        max_width: x.rules.max_width,
        max_height: x.rules.max_height,
        photo: [],
        ...x,
      }));
    },
    fetchInitPosDetailStart(state, action) {
      const mode = action.payload.mode || 1;
      return {...initialState, mode, isLoading: true};
    },
    fetchInitPosDetailSuccess(state, action) {
      state.isLoading = false;
      state.total = action.payload.total;
      state.sections = action.payload.sections;
      state.sectionsFull = action.payload.sectionsFull;
      state.pos = action.payload.pos;
      state.plan_id = action.payload.pos?.plan_id_current_approved || null;
      state.request_update = action.payload.pos?.latest_required_update || null;
      state.allowCheckin = action.payload.allowCheckin;
      state.allowCheckout = action.payload.allowCheckout;
      state.error = null;
    },
    fetchRequestUpdate(state, action) {},
    fetchRequestChecklist(state, action) {},
    fetchRequestChecklistSuccess(state, action) {
      state.checklists = action.payload.checklists;
    },
    updateChecklists(state, action) {
      const {id, status, note, photo} = action.payload;
      const index = state.checklists.findIndex(s => s.id === id);
      const item = state.checklists[index];
      item.status = status;
      item.note = note;
      item.photo = photo;
      state.checklists[index] = item;
    },
    updatePlanInventory(state, action) {
      const {plan_id} = action.payload;
      state.mode = 4;
      state.plan_id = plan_id;
    },
    fetchInitPosDetailFailure(state, action) {
      state.isLoading = false;
      state.error = action.payload;
    },
    updateScannedData(state, action) {
      const {item, photo, temData} = action.payload;
      state.scannedData = {
        item_id: item.id,
        tem_code: temData?.tem_code || null,
        photo: photo,
      };
    },
    clearScannedData(state) {
      state.scannedData = null;
    },
    toggleItemCheck(state, action) {
      const {itemId} = action.payload;
      const scannedData = state.scannedData;

      const item = state.sections
        .flatMap(section => section.data.flatMap(group => group.items))
        .find(i => i.id === itemId);

      const checked = !item.checked;

      updateItemCheckState(
        state,
        itemId,
        checked,
        checked ? item?.realQuantity || item?.qty : 0,
        scannedData?.photo,
        scannedData?.tem_code,
      );

      state.scannedData = null;
      state.currentItem = null;
    },

    setCurrentItem(state, action) {
      state.currentItem = action.payload;
    },
    setCurrentTem(state, action) {
      state.currentTem = action.payload;
    },
    clearCurrentItem(state) {
      state.currentItem = null;
    },

    confirmMismatch(state, action) {
      const {itemId, missingData} = action.payload;

      const _item = state.sections
        .flatMap(section => section.data.flatMap(group => group.items))
        .find(i => i.id === itemId);

      const realQuantity = _item?.qty - missingData.quantity || 0;

      const item = updateItemCheckState(state, itemId, true, realQuantity);
      if (item) {
        item.isMissing = true;
        item.missingData = missingData;
      }
      if (state.currentItem.id === itemId) {
        state.currentItem = item;
      }
    },
    removeConfirmMismatch(state, action) {
      const {itemId} = action.payload;
      const item = updateItemCheckState(state, itemId, false, 0);
      if (item) {
        item.isMissing = false;
        item.missingData = null;
      }
      if (state.currentItem.id === itemId) {
        state.currentItem = item;
      }
    },
    reportStatus(state, action) {
      const {itemId, statusData} = action.payload;
      const item = state.sections
        .flatMap(section => section.data.flatMap(group => group.items))
        .find(i => i.id === itemId);
      console.log('reportStatus', statusData, item);

      if (item) {
        item.isStatus = true;
        item.statusData = statusData;
        if (state.currentItem.id === itemId) {
          state.currentItem = item;
        }
      }
    },
    removeStatusReport(state, action) {
      const {itemId} = action.payload;
      const item = state.sections
        .flatMap(section => section.data.flatMap(group => group.items))
        .find(i => i.id === itemId);
      if (item) {
        item.isStatus = false;
        item.statusData = null;
        if (state.currentItem.id === itemId) {
          state.currentItem = item;
        }
      }
    },
    changeTag(state, action) {
      const {itemId, tagData} = action.payload;
      const item = state.sections
        .flatMap(section => section.data.flatMap(group => group.items))
        .find(i => i.id === itemId);
      if (item) {
        item.tagChanged = true;
        item.tagData = tagData;
        if (state.currentItem.id === itemId) {
          state.currentItem = item;
        }
      }
    },
    removeTagChange(state, action) {
      const {itemId} = action.payload;

      const item = state.sections
        .flatMap(section => section.data.flatMap(group => group.items))
        .find(i => i.id === itemId);
      if (item) {
        item.tagChanged = false;
        item.tagData = null;
        if (state.currentItem.id === itemId) {
          state.currentItem = item;
        }
      }
    },
    setConfirmUncheckModalVisible(state, action) {
      state.isConfirmUncheckModalVisible = action.payload;
    },

    setDetailedViewModalVisible(state, action) {
      const {nextModal = null, show} = action.payload;
      state.nextModal = nextModal;
      state.isDetailedViewModalVisible = show;
    },
    setStatusUpdateModalVisible(state, action) {
      const {nextModal = null, show} = action.payload;
      state.nextModal = nextModal;
      state.isStatusUpdateModalVisible = show;
    },
    setReportMissingModalVisible(state, action) {
      const {nextModal = null, show} = action.payload;
      state.nextModal = nextModal;
      state.isReportMissingModalVisible = show;
    },

    setTagChangeModalVisible(state, action) {
      const {nextModal = null, show} = action.payload;
      state.nextModal = nextModal;
      state.isTagChangeModalVisible = show;
    },

    setTagDateModalVisible(state, action) {
      const {nextModal = null, show} = action.payload;
      state.nextModal = nextModal;
      state.isTagDateModalVisible = show;
    },

    setIsTypeModalVisible(state, action) {
      const {nextModal = null, show} = action.payload;
      state.nextModal = nextModal;
      state.isTypeModalVisible = show;
    },
    setIsStatusModalVisible(state, action) {
      const {nextModal = null, show} = action.payload;
      state.nextModal = nextModal;
      state.isStatusModalVisible = show;
    },
    handleModalHide(state, action) {
      if (state.nextModal) {
        state[state.nextModal] = true;
        state.nextModal = null;
      }
    },

    setAddNewItemModalVisible(state, action) {
      const {
        show = false,
        typeId = undefined,
        nextModal = null,
        isEdit = false,
      } = action.payload;
      state.isAddNewItemModalVisible = show;
      state.nextModal = nextModal;
      if (!isEdit) {
        state.currentItem = null;
      }
      if (typeId) {
        state.addNewTypeId = typeId;
      }
    },
    resetAddNewItemModalVisible(state, action) {
      state.isAddNewItemModalVisible = false;
      state.nextModal = null;
      state.addNewTypeId = null;
    },
    setIsTypeAddNewModalVisible(state, action) {
      const {nextModal = null, show} = action.payload;
      state.nextModal = nextModal;
      state.isTypeAddNewModalVisible = show;
    },
    setIsProductTypeModalVisible(state, action) {
      const {nextModal = null, show} = action.payload;
      state.nextModal = nextModal;
      state.isProductTypeModalVisible = show;
    },

    addNewItem(state, action) {
      const newItem = {
        ...action.payload,
        isNew: true,
        supplier_type_id: state.addNewTypeId,
        isMissing: false,
        checked: true,
      };
      const section = state.sections.find(
        s => s.type_id === state.addNewTypeId,
      );
      const group = section.data.find(g => g.product_id === newItem.product_id);
      if (group) {
        const existingItemIndex = group.items.findIndex(
          item => item.id === newItem.id,
        );
        if (existingItemIndex !== -1) {
          group.items[existingItemIndex] = {
            ...group.items[existingItemIndex],
            ...newItem,
          };
        } else {
          group.items.push(newItem);
          group.quantity += newItem.qty;
          group.realQuantity += newItem.realQuantity;
        }
      } else {
        const newGroup = {
          typeName: newItem.name,
          product_id: newItem.product_id,
          quantity: newItem.qty,
          realQuantity: newItem.realQuantity,
          items: [newItem],
        };
        section.data.push(newGroup);
      }
      state.addNewTypeId = undefined;
    },

    setIsConfirmMismatchModalVisible(state, action) {
      state.isConfirmMismatchModalVisible = action.payload;
    },
    setTemModalVisible(state, action) {
      state.isTemModalVisible = action.payload;
    },
    resetCheckin(state, action) {
      state = initialState;
    },
  },
});

export const {
  handleModalHide,
  setIsTypeModalVisible,
  setIsStatusModalVisible,
  resetCheckin,
  initializePhotos,
  removePhotoFromCurrentIndex,
  addPhotoToCurrentIndex,
  fetchRequestUpdate,
  fetchInitPosDetailStart,
  fetchInitPosDetailSuccess,
  fetchInitPosDetailFailure,
  updateScannedData,
  clearScannedData,
  toggleItemCheck,
  confirmMismatch,
  removeConfirmMismatch,
  setCurrentItem,
  setCurrentTem,
  setTemModalVisible,
  clearCurrentItem,
  addNewItem,
  removeItem,
  reportStatus,
  removeStatusReport,
  changeTag,
  removeTagChange,
  updateRequestDetails,
  updateChecklists,
  fetchRequestChecklist,
  fetchRequestChecklistSuccess,
  fetchProducts,
  fetchEquipmentStatusStart,
  fetchEquipmentStatusSuccess,
  fetchEquipmentStatusFailure,
  setAddNewItemModalVisible,
  setConfirmUncheckModalVisible,
  setDetailedViewModalVisible,
  setStatusUpdateModalVisible,
  setReportMissingModalVisible,
  setTagChangeModalVisible,
  setTagDateModalVisible,
  setIsConfirmMismatchModalVisible,
  setIsProductTypeModalVisible,
  resetAddNewItemModalVisible,
  setIsTypeAddNewModalVisible,
  setLocationCheckinData,
  setUpdateLocationData,
  changeMode,
  fetchProductsSuccess,
  fetchProductsFailure,
  changeScreenParent,
  setDataSectionMode,
  setDataSectionModeSuccess,
  setCheckinAt,
  updateItemInSectionsFull,
  updatePlanInventory,
} = inventorySlice.actions;

export default inventorySlice.reducer;
