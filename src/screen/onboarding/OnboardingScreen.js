import {useNavigation} from '@react-navigation/native';
import React, {useEffect} from 'react';
import {View, useWindowDimensions} from 'react-native';
import * as Ant from 'react-native-animatable';
import LogoComponent from '../../components/LogoComponent';
import Background from './icons/Background';
const OnboardingScreen = () => {
  const navigation = useNavigation();
  const {height, width} = useWindowDimensions();

  const onRedirectToLoginScreen = () => {
    navigation.reset({
      index: 0,
      routes: [
        {
          name: 'LoginScreen',
        },
      ],
    });
  };

  useEffect(() => {
    setTimeout(() => {
      onRedirectToLoginScreen();
    }, 2100);
  }, []);

  return (
    <View>
      <Ant.View
        animation={{
          0: {
            scale: 1.2,
          },
          0.7: {
            scale: 1.7,
          },
          1: {
            scale: 1.5,
          },
        }}
        duration={2000}
        style={{
          position: 'absolute',
          zIndex: 9,
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        }}>
        <Background />
      </Ant.View>
      <Ant.View
        animation={{
          0: {
            scale: 1,
          },
          0.7: {
            scale: 1.7,
          },
          1: {
            scale: 1.5,
          },
        }}
        duration={2000}
        style={{
          position: 'absolute',
          zIndex: 10,
          height,
          width,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <LogoComponent />
      </Ant.View>
    </View>
  );
};

export default OnboardingScreen;
