import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const Background = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={390}
    height={840}
    fill="none"
    {...props}>
    <Path fill="#D72035" d="M492 0h-593.416v840H492V0Z" />
    <Path
      fill="#BC212F"
      fillRule="evenodd"
      d="m380.281 397.614 1.974 1.001-483.671-90.991V463.02l486.842-51.752 3.699-11.93-8.844-1.724Z"
      clipRule="evenodd"
    />
    <Path
      fill="#EA1F46"
      fillRule="evenodd"
      d="M306.636 0h-408.052v153.089l483.671 245.526-.778-.139 6.897 3.309 11.486-37.487L306.636 0Z"
      clipRule="evenodd"
    />
    <Path
      fill="#EC5355"
      fillRule="evenodd"
      d="M-101.805 152.894V307.54l484.061 91.075-484.061-245.721Z"
      clipRule="evenodd"
    />
    <Path
      fill="#CE202E"
      fillRule="evenodd"
      d="m-101.416 76.53 368.448 221.388L33.164 0h-134.58v76.53Z"
      clipRule="evenodd"
    />
    <Path
      fill="#EE5255"
      fillRule="evenodd"
      d="M492 66.658 379.335 430.957l-480.751 365.355V840h354.097l134.636-404.51L492 357.958v-291.3Z"
      clipRule="evenodd"
    />
    <Path
      fill="#D72035"
      fillRule="evenodd"
      d="m-102 524.618 2.058 268.663 399.597-298.197 81.85-71.524 5.979-19.773L-102 524.618Z"
      clipRule="evenodd"
    />
  </Svg>
);
export default Background;
