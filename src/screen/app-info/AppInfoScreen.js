import React from 'react';
import {Image, View} from 'react-native';
import {useSelector} from 'react-redux';
import {AppSelectors} from '../../app.slice';
import Sys from '../../components/Sys';

const AppIntroScreen = () => {
  const {general} = useSelector(AppSelectors.settings);

  return (
    <Sys.Container hasHeader>
      <Sys.Header title={'Thông tin ứng dụng'} />
      <View
        style={{
          marginVertical: 10,
          marginHorizontal: 20,
        }}>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Image source={require('./icons/logo.png')} />
        </View>
        <View
          style={{
            marginVertical: 20,
          }}>
          <Sys.Text
            style={{
              fontSize: 18,
              lineHeight: 26,
              textAlign: 'center',
            }}>
            {general?.app_name}
          </Sys.Text>
          <Sys.Text
            style={{
              fontSize: 18,
              lineHeight: 26,
              textAlign: 'center',
              marginTop: 20,
            }}>
            <PERSON><PERSON><PERSON> bản ứng dụng: <Sys.Text>{general?.app_version}</Sys.Text>
          </Sys.Text>
        </View>
        <Sys.Text
          style={{
            textAlign: 'center',
            color: '#001451',
          }}>
          Website:{' '}
          <Sys.Text
            style={{
              textDecorationLine: 'underline',
              color: '#001451',
            }}>
            https://vietlott.vn
          </Sys.Text>
        </Sys.Text>
      </View>
    </Sys.Container>
  );
};

export default AppIntroScreen;
