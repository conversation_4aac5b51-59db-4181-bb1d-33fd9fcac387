import React from 'react';
import {TouchableOpacity} from 'react-native';
import Svg, {Path} from 'react-native-svg';

const Checkbox = ({checked, onPress, ...props}) => {
  return (
    <TouchableOpacity onPress={onPress}>
      {checked ? (
        <Svg
          xmlns="http://www.w3.org/2000/svg"
          width={24}
          height={24}
          fill="none">
          <Path
            fill="#0062FF"
            fillRule="evenodd"
            d="M6 2.4h12A3.6 3.6 0 0 1 21.6 6v12a3.6 3.6 0 0 1-3.6 3.6H6A3.6 3.6 0 0 1 2.4 18V6A3.6 3.6 0 0 1 6 2.4Z"
            clipRule="evenodd"
          />
          <Path
            fill="#fff"
            d="M8.832 11.136a1.2 1.2 0 0 0-1.686 1.708l3.05 3.01a1.2 1.2 0 0 0 1.768-.09l4.95-6a1.2 1.2 0 1 0-1.85-1.528l-4.116 4.988-2.116-2.088Z"
          />
        </Svg>
      ) : (
        <Svg
          xmlns="http://www.w3.org/2000/svg"
          width={24}
          height={24}
          fill="none">
          <Path
            fill="#B5B5BE"
            fillRule="evenodd"
            d="M6 2.4h12A3.6 3.6 0 0 1 21.6 6v12a3.6 3.6 0 0 1-3.6 3.6H6A3.6 3.6 0 0 1 2.4 18V6A3.6 3.6 0 0 1 6 2.4Zm0 1.2A2.4 2.4 0 0 0 3.6 6v12A2.4 2.4 0 0 0 6 20.4h12a2.4 2.4 0 0 0 2.4-2.4V6A2.4 2.4 0 0 0 18 3.6H6Z"
            clipRule="evenodd"
          />
        </Svg>
      )}
    </TouchableOpacity>
  );
};

export default Checkbox;
