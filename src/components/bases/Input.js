import {
  Keyboard,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableWithoutFeedback,
  useColorScheme,
  View,
} from 'react-native';
import React from 'react';
import SysText from './Text';
import {isIOS} from '../../services/util';
import THEME from '../theme/theme';

const Input = ({
  label,
  style,
  value,
  placeholder,
  onChangeText,
  secureTextEntry,
  editable = true,
  required = false,
  right = undefined,
  left = undefined,
  ...props
}) => {
  const colorScheme = useColorScheme();
  const styles = StyleSheet.create({
    container: {
      position: 'relative',
    },
    label: {
      lineHeight: 24,
      marginBottom: 5,
    },
    inputStyle: {
      color: colorScheme === 'dark' ? THEME.Color.black : THEME.Color.text,
      backgroundColor: editable ? 'white' : '#F2F2F2',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#D1D1D1',
      paddingVertical: 10,
      paddingHorizontal: 20,
      ...Platform.select({
        ios: {
          height: 50,
          paddingHorizontal: 20,
        },
        android: {
          padding: 0,
        },
      }),
    },
    required: {
      color: 'red',
    },
  });

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <View style={styles.container}>
        {label && (
          <SysText style={styles.label}>
            {label}
            {required && <Text style={styles.required}>*</Text>}
          </SysText>
        )}
        {left && (
          <View
            style={{
              left: 0,
              position: 'absolute',
              top: isIOS ? 0 : 8,
              zIndex: 1,
            }}>
            {left}
          </View>
        )}
        <TextInput
          editable={editable}
          style={[styles.inputStyle, style]}
          placeholderTextColor={THEME.Color.text}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          secureTextEntry={secureTextEntry}
          returnKeyType={isIOS ? 'done' : 'default'}
          onSubmitEditing={dismissKeyboard}
          {...props}
        />
        {right && (
          <View
            style={{
              position: 'absolute',
              right: 0,
            }}>
            {right}
          </View>
        )}
      </View>
    </TouchableWithoutFeedback>
  );
};

export default Input;
