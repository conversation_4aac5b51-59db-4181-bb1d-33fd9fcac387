import React from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import * as Ant from 'react-native-animatable';
import THEME from '../theme/theme';
import SysText from './Text';

const Button = ({
  style,
  labelStyle,
  children,
  left,
  right,
  onPress,
  disabled = false,
  backgroundColor = undefined,
}) => {
  const btnStyle = StyleSheet.create({
    container: {
      backgroundColor: disabled ? '#848484' : '#001451',
      borderRadius: 12,
      padding: 12,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    label: {
      color: 'white',
      flex: 1,
      textAlign: 'center',
      fontFamily: THEME.FrontFamily['Roboto-Medium'],
      fontSize: 16,
    },
  });

  return (
    <Ant.View animation={'fadeIn'}>
      <TouchableOpacity
        disabled={disabled}
        onPress={onPress}
        activeOpacity={0.7}
        style={[btnStyle.container, style]}>
        {left}
        <SysText style={[btnStyle.label, labelStyle]}>{children}</SysText>
        {right}
      </TouchableOpacity>
    </Ant.View>
  );
};

export default Button;
