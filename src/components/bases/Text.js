import React from 'react';
import {Platform, StyleSheet, useColorScheme} from 'react-native';
import * as Animatable from 'react-native-animatable';
import THEME from '../theme/theme';

const SysText = props => {
  const colorScheme = useColorScheme();

  const textStyle = StyleSheet.create({
    text: {
      color: colorScheme === 'dark' ? THEME.Color.black : THEME.Color.text,
      fontFamily: (() => {
        return Platform.select({
          ios: THEME.FrontFamily['Roboto-Regular'],
          android: THEME.FrontFamily['Roboto-Regular'],
        });
      })(),
    },
  });

  return (
    <Animatable.Text
      animation={'fadeIn'}
      {...props}
      style={[textStyle.text, props.style]}
    />
  );
};

export default SysText;
