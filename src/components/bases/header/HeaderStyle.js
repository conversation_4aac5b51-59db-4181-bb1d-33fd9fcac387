import {StyleSheet} from 'react-native';
import {isIOS} from '../../../services/util';

const HeaderStyle = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: isIOS ? 40 : 55,
    alignItems: 'center',
    justifyContent: 'center',
  },
  btnGoBack: {
    padding: 12,
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    lineHeight: 28,
    textTransform: 'uppercase',
  },
  left: {
    position: 'absolute',
    left: 0,
    zIndex: 20,
  },
  right: {
    position: 'absolute',
    right: 0,
    zIndex: 10,
  },
});
export default HeaderStyle;
