import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import SysText from '../Text';
import HeaderStyle from './HeaderStyle';

const HeaderComponent = ({
  right,
  title,
  hideGoBack = false,
  color = '#001451',
  onBack = undefined,
  iconColor = '#fff',
  style = {},
}) => {
  const {goBack, canGoBack} = useNavigation();

  return (
    <View
      style={[
        {
          backgroundColor: color,
        },
        HeaderStyle.container,
        style,
      ]}>
      <View style={HeaderStyle.left}>
        {canGoBack && !hideGoBack ? (
          <TouchableOpacity
            style={HeaderStyle.btnGoBack}
            onPress={() => {
              if (onBack) {
                onBack();
              } else {
                goBack();
              }
            }}>
            <MaterialCommunityIcons
              color={iconColor}
              size={34}
              name="chevron-left"
            />
          </TouchableOpacity>
        ) : (
          <></>
        )}
      </View>
      <View style={HeaderStyle.titleContainer}>
        <SysText style={[HeaderStyle.title]}>{title}</SysText>
      </View>
      <View style={HeaderStyle.right}>{right && right}</View>
    </View>
  );
};

export default HeaderComponent;
