import React, {useEffect} from 'react';
import {StyleSheet, View} from 'react-native';
import Modal from 'react-native-modal';
import Sys from '../components/Sys';
import * as NetInfo from '@react-native-community/netinfo';
import Color from './theme/Color';
import {useDispatch, useSelector} from 'react-redux';
import {AppActions, AppSelectors} from '../app.slice';

const ConnectionAlert = () => {
  const dispatch = useDispatch();
  const isConnected = useSelector(AppSelectors.isConnected);

  // const handleClose = () => {
  //   if (isConnected) {
  //     resetNavigation('LoginScreen');
  //   }
  // };
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      dispatch(AppActions.setConnected(state.isConnected));
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 20,
    },
    modalContent: {
      width: '100%',
      backgroundColor: '#ffffff',
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 50,
      shadowOpacity: 0.25,
      shadowRadius: 3.5,
      elevation: 5,
      shadowOffset: {width: 0, height: 2},
      shadowColor: '#000',
    },
    modalText: {
      fontSize: 14,
      textAlign: 'center',
      color: Color.red,
      marginBottom: 30,
      textTransform: 'uppercase',
      fontWeight: 'bold',
    },
    closeButton: {
      backgroundColor: '#0057d9',
      paddingHorizontal: 20,
      paddingVertical: 10,
      borderRadius: 10,
      marginTop: 10,
      shadowOpacity: 0.2,
      shadowRadius: 3,
      shadowOffset: {width: 0, height: 2},
      shadowColor: '#000',
      elevation: 2,
    },
    closeButtonText: {
      color: 'white',
      fontSize: 16,
    },
  });

  return (
    <Modal
      isVisible={!isConnected}
      backdropOpacity={0.5}
      animationIn="zoomIn"
      animationOut="zoomOut"
      backdropTransitionInTiming={600}
      backdropTransitionOutTiming={600}
      useNativeDriver={true}
      onBackdropPress={() => {}}
      hideModalContentWhileAnimating={true}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Sys.Text style={styles.modalText}>
            Không có kết nối mạng. Vui lòng kiểm tra lại kết nối của bạn.
          </Sys.Text>
          {/*<TouchableOpacity style={styles.closeButton} onPress={handleClose}>*/}
          {/*  <Text style={styles.closeButtonText}>Đóng</Text>*/}
          {/*</TouchableOpacity>*/}
        </View>
      </View>
    </Modal>
  );
};

export default ConnectionAlert;
