import React from 'react';
import {StyleSheet, View} from 'react-native';
import * as Ant from 'react-native-animatable';
import {useSelector} from 'react-redux';
import {AppSelectors} from '../app.slice';
import Loading from './Loading';
import ReactNativeModal from 'react-native-modal';
const GlobalLoading = () => {
  const isLoading = useSelector(AppSelectors.isLoading);

  const styles = StyleSheet.create({
    container: {
      position: 'absolute',
      backgroundColor: '#ffffff99',
      width: '100%',
      height: '100%',
      zIndex: 9999,
      justifyContent: 'center',
      alignItems: 'center',
    },
    content: {
      width: 100,
      height: 100,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 12,
    },
  });

  return (
    <ReactNativeModal
      style={{
        zIndex: 100,
        flex: 1,
      }}
      isVisible={isLoading}>
      <View
        styles={{
          flex: 1,
        }}>
        <Loading />
      </View>
    </ReactNativeModal>
  );
};

export default GlobalLoading;
