import {createNativeStackNavigator} from '@react-navigation/native-stack';
import React, {useEffect} from 'react';
import DashboardScreen from '../screen/dashboard/DashboardScreen';
import LoginScreen from '../screen/login/LoginScreen';
import OnboardingScreen from '../screen/onboarding/OnboardingScreen';
import THEME from './theme/theme';
import ForgotPasswordScreen from '../screen/forgot-password/ForgotPasswordScreen';
import InputUserInfoScreen from '../screen/forgot-password/InputUserInfoScreen';
import CreatePasswordScreen from '../screen/forgot-password/CreatePasswordScreen';
import ConformAccountInformationScreen from '../screen/login/ConformAccountInformationScreen';
import AccountInformationScreen from '../screen/account/account-information/AccountInformationScreen';
import EditAccountInformationScreen from '../screen/account/edit-account-information/EditAccountInformationScreen';
import ChangePasswordScreen from '../screen/account/change-password/ChangePasswordScreen';
import SettingScreen from '../screen/account/setting/SettingScreen';
import NotificationScreen from '../screen/notification/NotificationScreen';
import NotificationDetail from '../screen/notification-detail/NotificationDetail';
import SaleLocationDetailScreen from '../screen/sale-location-detail/SaleLocationDetailScreen';
import TrackingAllocationScreen from '../screen/tracking-allocation/TrackingAllocationScreen';
import ItemsForStoreScreen from '../screen/items-for-store/ItemsForStoreScreen';
import ChatDetailScreen from '../screen/chat-detail/ChatDetailScreen';
import SelectRoleScreen from '../screen/select-role/SelectRoleScreen';
import SaleInformationScreen from '../screen/sale-information/SaleInformationScreen';
import SaleLocationScreen from '../screen/sale-location/SaleLocationScreen';
import CheckInLocationScreen from '../screen/checkin-location/CheckInLocationScreen';
import CheckInLocationCameraScreen from '../screen/checkin-location-camera/CheckInLocationCameraScreen';
import VisitPlanScreen from '../screen/visit-plan/VisitPlanScreen';
import MessageSupportScreen from '../screen/message-support/MessageSupportScreen';
import HomeScreen from '../screen/home/<USER>';
import MessageDetailScreen from '../screen/message-detail/MessageDetailScreen';
import ReportScreen from '../screen/report/ReportScreen';
import ReportDayScreen from '../screen/report/screens/ReportDayScreen';
import ReportMonthScreen from '../screen/report/screens/ReportMonthScreen';
import ReportQuarterScreen from '../screen/report/screens/ReportQuarterScreen';
import ReportAgencyScreen from '../screen/report/screens/ReportAgencyScreen';
import SaleLocationFilterScreen from '../screen/sale-location/SaleLocationFilterScreen';
import {useDispatch, useSelector} from 'react-redux';
import {AppActions, AppSelectors} from '../app.slice';
import DeviceListScreen from '../screen/device-list/DeviceListScreen';
import ConformCheckinScreen from '../screen/conform-checkin/ConformCheckinScreen';
import CheckinResultScreen from '../screen/checkin-result/CheckinResultScreen';
import SurveyScreen from '../screen/survey/SurveyScreen';
import CheckDeviceScreen from '../screen/check-device/CheckDeviceScreen';
import CheckoutScreen from '../screen/checkout/CheckoutScreen';
import CheckCloseScreen from '../screen/check-close/CheckCloseScreen';
import CameraScreen from '../screen/camera/CameraScreen';
import UpdateTemCodeScreen from '../screen/device-list/UpdateTemCodeScreen';
import ReportFilterScreen from '../screen/report/ReportFilterScreen';
import AppContactScreen from '../screen/app-contact/AppContactScreen';
import AppInfoScreen from '../screen/app-info/AppInfoScreen';
import ScanCodeScreen from '../screen/camera/ScanCodeScreen';
import WebViewScreen from '../screen/survey/WebViewScreen';
import RegisterAccountScreen from '../screen/register-account/RegisterAccountScreen';
import EIPScreen from '../screen/survey/EIPScreen';
import MarketingScreen from '../screen/survey/MarketingScreen';
import MarketingItemDetailsScreen from '../screen/survey/MarketingItemDetailsScreen';
import ReportOwnerScreen from '../screen/report/ReportOwnerScreen';
import messaging from '@react-native-firebase/messaging';
import navigateToScreen from '../services/navigation';
import BranchManagerMessageSubScreen from '../screen/branch-manager-message/BranchManagerMessageSubScreen';
import NotificationNewDetail from '../screen/notification/components/NotificationNewDetail';
import BranchManagerFilterScreen from '../screen/branch-manager-home/BranchManagerFilterScreen';
import DeleteAccountScreen from '../screen/delete-account/DeleteAccountScreen';
import ReportBranchScreen from '../screen/report/screens/ReportBranchScreen';
import CompleteHistoryScreen from '../screen/sale-information/CompleteHistoryScreen';
import {PermissionsAndroid} from 'react-native';
import {NotificationActions} from '../screen/notification/services/notification.slice';
import AttendanceScreen from '../screen/attendance/AttendanceScreen';
import AttendanceListScreen from '../screen/attendance/AttendanceListScreen';
import EditAttendanceScreen from '../screen/attendance/EditAttendanceScreen';
import OtpInputScreen from '../screen/login/OtpInputScreen';
import AccountConfirmationScreen from '../screen/login/AccountConfirmationScreen';
import ChangePasswordScreenFirst from '../screen/login/ChangePasswordScreenFirst';

const Stack = createNativeStackNavigator();
const InitialScreen = () => {
  const dispatch = useDispatch();
  const bioToken = useSelector(AppSelectors.bioToken);

  useEffect(() => {
    dispatch(AppActions.getSettings());
  }, []);

  useEffect(() => {
    // Xin quyền thông báo
    PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
    );

    // Xử lý thông báo khi ứng dụng đang chạy nền hoặc bị đóng
    messaging().onNotificationOpenedApp(remoteMessage => {
      console.log(
        'Notification caused app to open from background state:',
        remoteMessage.notification,
      );
      const {screen, ...params} = remoteMessage.data;
      if (screen) {
        navigateToScreen(screen, params);
      }
    });

    // Xử lý thông báo khi ứng dụng được mở từ trạng thái bị đóng hoàn toàn
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log(
            'Notification caused app to open from quit state:',
            remoteMessage.notification,
          );
          const {screen, ...params} = remoteMessage.data;
          if (screen) {
            navigateToScreen(screen, params);
          }
        }
      });

    // Xử lý thông báo khi ứng dụng đang chạy foreground
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      console.log('A new FCM message arrived!', remoteMessage);
      if (bioToken) {
        setTimeout(() => {
          dispatch(NotificationActions.getNotification());
        }, 3000);
      }

      // Alert.alert(
      //   remoteMessage.notification.title,
      //   remoteMessage.notification.body,
      //   [
      //     {
      //       text: 'OK',
      //       onPress: () =>
      //         remoteMessage.data &&
      //         navigateToScreen(remoteMessage.data.screen, remoteMessage.data),
      //     },
      //   ],
      // );
    });

    // Handle background/quit state notifications
    messaging().setBackgroundMessageHandler(async remoteMessage => {
      console.log('Message handled in the background!', remoteMessage);
    });

    return unsubscribe;
  }, []);

  return (
    <Stack.Navigator
      initialRouteName="OnboardingScreen"
      screenOptions={{
        gestureEnabled: false,
        animation: 'ios',
        headerShown: false,
        transitionSpec: {
          open: THEME.Animation.screen,
          close: THEME.Animation.screen,
        },
      }}>
      <Stack.Screen
        name="BranchManagerFilterScreen"
        component={BranchManagerFilterScreen}
      />
      <Stack.Screen name="AppContactScreen" component={AppContactScreen} />
      <Stack.Screen name="ReportBranchScreen" component={ReportBranchScreen} />
      <Stack.Screen
        name="DeleteAccountScreen"
        component={DeleteAccountScreen}
      />
      <Stack.Screen name="ReportOwnerScreen" component={ReportOwnerScreen} />
      <Stack.Screen
        name="RegisterAccountScreen"
        component={RegisterAccountScreen}
      />
      <Stack.Screen name="AppInfoScreen" component={AppInfoScreen} />
      <Stack.Screen name="ReportFilterScreen" component={ReportFilterScreen} />
      <Stack.Screen name="SurveyScreen" component={SurveyScreen} />
      <Stack.Screen name="EIPScreen" component={EIPScreen} />
      <Stack.Screen name="MarketingScreen" component={MarketingScreen} />
      <Stack.Screen
        name="MarketingItemDetailsScreen"
        component={MarketingItemDetailsScreen}
      />
      <Stack.Screen name="WebViewScreen" component={WebViewScreen} />
      <Stack.Screen name="CheckDeviceScreen" component={CheckDeviceScreen} />
      <Stack.Screen
        name="CheckinResultScreen"
        component={CheckinResultScreen}
      />
      <Stack.Screen name="DeviceListScreen" component={DeviceListScreen} />
      <Stack.Screen
        name="ConformCheckinScreen"
        component={ConformCheckinScreen}
      />
      <Stack.Screen
        name="SaleInformationScreen"
        component={SaleInformationScreen}
      />
      <Stack.Screen name="HomeScreen" component={HomeScreen} />
      <Stack.Screen name="OnboardingScreen" component={OnboardingScreen} />
      <Stack.Screen name="SelectRoleScreen" component={SelectRoleScreen} />
      <Stack.Screen name="DashboardScreen" component={DashboardScreen} />
      <Stack.Screen name="LoginScreen" component={LoginScreen} />
      <Stack.Screen
        name="ConformAccountInformationScreen"
        component={ConformAccountInformationScreen}
      />
      <Stack.Screen
        name="ForgotPasswordScreen"
        component={ForgotPasswordScreen}
      />
      <Stack.Screen
        name="InputUserInfoScreen"
        component={InputUserInfoScreen}
      />
      <Stack.Screen
        name="CreatePasswordScreen"
        component={CreatePasswordScreen}
      />
      <Stack.Screen
        name="AccountInformationScreen"
        component={AccountInformationScreen}
      />
      <Stack.Screen
        name="EditAccountInformationScreen"
        component={EditAccountInformationScreen}
      />
      <Stack.Screen
        name="ChangePasswordScreen"
        component={ChangePasswordScreen}
      />
      <Stack.Screen
        name="ChangePasswordScreenFirst"
        component={ChangePasswordScreenFirst}
      />
      <Stack.Screen name="SettingScreen" component={SettingScreen} />
      <Stack.Screen name="NotificationScreen" component={NotificationScreen} />
      <Stack.Screen
        name="NotificationDetailScreen"
        component={NotificationDetail}
      />
      <Stack.Screen
        name="NotificationNewDetail"
        component={NotificationNewDetail}
      />
      <Stack.Screen
        name="SaleLocationDetailScreen"
        component={SaleLocationDetailScreen}
      />
      <Stack.Screen
        name="CompleteHistoryScreen"
        component={CompleteHistoryScreen}
      />
      <Stack.Screen
        name="TrackingAllocationScreen"
        component={TrackingAllocationScreen}
      />
      <Stack.Screen
        name="ItemsForStoreScreen"
        component={ItemsForStoreScreen}
      />
      <Stack.Screen name="ChatDetailScreen" component={ChatDetailScreen} />
      <Stack.Screen name="SaleLocationScreen" component={SaleLocationScreen} />
      <Stack.Screen
        name="SaleLocationFilterScreen"
        component={SaleLocationFilterScreen}
      />
      <Stack.Screen
        name="CheckInLocationScreen"
        component={CheckInLocationScreen}
      />
      <Stack.Screen
        name="CheckInLocationCameraScreen"
        component={CheckInLocationCameraScreen}
      />
      <Stack.Screen name="CheckCloseScreen" component={CheckCloseScreen} />
      <Stack.Screen name="VisitPlanScreen" component={VisitPlanScreen} />
      <Stack.Screen
        name="MessageSupportScreen"
        component={MessageSupportScreen}
      />
      <Stack.Screen
        name="MessageDetailScreen"
        component={MessageDetailScreen}
      />
      <Stack.Screen name="ReportScreen" component={ReportScreen} />
      <Stack.Screen name="ReportDayScreen" component={ReportDayScreen} />
      <Stack.Screen name="ReportMonthScreen" component={ReportMonthScreen} />
      <Stack.Screen
        name="ReportQuarterScreen"
        component={ReportQuarterScreen}
      />
      <Stack.Screen name="ReportAgencyScreen" component={ReportAgencyScreen} />
      <Stack.Screen name="CheckoutScreen" component={CheckoutScreen} />
      <Stack.Screen name="CameraScreen" component={CameraScreen} />
      <Stack.Screen name="ScanCodeScreen" component={ScanCodeScreen} />
      <Stack.Screen
        name="UpdateTemCodeScreen"
        component={UpdateTemCodeScreen}
      />
      <Stack.Screen
        name="BranchManagerMessageSubScreen"
        component={BranchManagerMessageSubScreen}
      />
      <Stack.Screen name="AttendanceScreen" component={AttendanceScreen} />
      <Stack.Screen
        name="AttendanceListScreen"
        component={AttendanceListScreen}
      />
      <Stack.Screen
        name="EditAttendanceScreen"
        component={EditAttendanceScreen}
      />
      <Stack.Screen name="OtpInputScreen" component={OtpInputScreen} />
      <Stack.Screen
        name="AccountConfirmationScreen"
        component={AccountConfirmationScreen}
      />
      {/*<Stack.Screen*/}
      {/*  name="ChangePasswordScreen"*/}
      {/*  component={ChangePasswordScreen}*/}
      {/*/>*/}
    </Stack.Navigator>
  );
};

export default InitialScreen;
//
