import * as React from 'react';
import Svg, {<PERSON>, <PERSON>, <PERSON>} from 'react-native-svg';
const LogoComponent = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={135}
    height={122}
    fill="none"
    {...props}>
    <Path
      fill="#fff"
      d="M32.49 96.027c-1.739.005-4.194.429-4.919 2.66l-.538 3.086s5.59.73 6.458-1.867c.483-1.163.552-2.49.828-3.733 0 0-.786-.148-1.83-.146ZM2.425 95.984C1.142 96 0 96.496 0 96.496s1.822 14.042 2.485 18.02c.661 3.976 3.146 6.493 7.203 6.493 4.056 0 6.871-5.032 6.871-5.032l9.935-19.318c-6.789-2.192-8.693 3.002-8.693 3.002l-6.126 11.607s-.995 2.842-1.987 2.76c-.995-.08-1.243-1.947-1.243-1.947s-1.573-11.282-2.07-13.311c-.496-2.03-1.903-2.597-3.395-2.76a4.533 4.533 0 0 0-.555-.026Zm114.566.026s-5.919-.082-6.996 5.478c-1.076 5.561-2.401 13.393-2.401 13.393s-1.324 5.682 5.05 5.885c0 0 5.299.527 6.251-5.439 0 0-5.422 1.421-4.802-2.11l1.284-7.103s5.01.528 5.921-5.315h-5.093l.786-4.789Zm13.699 0s-5.919-.082-6.996 5.478c-1.076 5.561-2.401 13.393-2.401 13.393s-1.324 5.682 5.052 5.885c0 0 5.299.527 6.251-5.439 0 0-5.424 1.421-4.802-2.11l1.283-7.103s5.009.528 5.92-5.315h-5.093l.786-4.789Zm-53.785.006a9.26 9.26 0 0 0-1.063.075l-3.271 18.911s-1.407 5.559 4.885 5.439c6.293-.123 6.334-5.357 6.334-5.357s-5.175 1.664-4.637-1.949c.539-3.611 2.07-12.378 2.07-12.378s.619-4.779-4.318-4.741Zm-11.359.057c-1.426.016-5.343.505-6.554 5.252l-2.194 13.516s-1.407 6.005 5.174 5.843c0 0 4.927.082 5.963-5.357 0 0-4.223 1.421-4.843-1.379l1.365-7.834s5.216.691 5.795-5.315h-4.884l.704-4.708s-.197-.022-.526-.018Zm31.616 4.238s-11.633.081-13.166 11.728c0 0-.454 8.807 9.688 8.727 0 0 11.26.12 12.502-11.973-.911-8.562-9.024-8.482-9.024-8.482Zm-49.93.085c-1.926.023-4.227.535-6.868 1.945 0 0-6.127 3.49-5.465 10.023 0 0-.248 8.36 9.44 8.685 0 0 7.823-.163 9.892-7.101-1.959 1.826-8.306 3.408-11.591.12-4.346-5.965 1.615-9.252 1.615-9.252 4.595-1.461 5.174 1.868 5.174 1.868-.082 5.112-6.167 4.139-6.167 4.139s2.566 4.383 9.356.893c0 0 5.381-2.76 1.656-8.685 0 0-2.478-2.689-7.042-2.635Zm-15.852 3.121s-5.588-.162-6.706 3.735c-1.119 3.896-1.491 8.075-1.491 8.075s-.917 5.334 5.217 5.519c5.381.163 6.25-5.112 6.085-5.397 0 0-4.968 1.218-4.802-1.624.166-2.84 1.697-10.308 1.697-10.308Zm64.457 1.908s3.519-.122 3.932 4.139c-.454 5.803-5.754 5.965-5.754 5.965-3.85-.77-3.684-4.098-3.684-4.098.455-5.235 5.506-6.006 5.506-6.006Z"
    />
    <Mask
      id="a"
      width={84}
      height={82}
      x={23}
      y={0}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: 'luminance',
      }}>
      <Path fill="#fff" d="M23.329.476h82.954v80.993H23.329V.477Z" />
    </Mask>
    <G mask="url(#a)">
      <Path
        fill="#fff"
        d="M64.804.788c-4.208-.032-8.352.417-11.374 1.349-5.375 1.655-10.205 4.17-10.205 4.17s-5.238 3.112-.272 5.43c4.966 2.317 12.518 6.092 12.518 6.092s2.72 1.722 9.32 1.986c6.6.265 9.865.133 9.865.133L78.33 7.83s2.45-4.701-4.082-6.092c-2.857-.608-6.17-.924-9.444-.95Zm18.269 4.327c-1.561-.035-2.822.815-2.499 2.715l3.743 12.118H96.63s3.266-.994 1.497-3.245c-1.77-2.252-8.709-9.005-11.838-10.33-.94-.798-2.149-1.235-3.217-1.258Zm16.182 16.088c-1.47-.03-2.692 1.129-2.692 1.129l-9.117 6.489s2.041 6.951 2.926 9.071c.884 2.118 2.925 4.767 2.925 4.767l9.117 6.555s1.836 1.39 2.925-.596c1.088-1.986.748-6.29.748-10.461 0-4.172-2.585-13.64-4.762-15.957-.68-.725-1.401-.984-2.07-.997Zm-40.533.833c-1.203.001-1.958.228-1.958.228L30.571 23.59c-4.354-.133-5.034 3.973-5.034 3.973s-1.905 4.57-2.177 13.574c-.272 9.006 4.762 19.268 7.347 22.91 2.585 3.642 6.532.927 6.532.927S60.98 46.83 64.656 43.85c3.672-2.98 4.762-7.547 4.762-7.547l2.449-7.284-6.94-4.9c-2.372-1.714-4.659-2.086-6.205-2.083ZM79.758 34.05l-8.027 5.893s-3.334 3.443-4.83 8.343c-1.498 4.9-7.961 26.75-7.961 26.75s-2.79 6.356 5.852 6.158c7.686-.177 14.83-1.655 22.382-6.224 7.551-4.568 9.592-8.74 9.592-8.74s1.634-1.126.068-5.43c-1.563-4.303-6.19-16.95-6.19-16.95s-1.565-2.516-3.946-4.37c-2.381-1.855-6.94-5.43-6.94-5.43Z"
      />
    </G>
  </Svg>
);
export default LogoComponent;
