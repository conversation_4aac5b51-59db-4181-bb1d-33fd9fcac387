import React, {use<PERSON>allback, useEffect, useMemo, useState} from 'react';
import {
  Animated,
  Modal,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import Svg, {
  Defs,
  G,
  Line,
  LinearGradient as SvgLinearGradient,
  Rect,
  Stop,
  Text as SvgText,
} from 'react-native-svg';
import Sys from '../Sys';
import {useDispatch} from 'react-redux';
import {OwnerActions} from '../../screen/owner/services/slice';
import {ReportActions} from '../../screen/report/services/report.slice';
import moment from 'moment';
import THEME from '../theme/theme';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import {formatRevenue} from '../../utils/chartUtils';

/**
 * Reusable Revenue Chart Component
 *
 * @param {Object} config - Chart configuration
 * @param {string} config.title - Chart title
 * @param {string} config.subtitle - Chart subtitle (optional)
 * @param {string} config.apiAction - API action type: 'home' | 'report'
 * @param {Object} config.apiParams - Additional API parameters (for report mode)
 * @param {Function} config.onDataLoaded - Callback when data is loaded (optional)
 * @param {boolean} config.showDateRange - Show date range in subtitle
 */
const RevenueChart = ({
  config = {
    title: 'Doanh thu',
    apiAction: 'home', // 'home' | 'report'
    apiParams: {},
    showDateRange: false,
  },
}) => {
  const dispatch = useDispatch();
  const {width} = useWindowDimensions();

  // State management
  const [chart, setChart] = useState([]);
  const [sortedGameIds, setSortedGameIds] = useState([]);
  const [game, setGame] = useState([]);
  const [selectedData, setSelectedData] = useState(null);
  const [selectedGameFromLegend, setSelectedGameFromLegend] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const fadeAnim = useMemo(() => new Animated.Value(0), []);

  // Data fetching logic
  const fetchData = useCallback(() => {
    setIsLoading(true);

    // Choose API action based on config
    const chartAction =
      config.apiAction === 'home'
        ? OwnerActions.getOwnerChart
        : OwnerActions.getReportChart;

    dispatch(
      chartAction({
        onSuccess: rs => {
          const temp = formatData(rs?.data?.items);
          setChart(temp.data);
          setSortedGameIds(temp.sortedGameIds); // Store the sortedGameIds for correct mapping
          setIsLoading(false);

          // Animate chart appearance
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }).start();

          // Callback for parent component
          config.onDataLoaded?.(rs?.data);
        },
        onFail: () => {
          setIsLoading(false);
        },
        body: config.apiParams,
      }),
    );

    // Load games data
    dispatch(
      ReportActions.getGames({
        onSuccess: rs => {
          setGame(rs?.data || []);
        },
        onFail: () => {},
      }),
    );
  }, [
    dispatch,
    config.apiAction,
    config.apiParams,
    config.onDataLoaded,
    fadeAnim,
  ]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Transform data for custom chart
  const chartData = useMemo(() => {
    if (!chart.length || !game.length || !sortedGameIds.length) {
      return null;
    }

    // Get all dates and data
    const dates =
      chart[0]?.map(item => ({
        date: item.x,
        label: moment(item.x, 'YYYY-MM-DD').format('DD/MM'),
      })) || [];

    // Calculate max value for scaling
    let maxValue = 0;
    dates.forEach((_, dateIndex) => {
      let dayTotal = 0;
      chart.forEach(gameData => {
        dayTotal += gameData[dateIndex]?.y || 0;
      });
      maxValue = Math.max(maxValue, dayTotal);
    });

    // Transform data with CORRECT game mapping using sortedGameIds from formatData
    const transformedData = dates.map((dateInfo, dateIndex) => {
      const segments = [];
      let cumulativeValue = 0;

      chart.forEach((gameData, chartIndex) => {
        const value = gameData[dateIndex]?.y || 0;
        if (value > 0) {
          // FIXED: Use sortedGameIds from formatData for correct mapping
          const gameId = sortedGameIds[chartIndex];
          const gameInfo = game.find(g => parseInt(g.id) === gameId);

          if (gameInfo && gameInfo.color) {
            const gameIndex = game.findIndex(g => g.id === gameInfo.id);

            segments.push({
              value,
              startValue: cumulativeValue,
              endValue: cumulativeValue + value,
              color: gameInfo.color,
              gameName: gameInfo.name,
              gameIndex: gameIndex, // This is the index in the games array
              gameId: gameInfo.id,
            });
            cumulativeValue += value;
          }
        }
      });

      return {
        ...dateInfo,
        segments,
        total: cumulativeValue,
      };
    });

    return {
      data: transformedData,
      maxValue,
      colors: game.filter(x => !!x?.color).map(x => x.color),
    };
  }, [chart, game, sortedGameIds]);

  // Event handlers
  const handleBarPress = useCallback((segment, dateInfo) => {
    setSelectedData({
      date: dateInfo.label,
      value: segment.value,
      gameName: segment.gameName,
      gameColor: segment.color,
      total: dateInfo.total,
    });
    setSelectedGameFromLegend(null);

    // Auto-hide after 3 seconds
    setTimeout(() => {
      setSelectedData(null);
    }, 3000);
  }, []);

  const handleLegendPress = useCallback(
    (gameItem, gameIndex, event) => {
      if (!chartData) {
        return;
      }

      // Toggle functionality
      if (selectedGameFromLegend?.gameIndex === gameIndex) {
        setSelectedGameFromLegend(null);
        return;
      }

      // Calculate total revenue for this game across all days
      let totalGameRevenue = 0;
      const gameDataByDay = [];

      chartData.data.forEach(dayData => {
        const gameSegment = dayData.segments.find(
          segment => segment.gameIndex === gameIndex,
        );
        const dayRevenue = gameSegment ? gameSegment.value : 0;
        totalGameRevenue += dayRevenue;

        if (dayRevenue > 0) {
          gameDataByDay.push({
            date: dayData.label,
            value: dayRevenue,
          });
        }
      });

      setSelectedGameFromLegend({
        gameName: gameItem.name,
        gameColor: gameItem.color,
        totalRevenue: totalGameRevenue,
        dataByDay: gameDataByDay,
        gameIndex,
      });
      setSelectedData(null);
    },
    [chartData, selectedGameFromLegend],
  );

  const handleOutsidePress = useCallback(() => {
    setSelectedGameFromLegend(null);
    setSelectedData(null);
  }, []);

  // Generate subtitle with date range if needed
  const subtitle = useMemo(() => {
    console.log('📊 RevenueChart subtitle generation:');
    console.log('  - showDateRange:', config.showDateRange);
    console.log('  - event_time:', config.apiParams?.event_time);
    console.log('  - end_time:', config.apiParams?.end_time);
    console.log('  - config.subtitle:', config.subtitle);

    // If showDateRange is enabled, try to generate date range subtitle
    if (config.showDateRange) {
      const eventTime = config.apiParams?.event_time;
      const endTime = config.apiParams?.end_time;

      if (eventTime && endTime) {
        try {
          const startDate = moment(eventTime, 'YYYY-MM-DD');
          const endDate = moment(endTime, 'YYYY-MM-DD');

          if (startDate.isValid() && endDate.isValid()) {
            const dateRangeSubtitle = `(${startDate.format(
              'DD/MM/YYYY',
            )} - ${endDate.format('DD/MM/YYYY')})`;
            console.log('  - Generated dateRangeSubtitle:', dateRangeSubtitle);
            return dateRangeSubtitle;
          }
        } catch (error) {
          console.log('  - Error generating date range:', error);
        }
      }

      console.log(
        '  - Could not generate date range, falling back to config.subtitle',
      );
    }

    console.log('  - Returning config.subtitle:', config.subtitle);
    return config.subtitle;
  }, [
    config.showDateRange,
    config.apiParams?.event_time,
    config.apiParams?.end_time,
    config.subtitle,
  ]);

  // Render chart with loading and error states
  const renderChart = useMemo(() => {
    if (isLoading) {
      return (
        <View style={styles.container}>
          <LinearGradient colors={['#D30B0D', '#E53E3E']} style={styles.header}>
            <View style={styles.headerRow}>
              <MaterialCommunityIcons
                name="chart-bar"
                size={24}
                color={THEME.Color.white}
              />
              <Sys.Text style={styles.headerTitle}>{config.title}</Sys.Text>
            </View>
          </LinearGradient>
          <View style={styles.loadingContainer}>
            <MaterialCommunityIcons
              name="chart-line"
              size={48}
              color={THEME.Color.grayLight}
            />
            <Sys.Text style={styles.loadingText}>Đang tải dữ liệu...</Sys.Text>
          </View>
        </View>
      );
    }

    if (!chartData) {
      return (
        <View style={styles.container}>
          <LinearGradient colors={['#D30B0D', '#E53E3E']} style={styles.header}>
            <View style={styles.headerRow}>
              <MaterialCommunityIcons
                name="chart-bar"
                size={24}
                color={THEME.Color.white}
              />
              <Sys.Text style={styles.headerTitle}>{config.title}</Sys.Text>
            </View>
          </LinearGradient>
          <View style={styles.loadingContainer}>
            <MaterialCommunityIcons
              name="chart-off"
              size={48}
              color={THEME.Color.grayLight}
            />
            <Sys.Text style={styles.loadingText}>Không có dữ liệu</Sys.Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.rootContainer}>
        <Animated.View style={[styles.container, {opacity: fadeAnim}]}>
          <LinearGradient colors={['#D30B0D', '#E53E3E']} style={styles.header}>
            <View style={styles.headerRow}>
              <MaterialCommunityIcons
                name="chart-bar"
                size={24}
                color={THEME.Color.white}
              />
              <Sys.Text style={styles.headerTitle}>{config.title}</Sys.Text>
            </View>
            {(() => {
              console.log('📊 RevenueChart render - subtitle value:', subtitle);
              console.log(
                '📊 RevenueChart render - subtitle truthy:',
                !!subtitle,
              );
              return (
                subtitle && (
                  <Sys.Text style={styles.headerSubtitle}>{subtitle}</Sys.Text>
                )
              );
            })()}
          </LinearGradient>

          <TouchableOpacity
            style={styles.chartContainer}
            activeOpacity={1}
            onPress={handleOutsidePress}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={true}
              nestedScrollEnabled={true}
              contentContainerStyle={styles.chartScrollContainer}>
              <ModernStackedBarChart
                data={chartData}
                width={Math.max(width - 40, chartData.data.length * 80)}
                height={340}
                onBarPress={handleBarPress}
              />
            </ScrollView>

            {/* Modern Tooltip */}
            {selectedData && (
              <Animated.View style={styles.tooltipContainer}>
                <View
                  style={[
                    styles.tooltip,
                    {borderLeftColor: selectedData.gameColor},
                  ]}>
                  <View style={styles.tooltipHeader}>
                    <View
                      style={[
                        styles.tooltipColorDot,
                        {backgroundColor: selectedData.gameColor},
                      ]}
                    />
                    <Sys.Text style={styles.tooltipTitle}>
                      {selectedData.gameName}
                    </Sys.Text>
                  </View>
                  <Sys.Text style={styles.tooltipDate}>
                    📅 {selectedData.date}
                  </Sys.Text>
                  <Sys.Text
                    style={[
                      styles.tooltipValue,
                      {color: selectedData.gameColor},
                    ]}>
                    💰 {formatRevenue(selectedData.value)}
                  </Sys.Text>
                  {selectedData.total && (
                    <Sys.Text style={styles.tooltipTotal}>
                      📊 Tổng ngày: {formatRevenue(selectedData.total)}
                    </Sys.Text>
                  )}
                </View>
              </Animated.View>
            )}
          </TouchableOpacity>

          <LinearGradient
            colors={['#ffffff', '#f8f9fa']}
            style={styles.legendContainer}>
            <View style={styles.legendHeader}>
              <MaterialCommunityIcons
                name="palette-outline"
                size={20}
                color={THEME.Color.primary}
              />
              <Sys.Text style={styles.legendTitle}>Loại game</Sys.Text>
            </View>
            <View style={styles.legendGrid}>
              {game
                .filter(x => !!x?.color)
                .map((item, index) => {
                  const gameIndex = game.findIndex(g => g.id === item.id);
                  const isSelected =
                    selectedGameFromLegend?.gameIndex === gameIndex;

                  return (
                    <TouchableOpacity
                      key={`legend-${index}`}
                      style={[
                        styles.legendItem,
                        isSelected && styles.legendItemSelected,
                      ]}
                      activeOpacity={0.7}
                      onPress={event =>
                        handleLegendPress(item, gameIndex, event)
                      }>
                      <View
                        style={[
                          styles.legendColor,
                          {backgroundColor: item?.color},
                          isSelected && styles.legendColorSelected,
                        ]}
                      />
                      <Sys.Text
                        style={[
                          styles.legendText,
                          isSelected && styles.legendTextSelected,
                        ]}>
                        {item?.name}
                      </Sys.Text>
                      <MaterialCommunityIcons
                        name="information-outline"
                        size={16}
                        color={isSelected ? item?.color : THEME.Color.gray}
                        style={styles.legendIcon}
                      />
                    </TouchableOpacity>
                  );
                })}
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Game Summary Modal */}
        <GameSummaryModal
          visible={!!selectedGameFromLegend}
          data={selectedGameFromLegend}
          onClose={() => setSelectedGameFromLegend(null)}
        />
      </View>
    );
  }, [
    isLoading,
    chartData,
    game,
    selectedData,
    selectedGameFromLegend,
    fadeAnim,
    config,
    subtitle,
    width,
    handleBarPress,
    handleLegendPress,
    handleOutsidePress,
  ]);

  return renderChart;
};

// Modern Custom Stacked Bar Chart Component
const ModernStackedBarChart = ({data, width, height, onBarPress}) => {
  const padding = {top: 40, right: 25, bottom: 60, left: 65};
  const chartWidth = width - padding.left - padding.right;
  const chartHeight = height - padding.top - padding.bottom;
  const barWidth = Math.min(85, (chartWidth / data.data.length) * 0.85);
  const barSpacing = Math.max(
    12,
    (chartWidth - data.data.length * barWidth) / (data.data.length + 1),
  );

  // Y-axis scale
  const yScale = value => {
    return chartHeight - (value / data.maxValue) * chartHeight;
  };

  // Generate Y-axis labels
  const yAxisLabels = [];
  const steps = 5;
  for (let i = 0; i <= steps; i++) {
    const value = (data.maxValue / steps) * i;
    yAxisLabels.push({
      value,
      y: yScale(value),
      label: formatRevenue(value),
    });
  }

  return (
    <Svg width={width} height={height}>
      <Defs>
        {/* Gradient definitions for bars */}
        {data.colors.map((color, index) => (
          <SvgLinearGradient
            key={`gradient-${index}`}
            id={`gradient-${index}`}
            x1="0%"
            y1="0%"
            x2="0%"
            y2="100%">
            <Stop offset="0%" stopColor={color} stopOpacity="0.9" />
            <Stop offset="100%" stopColor={color} stopOpacity="0.7" />
          </SvgLinearGradient>
        ))}

        {/* Grid gradient */}
        <SvgLinearGradient id="gridGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <Stop offset="0%" stopColor="#E2E8F0" stopOpacity="0.3" />
          <Stop offset="50%" stopColor="#E2E8F0" stopOpacity="0.1" />
          <Stop offset="100%" stopColor="#E2E8F0" stopOpacity="0.3" />
        </SvgLinearGradient>
      </Defs>

      {/* Background */}
      <Rect
        x={0}
        y={0}
        width={width}
        height={height}
        fill="url(#gridGradient)"
        opacity={0.1}
      />

      {/* Grid lines */}
      <G>
        {yAxisLabels.map((label, index) => (
          <Line
            key={`grid-${index}`}
            x1={padding.left}
            x2={width - padding.right}
            y1={padding.top + label.y}
            y2={padding.top + label.y}
            stroke="#E2E8F0"
            strokeWidth={index === 0 ? 2 : 1}
            strokeDasharray={index === 0 ? 'none' : '4,4'}
            opacity={index === 0 ? 0.8 : 0.4}
          />
        ))}
      </G>

      {/* Y-axis labels */}
      <G>
        {yAxisLabels.map((label, index) => (
          <SvgText
            key={`y-label-${index}`}
            x={padding.left - 10}
            y={padding.top + label.y + 5}
            fontSize="11"
            fill="#64748B"
            textAnchor="end"
            fontFamily={THEME.FrontFamily['Roboto-Regular']}>
            {label.label}
          </SvgText>
        ))}
      </G>

      {/* Bars */}
      <G>
        {data.data.map((dayData, dayIndex) => {
          const x =
            padding.left + barSpacing + dayIndex * (barWidth + barSpacing);

          return (
            <G key={`day-${dayIndex}`}>
              {dayData.segments.map((segment, segmentIndex) => {
                const segmentHeight =
                  (segment.value / data.maxValue) * chartHeight;
                const segmentY = padding.top + yScale(segment.endValue);

                return (
                  <Rect
                    key={`segment-${dayIndex}-${segmentIndex}`}
                    x={x}
                    y={segmentY}
                    width={barWidth}
                    height={segmentHeight}
                    fill={segment.color}
                    stroke="white"
                    strokeWidth={2}
                    rx={4}
                    ry={4}
                    onPress={() => onBarPress(segment, dayData)}
                  />
                );
              })}
            </G>
          );
        })}
      </G>

      {/* Total labels on top of bars */}
      <G>
        {data.data.map((dayData, index) => {
          const x =
            padding.left +
            barSpacing +
            index * (barWidth + barSpacing) +
            barWidth / 2;
          const totalY = padding.top + yScale(dayData.total) - 8;

          return (
            <SvgText
              key={`total-label-${index}`}
              x={x}
              y={totalY}
              fontSize="12"
              fill="#1E293B"
              textAnchor="middle"
              fontFamily={THEME.FrontFamily['Roboto-Bold']}
              fontWeight="700">
              {formatRevenue(dayData.total)}
            </SvgText>
          );
        })}
      </G>

      {/* X-axis labels */}
      <G>
        {data.data.map((dayData, index) => {
          const x =
            padding.left +
            barSpacing +
            index * (barWidth + barSpacing) +
            barWidth / 2;
          return (
            <SvgText
              key={`x-label-${index}`}
              x={x}
              y={height - padding.bottom + 25}
              fontSize="14"
              fill="#334155"
              textAnchor="middle"
              fontFamily={THEME.FrontFamily['Roboto-Bold']}
              fontWeight="700">
              {dayData.label}
            </SvgText>
          );
        })}
      </G>

      {/* Axis lines */}
      <G>
        {/* Y-axis */}
        <Line
          x1={padding.left}
          x2={padding.left}
          y1={padding.top}
          y2={height - padding.bottom}
          stroke="#334155"
          strokeWidth={2}
        />

        {/* X-axis */}
        <Line
          x1={padding.left}
          x2={width - padding.right}
          y1={height - padding.bottom}
          y2={height - padding.bottom}
          stroke="#334155"
          strokeWidth={2}
        />
      </G>
    </Svg>
  );
};

// Game Summary Modal Component
const GameSummaryModal = ({visible, data, onClose}) => {
  if (!data) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={modalStyles.overlay}>
        <TouchableOpacity
          style={modalStyles.overlayTouchable}
          activeOpacity={1}
          onPress={onClose}
        />
        <View style={modalStyles.container}>
          <View
            style={[
              modalStyles.content,
              {borderLeftColor: data.gameColor || THEME.Color.primary},
            ]}>
            {/* Modal Header */}
            <View style={modalStyles.header}>
              <View style={modalStyles.titleContainer}>
                <View
                  style={[
                    modalStyles.colorDot,
                    {backgroundColor: data.gameColor || THEME.Color.primary},
                  ]}
                />
                <Sys.Text style={modalStyles.title}>
                  {data.gameName || ''}
                </Sys.Text>
              </View>
              <TouchableOpacity
                style={modalStyles.closeButton}
                onPress={onClose}>
                <MaterialCommunityIcons
                  name="close"
                  size={24}
                  color={THEME.Color.gray}
                />
              </TouchableOpacity>
            </View>

            {/* Total Revenue */}
            <View style={modalStyles.totalContainer}>
              <Sys.Text
                style={[
                  modalStyles.totalText,
                  {color: data.gameColor || THEME.Color.primary},
                ]}>
                🎯 Tổng doanh thu: {formatRevenue(data.totalRevenue || 0)}
              </Sys.Text>
            </View>

            <View style={modalStyles.divider} />

            {/* Scrollable Data List */}
            <View style={modalStyles.dataContainer}>
              <Sys.Text style={modalStyles.dataTitle}>
                📈 Chi tiết theo ngày ({data.dataByDay?.length || 0} ngày):
              </Sys.Text>
              <ScrollView
                style={modalStyles.scrollView}
                contentContainerStyle={modalStyles.scrollContent}
                nestedScrollEnabled={true}
                showsVerticalScrollIndicator={true}
                bounces={true}
                scrollEventThrottle={16}
                keyboardShouldPersistTaps="handled">
                {data.dataByDay?.map((item, index) => (
                  <View key={index} style={modalStyles.dataRow}>
                    <Sys.Text style={modalStyles.dataDate}>
                      📅 {item.date}
                    </Sys.Text>
                    <Sys.Text
                      style={[
                        modalStyles.dataValue,
                        {color: data.gameColor || THEME.Color.primary},
                      ]}>
                      💰 {formatRevenue(item.value)}
                    </Sys.Text>
                  </View>
                ))}
              </ScrollView>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

// formatData function - moved to shared utility
export function formatData(data) {
  let result = [];
  let maxRevenue = 0;
  let maxCompany = null;

  // Sort dates chronologically (oldest first)
  let sortedKeys = Object.keys(data).sort((a, b) => {
    const dateA = moment(a, 'YYYY-MM-DD');
    const dateB = moment(b, 'YYYY-MM-DD');
    return dateA.valueOf() - dateB.valueOf();
  });

  // Get all unique game IDs first
  const allGameIds = new Set();
  Object.values(data).forEach(dayData => {
    Object.keys(dayData.items).forEach(gameId => {
      allGameIds.add(parseInt(gameId));
    });
  });

  const sortedGameIds = Array.from(allGameIds).sort((a, b) => a - b);

  // Initialize result arrays for each game
  sortedGameIds.forEach((gameId, index) => {
    result[index] = [];
  });

  // Process dates in chronological order
  sortedKeys.forEach(dateKey => {
    let dayData = data[dateKey];
    let items = dayData.items;
    let totalDayRevenue = 0;

    // Process each game for this date
    sortedGameIds.forEach((gameId, gameIndex) => {
      const gameIdStr = gameId.toString();
      if (items[gameIdStr]) {
        let revenue = parseInt(items[gameIdStr].total_revenue) || 0;
        totalDayRevenue += revenue;

        result[gameIndex].push({
          x: dateKey,
          y: revenue,
          gameId: gameId, // Add gameId to track which game this data belongs to
        });
      } else {
        // Add zero revenue for missing games to maintain chart structure
        result[gameIndex].push({
          x: dateKey,
          y: 0,
          gameId: gameId, // Add gameId to track which game this data belongs to
        });
      }
    });

    if (totalDayRevenue > maxRevenue) {
      maxRevenue = totalDayRevenue;
      maxCompany = dayData;
    }
  });

  return {
    data: result,
    sortedGameIds, // Return sortedGameIds so we can use it for mapping
    maxCompany: {
      company: maxCompany,
      totalRevenue: maxRevenue,
    },
  };
}

// Styles
const styles = StyleSheet.create({
  rootContainer: {
    flex: 1,
    position: 'relative',
  },
  container: {
    backgroundColor: THEME.Color.white,
    marginHorizontal: 8,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.12,
    shadowRadius: 20,
    elevation: 8,
    overflow: 'visible',
    marginVertical: 8,
    position: 'relative',
  },
  loadingContainer: {
    paddingVertical: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: THEME.Color.gray,
    marginTop: 12,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
  },
  header: {
    flexDirection: 'column',
    paddingHorizontal: 24,
    paddingVertical: 18,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderRadius: 20,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 19,
    fontWeight: '700',
    color: THEME.Color.white,
    marginLeft: 12,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.Color.white,
    marginLeft: 36,
    marginTop: 6,
    marginRight: 16,
    opacity: 0.9,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
  },
  chartContainer: {
    paddingVertical: 24,
    backgroundColor: THEME.Color.white,
    position: 'relative',
  },
  chartScrollContainer: {
    paddingHorizontal: 8,
  },
  tooltipContainer: {
    position: 'absolute',
    top: 20,
    right: 20,
    zIndex: 1000,
  },
  tooltip: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    borderLeftWidth: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    minWidth: 180,
    maxWidth: 220,
  },
  tooltipHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tooltipColorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  tooltipTitle: {
    fontSize: 15,
    fontWeight: '700',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    flex: 1,
  },
  tooltipDate: {
    fontSize: 13,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginBottom: 6,
  },
  tooltipValue: {
    fontSize: 14,
    fontWeight: '700',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginBottom: 4,
  },
  tooltipTotal: {
    fontSize: 12,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    fontStyle: 'italic',
  },
  legendContainer: {
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  legendHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  legendTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.Color.text,
    marginLeft: 10,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    letterSpacing: 0.3,
  },
  legendGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingTop: 8,
  },
  legendItem: {
    width: '50%',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 2,
  },
  legendText: {
    color: THEME.Color.text,
    fontSize: 14,
    fontWeight: '500',
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    flex: 1,
  },
  legendIcon: {
    marginLeft: 4,
  },
  legendItemSelected: {
    backgroundColor: 'rgba(211, 11, 13, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(211, 11, 13, 0.3)',
  },
  legendColorSelected: {
    transform: [{scale: 1.2}],
    shadowOpacity: 0.3,
  },
  legendTextSelected: {
    fontWeight: '700',
    color: THEME.Color.primary,
  },
});

// Modal styles
const modalStyles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  overlayTouchable: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  container: {
    width: '100%',
    maxWidth: 400,
    maxHeight: '85%',
    zIndex: 1,
  },
  content: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    borderLeftWidth: 6,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 10},
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
    maxHeight: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  colorDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    flex: 1,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  totalContainer: {
    alignItems: 'center',
    paddingVertical: 16,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 12,
    marginBottom: 16,
  },
  totalText: {
    fontSize: 18,
    fontWeight: '800',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    textAlign: 'center',
  },
  divider: {
    height: 1,
    backgroundColor: '#E2E8F0',
    marginVertical: 16,
  },
  dataContainer: {
    height: 300,
  },
  dataTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginBottom: 12,
  },
  scrollView: {
    height: 250,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  scrollContent: {
    padding: 8,
    paddingBottom: 16,
  },
  dataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
    backgroundColor: 'white',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.08)',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  dataDate: {
    fontSize: 14,
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    fontWeight: '500',
  },
  dataValue: {
    fontSize: 14,
    fontWeight: '700',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
});

export default RevenueChart;
