import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useDispatch, useSelector} from 'react-redux';
import {AppActions, AppSelectors} from '../app.slice';
import Sys from '../components/Sys';

const FeatureUnderDevelopmentModal = () => {
  const isFeatureInDevelopment = useSelector(AppSelectors.isDev);
  const dispatch = useDispatch();

  const handleClose = () => {
    dispatch(AppActions.setDev(false));
  };

  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 20,
    },
    modalContent: {
      width: '100%',
      backgroundColor: '#ffffff',
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 50,
      shadowOpacity: 0.25,
      shadowRadius: 3.5,
      elevation: 5,
      shadowOffset: {width: 0, height: 2},
      shadowColor: '#000',
    },
    modalText: {
      fontSize: 14,
      textAlign: 'center',
      color: '#000000',
      marginBottom: 30,
      textTransform: 'uppercase',
      fontWeight: 'bold',
    },
    closeButton: {
      backgroundColor: '#0057d9',
      paddingHorizontal: 20,
      paddingVertical: 10,
      borderRadius: 10,
      marginTop: 10,
      shadowOpacity: 0.2,
      shadowRadius: 3,
      shadowOffset: {width: 0, height: 2},
      shadowColor: '#000',
      elevation: 2,
    },
    closeButtonText: {
      color: 'white',
      fontSize: 16,
    },
  });

  return (
    <Modal
      isVisible={isFeatureInDevelopment}
      backdropOpacity={0.5}
      animationIn="zoomIn"
      animationOut="zoomOut"
      backdropTransitionInTiming={600}
      backdropTransitionOutTiming={600}
      useNativeDriver={true}
      onBackdropPress={handleClose}
      hideModalContentWhileAnimating={true}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Sys.Text style={styles.modalText}>
            Tính năng này hiện đang được phát triển
          </Sys.Text>
          <Sys.Text style={[styles.modalText, {textTransform: 'none'}]}>
            Vui lòng kiểm tra lại sau!
          </Sys.Text>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Sys.Text style={styles.closeButtonText}>Đóng</Sys.Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default FeatureUnderDevelopmentModal;
