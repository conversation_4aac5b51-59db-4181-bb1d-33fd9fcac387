import React from 'react';
import {KeyboardAvoidingView, Platform, SafeAreaView} from 'react-native';
import * as Ant from 'react-native-animatable';
import THEME from './theme/theme';
const ContainerComponent = ({
  children,
  hasHeader = false,
  hasFooter = false,
  headerColor = '#001451',
  footerColor = '#001451',
  backgroundColor = THEME.Color.backgroundColor,
}) => {
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{flex: 1}}>
      <Ant.View
        animation={'fadeIn'}
        style={{
          flex: 1,
        }}>
        {hasHeader && (
          <SafeAreaView
            style={{
              backgroundColor: headerColor || THEME.Color.backgroundColor,
            }}
          />
        )}
        <SafeAreaView
          style={{
            flex: 1,
            backgroundColor: backgroundColor,
          }}>
          {children}
        </SafeAreaView>
        {hasFooter && (
          <SafeAreaView
            style={{
              backgroundColor: footerColor || THEME.Color.backgroundColor,
            }}
          />
        )}
      </Ant.View>
    </KeyboardAvoidingView>
  );
};

export default ContainerComponent;
