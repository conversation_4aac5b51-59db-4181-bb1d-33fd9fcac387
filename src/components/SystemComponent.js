import React, {useEffect} from 'react';
import Toast, {BaseToast, ErrorToast} from 'react-native-toast-message';
import TouchID from 'react-native-touch-id';
import {useDispatch} from 'react-redux';
import {AppActions} from '../app.slice';
import {AccountActions} from '../screen/account/services/account.slice';
import GlobalLoading from './GlobalLoading';
import FeatureUnderDevelopmentModal from './GlobalFeatureUnderDevelopmentModal';
import {View} from 'react-native';
import Sys from '../components/Sys';
import ConnectionAlert from './ConnectionAlert';
import StorageService from '../services/storageService';

const SystemComponent = () => {
  const dispatch = useDispatch();

  const toastConfig = {
    /*
      Overwrite 'success' type,
      by modifying the existing `BaseToast` component
    */
    success: props => (
      <BaseToast
        {...props}
        contentContainerStyle={{paddingHorizontal: 15}}
        style={{
          borderLeftColor: '#90BB3B',
        }}
        text1Style={{marginBottom: 5, fontSize: 16}}
        text2Style={{fontSize: 14}}
      />
    ),
    /*
      Overwrite 'error' type,
      by modifying the existing `ErrorToast` component
    */
    error: props => (
      <ErrorToast
        {...props}
        contentContainerStyle={{paddingHorizontal: 15}}
        text1Style={{marginBottom: 5, fontSize: 16}}
        text2Style={{
          fontSize: 14,
          flex: 1,
          flexWrap: 'wrap',
        }}
        style={{
          borderLeftColor: 'red',
          minHeight: 60,
          justifyContent: 'center',
        }}
      />
    ),
    /*
      Or create a completely new type - `tomatoToast`,
      building the layout from scratch.

      I can consume any custom `props` I want.
      They will be passed when calling the `show` method (see below)
    */
    tomatoToast: ({text1, props}) => (
      <View style={{height: 60, width: '100%', backgroundColor: 'tomato'}}>
        <Sys.Text>{text1}</Sys.Text>
        <Sys.Text>{props.uuid}</Sys.Text>
      </View>
    ),
  };

  //check bio
  useEffect(() => {
    initData();
    onCheckBio();
  }, []);

  const initData = async () => {
    try {
      const bioToken = await StorageService.getAccessToken();
      dispatch(AppActions.setBioToken(bioToken));
    } catch (error) {}

    try {
      const un = await StorageService.getUserName();
      dispatch(AccountActions.setUsername(un));
    } catch (error) {}

    try {
      const a = await StorageService.getUserAvatar();
      dispatch(AccountActions.setAvatar(a));
    } catch (error) {}
  };

  const onCheckBio = async () => {
    try {
      const bioTypeCheck = await TouchID.isSupported({
        unifiedErrors: false,
        passcodeFallback: false,
      });
      dispatch(AppActions.setBioType(bioTypeCheck));
    } catch (error) {
      console.log({error});
      dispatch(AppActions.setBioToken(''));
    }
  };
  return (
    <>
      <GlobalLoading />
      <FeatureUnderDevelopmentModal />
      <ConnectionAlert />
      <Toast config={toastConfig} />
    </>
  );
};

export default SystemComponent;
