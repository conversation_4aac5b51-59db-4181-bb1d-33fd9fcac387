import {put, takeEvery} from 'redux-saga/effects';
import {AppActions} from './app.slice';
import SysFetch from './services/fetch';

function* AppSaga() {
  yield takeEvery(AppActions.getSettings, getSettings);
  yield takeEvery(AppActions.fetchChartDay, fetch);
  yield takeEvery(AppActions.fetchChartHour, fetch);
  yield takeEvery(AppActions.fetchChartMonth, fetch);
  yield takeEvery(AppActions.fetchChartQuarter, fetch);
  yield takeEvery(AppActions.fetch, fetch);
  yield takeEvery(AppActions.fetchPost, fetchPost);
  yield takeEvery(AppActions.getChart, getChart);
}

export default AppSaga;
function* getChart({payload}) {
  const {onSuccess, onFail, posId, startDate, endDate} = payload;
  try {
    const rs = yield SysFetch.get('report/pos/opening', {
      pos_ids: [posId],
      start_date: startDate,
      end_date: endDate,
    });

    onSuccess && onSuccess(rs);
  } catch (error) {
    // console.log(error);
    // if (onFail) {
    //   onFail(error);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* fetchPost({payload}) {
  const {onSuccess, onFail, url, body} = payload;
  try {
    const rs = yield SysFetch.post(`${url}`, body);
    onSuccess && onSuccess(rs);
  } catch (error) {
    // console.log(error);
    // if (onFail) {
    //   onFail(error);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* fetch({payload}) {
  const {onSuccess, onFail, url, params = {}} = payload;
  try {
    const rs = yield SysFetch.get(`${url}`, params);
    onSuccess && onSuccess(rs);
  } catch (error) {
    // console.log(error);
    // if (onFail) {
    //   onFail(error);
    // } else {
    //   Toast.show({
    //     type: 'error',
    //     text1: 'Lỗi',
    //     text2: error.toString(),
    //   });
    // }
  }
}

function* getSettings() {
  try {
    const rs = yield SysFetch.get('settings');
    yield put(AppActions.setSettings(rs));
  } catch (error) {}
}
