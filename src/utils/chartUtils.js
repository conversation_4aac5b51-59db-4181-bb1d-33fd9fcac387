import moment from 'moment';

/**
 * Format API data for chart display
 * @param {Object} data - API response data items
 * @param {Array} gamesArray - Array of games from games API (optional)
 * @returns {Object} Formatted chart data with complete game structure
 */
export function formatData(data, gamesArray = null) {
  let result = [];
  let maxRevenue = 0;
  let maxCompany = null;

  // Sort dates chronologically (oldest first)
  let sortedKeys = Object.keys(data).sort((a, b) => {
    const dateA = moment(a, 'YYYY-MM-DD');
    const dateB = moment(b, 'YYYY-MM-DD');
    return dateA.valueOf() - dateB.valueOf();
  });

  console.log('Sorted date keys:', sortedKeys);

  // FIXED: Get all game IDs from games array if provided
  let allGameIds = new Set();
  
  if (gamesArray && Array.isArray(gamesArray)) {
    console.log('=== USING GAMES ARRAY FOR COMPLETE GAME ID COLLECTION ===');
    gamesArray.forEach(game => {
      allGameIds.add(parseInt(game.id));
    });
    console.log('Games array provided, collected all game IDs:', Array.from(allGameIds));
  } else {
    console.log('=== FALLBACK: COLLECTING FROM REVENUE DATA ONLY ===');
    Object.values(data).forEach(dayData => {
      Object.keys(dayData.items).forEach(gameId => {
        allGameIds.add(parseInt(gameId));
      });
    });
    console.log('No games array, collected from revenue data only:', Array.from(allGameIds));
  }

  const sortedGameIds = Array.from(allGameIds).sort((a, b) => a - b);
  console.log('All collected game IDs:', allGameIds);
  console.log('Sorted game IDs:', sortedGameIds);

  // Initialize result arrays for each game
  sortedGameIds.forEach((gameId, index) => {
    result[index] = [];
  });

  // Process dates in chronological order
  sortedKeys.forEach(dateKey => {
    let dayData = data[dateKey];
    let items = dayData.items;
    let totalDayRevenue = 0;

    // Process each game for this date
    console.log(`Processing date ${dateKey}, available games:`, Object.keys(items));
    sortedGameIds.forEach((gameId, gameIndex) => {
      const gameIdStr = gameId.toString();
      if (items[gameIdStr]) {
        let revenue = parseInt(items[gameIdStr].total_revenue) || 0;
        totalDayRevenue += revenue;
        console.log(`Game ${gameId} (index ${gameIndex}): ${revenue}`);

        result[gameIndex].push({
          x: dateKey,
          y: revenue,
        });
      } else {
        // Add zero revenue for missing games to maintain chart structure
        console.log(`Game ${gameId} (index ${gameIndex}): 0 (missing)`);
        result[gameIndex].push({
          x: dateKey,
          y: 0,
        });
      }
    });
    console.log(`Total day revenue for ${dateKey}: ${totalDayRevenue}`);

    if (totalDayRevenue > maxRevenue) {
      maxRevenue = totalDayRevenue;
      maxCompany = dayData;
    }
  });

  console.log('Final result data:', result);

  return {
    data: result,
    maxCompany: {
      company: maxCompany,
      totalRevenue: maxRevenue,
    },
  };
}

/**
 * Format revenue value for display
 * @param {number} value - Revenue value
 * @returns {string} Formatted revenue string
 */
export const formatRevenue = value => {
  if (value >= 1000000000) {
    return `${(value / 1000000000).toFixed(1)}B`;
  }
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  }
  if (value >= 1000) {
    return `${(value / 1000).toFixed(0)}K`;
  }
  return value.toString();
};
