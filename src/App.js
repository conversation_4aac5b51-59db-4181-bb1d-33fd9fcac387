import {NavigationContainer} from '@react-navigation/native';
import React, {Component} from 'react';
import InitialScreen from './components/InitialScreen';
import {Provider} from 'react-redux';
import {store} from './services/store';
import SystemComponent from './components/SystemComponent';

import {LogBox} from 'react-native';
import {LocaleConfig} from 'react-native-calendars';
import {navigationRef} from './services/navigation';
import moment from 'moment';

LogBox.ignoreLogs(['Warning: ...']); // Ignore log notification by message
LogBox.ignoreAllLogs(); //Ignore all log notifications

LocaleConfig.locales.vi = {
  monthNames: [
    'Tháng 1',
    'Tháng 2',
    'Tháng 3',
    'Tháng 4',
    'Tháng 5',
    'Tháng 6',
    'Tháng 7',
    'Tháng 8',
    'Tháng 9',
    'Tháng 10',
    'Tháng 11',
    'Tháng 12',
  ],
  monthNamesShort: [
    'Th 1',
    'Th 2',
    'Th 3',
    'Th 4',
    'Th 5',
    'Th 6',
    'Th 7',
    'Th 8',
    'Th 9',
    'Th 10',
    'Th 11',
    'Th 12',
  ],
  dayNames: ['Chủ Nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'],
  dayNamesShort: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
  today: 'Hôm nay',
};
LocaleConfig.defaultLocale = 'vi';
require('moment/locale/vi');
moment.locale('vi');

export class App extends Component {
  render() {
    return (
      <Provider store={store}>
        <NavigationContainer ref={navigationRef}>
          <InitialScreen />
          <SystemComponent />
        </NavigationContainer>
      </Provider>
    );
  }
}

export default App;
