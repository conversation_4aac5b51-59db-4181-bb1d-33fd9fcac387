import {Alert, Dimensions, Linking, Platform} from 'react-native';
import ImageResizer from 'react-native-image-resizer';
import moment from 'moment';
import {check, PERMISSIONS, request, RESULTS} from 'react-native-permissions';
import _ from 'lodash';
import Color from '../components/theme/Color';
import DeviceInfo, {
  getDeviceId,
  getSystemVersion,
} from 'react-native-device-info';
import messaging from '@react-native-firebase/messaging';

const {width, height} = Dimensions.get('window');
const aspectRatio = height / width;

export const isNewerSmartphone = aspectRatio > 1.8;

export const isModerateAspectRatio = aspectRatio > 1.6 && aspectRatio <= 1.8;

export const isShorterOrWiderScreen = aspectRatio <= 1.6;

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function hasVietlottDomain(email) {
  return email.endsWith('@vietlott.com');
}

function formatVND(number) {
  if (isNaN(number)) {
    return '--';
  }
  var formattedNumber = new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(number);
  return formattedNumber.replace(/\s*₫$/, '');
}

const formatVNDForChart = amount => {
  if (!amount) {
    return 0;
  }
  return `${(amount / 1e9).toFixed(2)}`;
  // if (amount >= 1e9) {
  //   return `${(amount / 1e9).toFixed(2)}`;
  // } else if (amount >= 1e8) {
  //   return `${(amount / 1e9).toFixed(2)}`;
  // } else {
  //   return `${(amount / 1e6).toFixed(2)}`;
  // }
};
export {isValidEmail, hasVietlottDomain, formatVND, formatVNDForChart};

export const isIOS = Platform.OS === 'ios';

export const isAndroid = Platform.OS === 'android';

export const getCameraHeight = () => {
  if (aspectRatio > 1.8) {
    return isIOS ? height / 2.4 : height / 2;
  } else {
    return isIOS ? height / 2 : height / 1.8;
  }
};

export async function resizeAndCompressImage(
  photoUri,
  maxWidth,
  maxHeight,
  quality,
  convertToWebP = false,
  orientation = 'portrait',
) {
  try {
    // Set format based on platform and desired conversion flag
    const format = convertToWebP && Platform.OS === 'android' ? 'WEBP' : 'JPEG';
    console.log('orientation', orientation);
    // Trongnd orientation portrait
    let rotationAngle = 0;
    switch (orientation) {
      // case 'landscape-right':
      //   rotationAngle = -90;
      //   break;
      case 'portrait':
        rotationAngle = 90;
        break;
      // case 'portrait-upside-down':
      //   rotationAngle = 180;
      //   break;
      case 'landscape-left':
      default:
        rotationAngle = 0;
    }

    const resizedImage = await ImageResizer.createResizedImage(
      photoUri,
      maxWidth,
      maxHeight,
      format,
      quality,
      rotationAngle, // Apply calculated rotation
      undefined, // outputPath
      false, // keepMetaData
      {mode: 'contain', onlyScaleDown: true},
    );

    console.log(`Resized image path: ${resizedImage.uri}`);
    return {
      uri: resizedImage.uri,
      width: resizedImage.width,
      height: resizedImage.height,
    };
  } catch (error) {
    console.error('Failed to resize and compress image', error);
    throw error;
  }
}

/**
 * Formats distance based on the value.
 * @param {number} distance - The distance in meters.
 * @returns {string} - The formatted distance in meters or kilometers.
 */
export function formatDistance(distance) {
  if (distance >= 1000000) {
    return `${(distance / 1000000).toFixed(1)}k km`; // Converts to thousands of kilometers and keeps one decimal
  } else if (distance >= 1000) {
    return `${(distance / 1000).toFixed(1)} km`; // Converts to kilometers and keeps one decimal
  }
  return `${Math.round(distance)} m`; // Keeps the distance in meters
}

export function formatDistanceKm(distance) {
  try {
    if (distance < 1) {
      return `${Math.round(distance * 1000)} m`;
    }
    return `${distance.toFixed(3)} km`;
  } catch (error) {
    return '';
  }
}

export const formatMoney = amount => {
  if (!amount) {
    return 0;
  }
  if (amount >= 1e9) {
    return `${(amount / 1e9).toFixed(2)} Tỷ`;
  }
  // else if (amount >= 1e8) {
  //   return `${(amount / 1e8).toFixed(2)} Trăm triệu`;
  // }
  else {
    return `${(amount / 1e6).toFixed(2)} Triệu`;
  }
};
export const formatDate = (date, dateTime = false) => {
  if (!date) {
    return null;
  }
  return moment(date, 'YYYY-MM-DD HH:mm:ss').format(
    dateTime ? 'DD/MM/YYYY HH:mm:ss' : 'DD/MM/YYYY',
  );
};

export function timeSince(date) {
  const pastDate = moment(date, 'YYYY-MM-DD HH:mm:ss');
  const currentDate = moment();
  const minutes = currentDate.diff(pastDate, 'minutes');
  const hours = currentDate.diff(pastDate, 'hours');
  const days = currentDate.diff(pastDate, 'days');
  const months = currentDate.diff(pastDate, 'months');

  if (months > 0) {
    return months === 1 ? '1 tháng trước' : `${months} tháng trước`;
  } else if (days > 0) {
    return days === 1 ? '1 ngày trước' : `${days} ngày trước`;
  } else if (hours > 0) {
    return hours === 1 ? '1 giờ trước' : `${hours} giờ trước`;
  } else if (minutes > 0) {
    return minutes === 1 ? '1 phút trước' : `${minutes} phút trước`;
  } else {
    return 'Vài giây trước';
  }
}

/**
 * // Inside your component
 * useEffect(() => {
 *   const handleCameraPermission = async () => {
 *     const hasPermission = await checkAndRequestCameraPermission();
 *     setCameraPermissionGranted(hasPermission);
 *   };
 *
 *   handleCameraPermission();
 * }, []);
 * */
export const checkAndRequestCameraPermission = async () => {
  const cameraPermission = Platform.select({
    ios: PERMISSIONS.IOS.CAMERA,
    android: PERMISSIONS.ANDROID.CAMERA,
  });

  let permissionStatus = await check(cameraPermission);
  if (permissionStatus === RESULTS.GRANTED) {
    return true;
  } else {
    permissionStatus = await request(cameraPermission);
    if (permissionStatus === RESULTS.GRANTED) {
      return true;
    } else {
      Alert.alert(
        'Thông báo',
        'Vui lòng cấp quyền camera cho ứng dụng để tiếp tục sử dụng tính năng này.',
        [
          {
            text: 'Đóng',
            onPress: () => {},
          },
          {
            text: 'Tới cài đặt',
            onPress: () => Linking.openSettings(),
          },
        ],
      );
      return false;
    }
  }
};

/**
 * Sắp xếp một mảng các đối tượng theo trường ngày trong `plan`.
 * Các đối tượng không có ngày sẽ được đặt lên đầu.
 * @param {Array} items Mảng các đối tượng để sắp xếp.
 * @return {Array} Mảng đã được sắp xếp.
 */
/**
 * Sắp xếp một mảng các đối tượng theo trường ngày được chỉ định.
 * Các đối tượng không có trường ngày được chỉ định sẽ được đặt lên đầu.
 * @param {Array} items Mảng các đối tượng để sắp xếp.
 * @param {String} dateField Tên trường chứa ngày để sắp xếp.
 * @return {Array} Mảng đã được sắp xếp.
 */

export const sortByDateAscending = (items, dateField = 'plan.date') => {
  if (!items || items.length === 0) {
    return [];
  }
  return items.sort((a, b) => {
    const dateA = _.get(a, dateField)
      ? new Date(_.get(a, dateField))
      : new Date(0);
    const dateB = _.get(b, dateField)
      ? new Date(_.get(b, dateField))
      : new Date(0);

    return dateA - dateB;
  });
};

export const getColorByThreshold = (
  amount,
  threshold,
  defaultColor = Color.blue,
) => {
  return amount <= threshold ? Color.red : defaultColor;
};

export const getColorByDaysDifference = date => {
  const currentDate = moment().startOf('day');
  const pastDate = moment(date, 'YYYY-MM-DD HH:mm:ss').startOf('day');
  const daysDifference = currentDate.diff(pastDate, 'days');
  let color = Color.red;
  const value = date ? daysDifference || 'Hôm nays' : null;

  if (daysDifference >= 90 && daysDifference <= 120) {
    color = Color.orange;
  }
  if (daysDifference < 90) {
    color = Color.blue;
  }
  return {
    color,
    value,
  };
};

/**
 * Delays the execution of the next line of code.
 * @param {number} ms - The time to delay in milliseconds.
 * @return {Promise} A promise that resolves after the specified delay.
 */
export const delay = (ms = 100) =>
  new Promise(resolve => setTimeout(resolve, ms));

export const getDeviceInfo = async () => {
  const device_id = `${getDeviceId()} ${getSystemVersion()}`;
  const device_name = await DeviceInfo.getDeviceName();
  const deviceUniqueId = await DeviceInfo.getUniqueId();
  let device_token = '';

  try {
    device_token = await messaging().getToken();
  } catch (error) {
    console.error('Error getting FCM Token:', error);
  }

  return {device_id, device_token, device_name, deviceUniqueId};
};

export const formatLargeNumber = num => {
  if (Math.abs(num) >= 1.0e9) {
    return (num / 1.0e9).toFixed(1) + 'B';
  } else if (Math.abs(num) >= 1.0e6) {
    return (num / 1.0e6).toFixed(1) + 'M';
  } else if (Math.abs(num) >= 1.0e3) {
    return (num / 1.0e3).toFixed(1) + 'K';
  } else {
    return num;
  }
};
