import {combineReducers} from '@reduxjs/toolkit';
import AppReducer from '../app.slice';
import AccountReducers from '../screen/account/services/account.slice';
import LoginReducers from '../screen/login/services/login.slice';
import ReportReducers from '../screen/report/services/report.slice';
import PosReducers from '../screen/sale-location/services/pos.slice';
import SurveyReducers from '../screen/survey/service/survey.slice';
import InventorySlice from '../screen/device-list/services/inventory.slice';

import SupportReducers from '../screen/message-support/services/support.slice';
import OwnerReducer from '../screen/owner/services/slice';
import BranchManagerReducer from '../screen/branch-manager-home/services/slide';
import NotificationReducers from '../screen/notification/services/notification.slice';

export const reducers = combineReducers({
  app: AppReducer,
  login: LoginReducers,
  account: AccountReducers,
  pos: PosReducers,
  survey: SurveyReducers,
  report: ReportReducers,
  support: SupportReducers,
  notify: NotificationReducers,
  inventory: InventorySlice,
  owner: OwnerReducer,
  branchManager: BranchManagerReducer,
});
