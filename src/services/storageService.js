// storageService.js
import SysStorage from './storage';
import CONST from './const.js';
import * as _ from 'lodash';
import {AppActions} from '../app.slice';
import {store} from './store';

class StorageService {
  static async getFilters() {
    const filter = await SysStorage(CONST.STORAGE.GLOBAL_FILTER).get();
    return JSON.parse(filter);
  }

  static async setFilter(value) {
    return await SysStorage(CONST.STORAGE.GLOBAL_FILTER).set(
      JSON.stringify(value),
    );
  }

  static async resetFilter() {
    return await SysStorage(CONST.STORAGE.GLOBAL_FILTER).set(undefined);
  }

  static async getUserDName() {
    return await SysStorage(CONST.STORAGE.USER_DISPLAY_NAME).get();
  }

  static async setUserDName(value) {
    return await SysStorage(CONST.STORAGE.USER_DISPLAY_NAME).set(value);
  }

  static async getUserName() {
    return await SysStorage(CONST.STORAGE.USERNAME).get();
  }

  static async setUserName(value) {
    return await SysStorage(CONST.STORAGE.USERNAME).set(value);
  }

  static async getUserAvatar() {
    return await SysStorage(CONST.STORAGE.AVATAR).get();
  }

  static async setUserAvatar(value) {
    return await SysStorage(CONST.STORAGE.AVATAR).set(value);
  }

  static async getBio() {
    const currentBioCheck = await SysStorage(CONST.STORAGE.BIO_ENABLE).get();
    return _.isBoolean(currentBioCheck) && currentBioCheck;
  }

  static async setBioEnable(value = false) {
    return await SysStorage(CONST.STORAGE.BIO_ENABLE).set(value);
  }

  static async setDeviceToken(value) {
    return await SysStorage(CONST.STORAGE.DEVICE_TOKEN).set(value);
  }

  static async getDeviceToken() {
    return await SysStorage(CONST.STORAGE.DEVICE_TOKEN).get();
  }

  static async getAccessToken() {
    return await SysStorage(CONST.STORAGE.ACCESS_TOKEN).get();
  }

  static async setAccessToken(token) {
    store.dispatch(AppActions.setBioToken(token));
    return await SysStorage(CONST.STORAGE.ACCESS_TOKEN).set(token);
  }

  static async getRefreshToken() {
    return await SysStorage(CONST.STORAGE.REFRESH_TOKEN).get();
  }

  static async setRefreshToken(token) {
    return await SysStorage(CONST.STORAGE.REFRESH_TOKEN).set(token);
  }

  static async clearAllTokens() {
    await SysStorage(CONST.STORAGE.ACCESS_TOKEN).set(null);
    await SysStorage(CONST.STORAGE.REFRESH_TOKEN).set(null);
    await SysStorage(CONST.STORAGE.BIO_ENABLE).set(false);
  }
}

export default StorageService;
