import AsyncStorage from '@react-native-async-storage/async-storage';
import CryptoJS from 'crypto-js';

const SECRET = {
  KEY: 'CP@23@Secret',
  IV: 'IV2205CP2023',
};

const SysStorage = name => {
  return {
    async get() {
      try {
        const value = await AsyncStorage.getItem(name);
        if (value) {
          const decrypted = decrypt(value);
          if (decrypted === 'true' || decrypted === 'false') {
            return decrypted === 'true';
          }
          return decrypted;
        }
        return null;
      } catch (error) {
        console.error('Failed to get or decrypt the value:', error);
        return null;
      }
    },
    async set(value) {
      try {
        const valueToStore = typeof value === 'boolean' ? String(value) : value;
        const encrypted = encrypt(valueToStore);
        await AsyncStorage.setItem(name, encrypted);
        return true;
      } catch (error) {
        console.error('Failed to set or encrypt the value:', error);
        return false;
      }
    },
    async remove() {
      try {
        await AsyncStorage.removeItem(name);
        return true;
      } catch (error) {
        console.error('Failed to remove the item:', error);
        return false;
      }
    },
  };
};

export default SysStorage;

export const SysClearStorage = async () => {
  try {
    await AsyncStorage.clear();
    return true;
  } catch (error) {
    console.error('Failed to clear AsyncStorage:', error);
    return false;
  }
};
const decrypt = value => {
  try {
    const key = CryptoJS.enc.Utf8.parse(SECRET.KEY);
    const iv = CryptoJS.enc.Utf8.parse(SECRET.IV);
    const decrypted = CryptoJS.AES.decrypt(value, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('Decryption failed:', error);
    return '';
  }
};

const encrypt = sourceString => {
  try {
    const key = CryptoJS.enc.Utf8.parse(SECRET.KEY);
    const iv = CryptoJS.enc.Utf8.parse(SECRET.IV);
    const encrypted = CryptoJS.AES.encrypt(sourceString, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    return encrypted.toString();
  } catch (error) {
    console.error('Encryption failed:', {sourceString}, error);
    return '';
  }
};
