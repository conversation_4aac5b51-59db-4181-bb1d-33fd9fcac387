import {API_URL} from '@env';
import axios from 'axios';
import Toast from 'react-native-toast-message';
import CONST from './const.js';
import {resetNavigation} from './navigation.js';
import StorageService from './storageService';

let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

let AxiosClient = axios.create({
  baseURL: API_URL || CONST.REQUEST.API_ADDRESS,
  timeout: CONST.REQUEST.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

AxiosClient.interceptors.request.use(
  config => {
    const excludedUrls = [
      'settings',
      'login/vietlott',
      'login/dms',
      'register',
      'check-first-login',
      'login/required-otp',
      'login/login-by-otp',
    ];

    if (excludedUrls.some(url => config.url.includes(url))) {
      config.headers.Authorization = '';
    }
    // console.log(
    //   `Request: ${config.method.toUpperCase()} ${config.baseURL}${config.url}`,
    // );
    // console.log('Request Data:', config.data || config.params);
    return config;
  },
  error => {
    console.log({error});
    return Promise.reject(error);
  },
);

AxiosClient.interceptors.response.use(
  response => {
    // console.log(`Response from ${response.config.url}`);
    // console.log('Response Status:', response.status);
    // console.log('Response Data:', response.data);
    return response.data || response;
  },
  async error => {
    const originalRequest = error.config;
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      error?.response?.config?.url !== 'me/delete-account'
    ) {
      return handle401Error(error, originalRequest);
    }
    return handleOtherErrors(error);
  },
);

async function handle401Error(error, originalRequest) {
  if (isRefreshing) {
    return queueFailedRequest(originalRequest);
  }
  return tryRefreshToken(originalRequest, error);
}

async function queueFailedRequest(originalRequest) {
  return new Promise((resolve, reject) => {
    failedQueue.push({resolve, reject});
  })
    .then(token => {
      originalRequest.headers.Authorization = 'Bearer ' + token;
      return AxiosClient(originalRequest);
    })
    .catch(Promise.reject);
}

async function tryRefreshToken(originalRequest, error) {
  originalRequest._retry = true;
  isRefreshing = true;
  try {
    const refreshToken = await StorageService.getRefreshToken();
    const deviceToken = await StorageService.getDeviceToken();
    const refreshResponse = await axios.post(
      `${API_URL}bio/refresh`,
      {refresh_token: refreshToken, device_token: deviceToken},
      {
        headers: {
          Authorization: `Bearer ${await StorageService.getAccessToken()}`,
        },
      },
    );
    const {token, refresh_token} = refreshResponse.data;
    await StorageService.setAccessToken(token);
    await StorageService.setRefreshToken(refresh_token);
    setConfigAxios(token);
    processQueue(null, token);
    isRefreshing = false;
    originalRequest.headers.Authorization = 'Bearer ' + token;
    return AxiosClient(originalRequest);
  } catch (refreshError) {
    console.log(refreshError);
    processQueue(refreshError, null);
    isRefreshing = false;
    await StorageService.clearAllTokens();
    resetNavigation('LoginScreen');
    Toast.show({
      type: 'error',
      text1: 'Thông báo',
      text2:
        // error.response.data?.message ||
        'Phiên đăng nhập đã hết hạn, vui lòng đăng nhập lại',
    });
    return Promise.reject(refreshError);
  }
}

function handleOtherErrors(error) {
  if (error?.response?.data?.message) {
    Toast.show({
      type: 'error',
      text1: 'Thông báo',
      text2: error.response.data.message,
    });
  } else {
    console.log({error});
  }
  return Promise.reject(error);
}

export function setConfigAxios(accessToken) {
  AxiosClient.defaults.headers.common = {
    'Content-Type': 'application/json',
    Authorization: '',
  };
  AxiosClient.defaults.timeout = CONST.REQUEST.TIMEOUT;
  if (accessToken) {
    AxiosClient.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
  }
}

const post = (url, data, config) => AxiosClient.post(url, data, config);

const get = (url, params = {}) => AxiosClient.get(url, {params});

const put = (url, data, config = {}) => AxiosClient.put(url, data, config);

const patch = (url, data, config = {}) => AxiosClient.patch(url, data, config);

const del = (url, config = {}) => AxiosClient.delete(url, config);

const SysFetch = {
  post,
  get,
  put,
  patch,
  delete: del,
};

export default SysFetch;
