import {configureStore} from '@reduxjs/toolkit';
import createSagaMiddleware from 'redux-saga';
import {reducers} from './reducer';
import rootSaga from './saga';
import {setupListeners} from '@reduxjs/toolkit/query';

const sagaMiddleware = createSagaMiddleware();

const store = configureStore({
  reducer: reducers,
  middleware: getDefaultMiddleware => {
    const middlewares = getDefaultMiddleware({
      serializableCheck: false,
    }).concat(sagaMiddleware);
    return middlewares;
  },
});

sagaMiddleware.run(rootSaga);

setupListeners(store.dispatch);

export {store};
