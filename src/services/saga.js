import {all, call} from 'redux-saga/effects';
import LoginSaga from '../screen/login/services/login.saga';
import AccountSaga from '../screen/account/services/account.saga';
import PosSaga from '../screen/sale-location/services/pos.saga';
import AppSaga from '../app.saga';
import SurveySaga from '../screen/survey/service/survey.saga';
import ReportSaga from '../screen/report/services/report.saga';
import InventorySaga from '../screen/device-list/services/inventory.saga';

import SupportSaga from '../screen/message-support/services/support.saga';
import OwnerSaga from '../screen/owner/services/saga';
import branchManagerSaga from '../screen/branch-manager-home/services/saga';
import NotificationSaga from '../screen/notification/services/notification.saga';

function* rootSaga() {
  yield all([
    call(AppSaga),
    call(LoginSaga),
    call(AccountSaga),
    call(PosSaga),
    call(SurveySaga),
    call(ReportSaga),
    call(InventorySaga),
    call(SupportSaga),
    call(OwnerSaga),
    call(branchManagerSaga),
    call(NotificationSaga),
  ]);
}

export default rootSaga;
