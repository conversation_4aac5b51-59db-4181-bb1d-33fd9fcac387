import {
  CommonActions,
  createNavigationContainerRef,
} from '@react-navigation/native';
import PropTypes from 'prop-types';

export const navigationRef = createNavigationContainerRef();

export function navigate(name, params) {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name, params);
  }
}

export const resetNavigation = routeName => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{name: routeName}],
      }),
    );
  }
};

const navigateToScreen = (screen, params) => {
  switch (screen) {
    case 'NotificationList':
      navigationRef.current?.navigate('NotificationScreen', {
        initialTab: 'Thông báo',
      });
      break;
    default:
      navigationRef.current?.navigate(screen || 'HomeScreen', params);
  }
};

navigateToScreen.propTypes = {
  screen: PropTypes.string.isRequired,
  params: PropTypes.object.isRequired,
};

export default navigateToScreen;
