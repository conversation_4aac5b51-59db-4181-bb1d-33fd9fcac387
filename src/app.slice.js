import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  isLoading: false,
  isDev: false,
  isEnableBio: false,
  isConnected: true,
  bioType: 'TouchID',
  bioToken: '',
  refreshToken: '',
  device_token: '',
  current_location: {
    location: {
      latitude: 0,
      longitude: 0,
      speed: 0,
    },
    extras: {
      maxCn0: 0,
      meanCn0: 0,
      satellites: 0,
    },
  },
  settings: {
    image_rules: {
      mimetypes: 'jpeg,jpg,png,webp',
      max: 1204,
      max_width: 1920,
      max_height: 1080,
      isWebp: false,
      quality: 60,
    },
    barcode_prefix: {
      HCM: '01',
      CTO: '02',
      VT: '03',
      KH: '04',
      HP: '05',
      NA: '06',
      SMS: '07',
      TOHO: '08',
    },
  },
};

const AppSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    getSettings: () => {},
    setSettings: (state, {payload}) => {
      state.settings = payload;
    },
    setLocation: (state, {payload}) => {
      if (payload.coords) {
        state.current_location.location = payload.coords;
      }
      if (payload.extras) {
        state.current_location.extras = payload.extras;
      }
    },
    setDeviceToken: (state, {payload}) => {
      state.device_token = payload;
    },
    setLoading: (state, {payload}) => {
      state.isLoading = payload;
    },
    setDev: (state, {payload}) => {
      state.isDev = payload;
    },
    setConnected: (state, {payload}) => {
      state.isConnected = payload;
    },
    setBioType: (state, {payload}) => {
      state.bioType = payload;
    },
    setIsEnableBio: (state, {payload}) => {
      state.isEnableBio = payload;
    },
    setBioToken: (state, {payload}) => {
      state.bioToken = payload;
    },
    setRefreshToken: (state, {payload}) => {
      state.refreshToken = payload;
    },
    clear: state => {
      state.refreshToken = '';
      state.bioToken = '';
    },
    fetch: (state, {payload}) => {},
    fetchChartMonth: (state, {payload}) => {},
    fetchChartDay: (state, {payload}) => {},
    fetchChartHour: (state, {payload}) => {},
    fetchChartQuarter: (state, {payload}) => {},
    fetchPost: (state, {payload}) => {},
    getChart: (state, {payload}) => {},
  },
});

const AppReducer = AppSlice.reducer;
export default AppReducer;

export const AppSelectors = {
  isLoading: state => state.app.isLoading,
  isDev: state => state.app.isDev,
  bioType: state => state.app.bioType,
  isEnableBio: state => state.app.isEnableBio,
  bioToken: state => state.app.bioToken,
  refreshToken: state => state.app.refreshToken,
  settings: state => state.app.settings,
  device_token: state => state.app.device_token,
  current_location: state => state.app.current_location,
  isConnected: state => state.app.isConnected,
};
export const AppActions = AppSlice.actions;
