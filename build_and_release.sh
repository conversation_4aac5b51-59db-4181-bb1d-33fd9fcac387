#!/bin/bash
set -e
# <PERSON><PERSON><PERSON> nghĩa thư mục gốc của dự án
PROJECT_DIR=$(pwd)


if [ -f "$PROJECT_DIR/.env" ]; then
    echo "Loading environment variables from .env..."
    export $(grep -v '^#' "$PROJECT_DIR/.env" | xargs)
else
    echo ".env file not found in $PROJECT_DIR. Exiting."
    exit 1
fi

# Lấy thời gian hiện tại để làm VERSION_CODE
VERSION_CODE=$(date +"%y%m%d%H")
VERSION_NAME="1.3.1"

echo "Starting iOS build and release..."
echo "VERSION_CODE: $VERSION_CODE"
echo "VERSION_NAME: $VERSION_NAME"
echo "Building for API_URL: $API_URL"

export VERSION_CODE
export VERSION_NAME

echo "Starting Android build and release..."
# <PERSON><PERSON><PERSON><PERSON> đến thư mục Android và chạy Fastlane
cd "$PROJECT_DIR/android"
bundle exec fastlane increment_version_and_build

echo "Starting ios build and release..."
# <PERSON><PERSON><PERSON><PERSON> đến thư mục iOS và chạy Fastlane
cd "$PROJECT_DIR/ios"
bundle exec fastlane increment_version_and_build

echo "Build and release process completed!"
