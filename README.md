#

keytool -genkey -v -keystore android\app\dmskey.keystore -alias dms -keyalg RSA -keysize 2048 -validity 10000


```
NO_FLIPPER=1 pod install
```


# ios

```angular2html
rm -rf ios/Podfile.lock
rm -rf ~/Library/Caches/CocoaPods
rm -rf ~/Library/Developer/Xcode/DerivedData/*
yarn install
cd ios
pod install
cd ..

```

```angular2html
rm -rf node_modules/
rm -rf yarn.lock
rm -rf ios/Pods/
rm -rf ios/Podfile.lock
rm -rf ~/Library/Caches/CocoaPods
rm -rf ~/Library/Developer/Xcode/DerivedData/*
yarn install
cd ios
pod install
cd ../
yarn ios

```

nếu cài lỗi chạy
rbenv global 2.7.0
gem install cocoapods

```
cd ios    
bundle install
pod install
```
rbenv global 2.7.6 -> build cicd
và build ios bằng xcode bình thường xem có chạy được không

# android
## config
```angular2html
/android/app/build.gradle line:83

xem thông tin để tạo keystore
```



```angular2html
cd android/app

tạo key
 keytool -genkeypair -v -keystore dmskey.keystore -alias dms -keyalg RSA -keysize 2048 -validity 10000

test key

keytool -list -keystore dmskey.keystore -alias dms -storepass Admin@123

tạo sha 1

keytool -list -v -keystore dmskey.keystore -alias dms -storepass Admin@123 -keypass Admin@123


```

```angular2html
Tạo index apk nếu cần

react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res
```


```
cd android
adb uninstall vn.vietlott.dms
./gradlew clean
./gradlew assembleRelease
```
```
./gradlew clean
./gradlew build --warning-mode all
```
# build
## development
### bundle
```
DBIZ
```
### ID
```
dms202401
```

API_URL=https://dms.s2-dev.mkit.vn/api/v1/

## production
### bundle
```
VIETLOT
```
### IDf
```
vietlottdms
```
API_URL=https://dms.vietlott.vn/api/v1/

# deployment

## 1 dev 2 bản
## 1 prod 2 bản


# icon
```angular2html
yarn react-native generate-bootsplash assets/1024.png --background=ec2027 --logo-width=200 --assets-output=assets --flavor=main --platforms=android,ios
```

## ios
```angular2html
https://www.appicon.co/#image-sets
```
## android
```angular2html
https://easyappicon.com/
```


# issue
```angular2html
adb uninstall vn.vietlott.dms
cd android && ./gradlew assembleDebug

```
# gỡ lỗi glog
```angular2html
git ls-files -z | xargs -0 rm
git checkout .
Updated 629 paths from the index

```


# nếu lỗi sh build do version ruby 2.7.0 thì thư mục gốc
```
 rm -rf vendor/bundle
bundle install


```

## cách check log bằng cap

- cắm dây cap và dùng lệnh
```angular2html
adb shell ps -A | grep <package_name>
  
  adb shell ps -A | grep  vn.vietlott.dms
```

```angular2html

kết quả
$ adb shell ps -A | grep  vn.vietlott.dms
u0_a565      12153   542 37767956 379660 0                  0 S vn.vietlott.dms
```
- dùng lệnh để check
````angular2html
adb logcat --pid=12153
````

hoặc 
```angular2html
adb logcat --pid=12153 | grep "ReactNativeJS"
```
# Hoặc chụp trực tiếp về máy tính
```angular2html
adb exec-out screencap -p > screenshot.png
```
