<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Vietlott DMS</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.3.1</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>25062416</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>Sử dụng thư viện upload hình ảnh cho mục đích cập nhật ảnh đại diện của người dùng</string>
	<key>NSCameraUsageDescription</key>
	<string>Sử dụng camera để chụp ảnh cho mục đích cập nhật ảnh đại diện và checkin của người dùng</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Sử dụng sinh trắc học để đăng nhập hệ thống</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Sử dụng vị trí cho mục đích thực hiện tính năng checkin của người dùng</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict/>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Sử dụng vị trí cho mục đích thực hiện tính năng checkin của người dùng</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Sử dụng thư viện upload hình ảnh cho mục đích cập nhật ảnh đại diện của người dùng</string>
	<key>UIAppFonts</key>
	<array>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Roboto-Black.ttf</string>
		<string>Roboto-BlackItalic.ttf</string>
		<string>Roboto-Bold.ttf</string>
		<string>Roboto-BoldItalic.ttf</string>
		<string>Roboto-Italic.ttf</string>
		<string>Roboto-Light.ttf</string>
		<string>Roboto-LightItalic.ttf</string>
		<string>Roboto-Medium.ttf</string>
		<string>Roboto-MediumItalic.ttf</string>
		<string>Roboto-Regular.ttf</string>
		<string>Roboto-Thin.ttf</string>
		<string>Roboto-ThinItalic.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>BootSplash</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
