# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

before_all do
  require 'dotenv'
  Dotenv.load('.env')
  puts "Debug: Checking environment variable loading."
  puts "FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: #{ENV['FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD']}"
end
# Hiện đang chưa load dược env nên chưa đẩy được testflight


platform :ios do
  desc "Generate new localized screenshots"
  lane :screenshots do
    capture_screenshots(workspace: "VietlottDMS.xcworkspace", scheme: "VietlottDMS")
    upload_to_app_store(skip_binary_upload: true, skip_metadata: true)
  end

  desc "Increment version number and build the app"
  lane :increment_version_and_build do
    increment_version_number(
      version_number: ENV['VERSION_NAME'] || '1.3.0'
    )
    increment_build_number(
      build_number: ENV['VERSION_CODE']
    )

    build_app(
      workspace: "VietlottDMS.xcworkspace",
      scheme: "VietlottDMS",
      export_method: "app-store",
      export_xcargs: "-allowProvisioningUpdates"
    )

#     upload_to_testflight # Nếu bạn muốn đẩy lên TestFlight, bỏ dòng này nếu không cần
  end
end
