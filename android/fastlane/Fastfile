
default_platform(:android)

platform :android do
  # Lane để chạy tất cả các tests
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  # Lane để xây dựng bản release
  desc "Builds the release APK"
  lane :build_release do
    gradle(task: "clean assembleRelease")
  end

  # Lane để gửi bản beta mới đến Crashlytics Beta
  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    build_release
    begin
      crashlytics
    rescue => ex
      UI.error("Đã xảy ra lỗi khi đẩy lên Crashlytics: #{ex.message}")
      # G<PERSON>i thông báo lỗi qua Slack, email, v.v.
    end
  end

  # Lane để triển khai một phiên bản mới lên Google Play
  desc "Deploy a new version to the Google Play"
  lane :deploy do
    UI.confirm('Bạn có chắc chắn muốn đẩy ứng dụng lên Google Play không?')
    build_release
    upload_to_play_store
  end

  # Lane để tăng số phiên bản và xây dựng ứng dụng
 desc "Increment version number, build APK and App Bundle"
 lane :increment_version_and_build do
   # Build APK
   gradle(
     task: "clean assembleRelease", # Tạo APK
     properties: {
       "VERSION_CODE" => ENV['VERSION_CODE'], # Truyền version code từ biến môi trường
       "VERSION_NAME" => ENV['VERSION_NAME']  # Truyền version name từ biến môi trường
     }
   )

   # Build App Bundle (AAB)
   gradle(
     task: "bundleRelease", # Tạo App Bundle
     properties: {
       "VERSION_CODE" => ENV['VERSION_CODE'], # Truyền version code từ biến môi trường
       "VERSION_NAME" => ENV['VERSION_NAME']  # Truyền version name từ biến môi trường
     }
   )
   # Thông báo hoàn tất
   UI.success("Build completed! APK and AAB have been generated.")
 end

end
